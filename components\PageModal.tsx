import React, { RefObject } from "react";
import { useFilterState } from "@/hooks/useFilterState";
import { PageView, ExitLink } from "@/lib/types";

type PageModalProps = {
  pageModalRef: RefObject<HTMLDivElement | null>;
  topPages: PageView[];
  entryPages: PageView[];
  exitPages: PageView[];
  exitLinks: ExitLink[];
  maxPageVisitors: number;
  currentPageView: "all" | "entry" | "exit" | "exit-links";
  setShowPageModal: (show: boolean) => void;
};

function PageModal({
  pageModalRef,
  topPages,
  entryPages,
  exitPages,
  exitLinks,
  maxPageVisitors,
  currentPageView,
  setShowPageModal,
}: PageModalProps) {
  const { handleFilterClick, activeFilters } = useFilterState();

  const handlePageFilterClick = (
    page: { path: string; visitors: number }
  ) => {
    // Determine the filter type based on the current view
    const filterType = currentPageView === "entry" ? "entry-page" : "page";
    // First apply the filter
    handleFilterClick(filterType, page.path, page.path);
    // Then close the modal
    setTimeout(() => setShowPageModal(false), 100);
  };

  // Handle exit link click
  const handleExitLinkClick = (url: string) => {
    // Add filter for external link clicks
    handleFilterClick("page", `external_link:${url}`, `Clicks to ${getFaviconDomain(url)}`);
    // Close the modal
    setTimeout(() => setShowPageModal(false), 100);
  };

  // Handle opening external link
  const handleOpenExternalLink = (e: React.MouseEvent, url: string) => {
    e.stopPropagation(); // Prevent triggering the filter
    window.open(url, "_blank");
  };

  // Get domain for favicon
  const getFaviconDomain = (url: string) => {
    try {
      // Make sure URL has a protocol
      const fullUrl = url.startsWith('http') ? url : `https://${url}`;
      // Extract just the hostname (domain) part
      const hostname = new URL(fullUrl).hostname;
      // Remove www. prefix if present
      return hostname.replace(/^www\./, '');
    } catch (e) {
      // If URL parsing fails, try to extract domain manually
      const domainMatch = url.match(/^(?:https?:\/\/)?(?:www\.)?([^\/\?]+)/i);
      return domainMatch ? domainMatch[1] : url;
    }
  };

  // Get title for the modal based on current view
  const getModalTitle = () => {
    switch (currentPageView) {
      case "entry":
        return `Entry Pages (${entryPages?.length || 0})`;
      case "exit-links":
        return `External Clicks (${exitLinks?.length || 0})`;
      case "exit":
        // For backward compatibility, but not shown in the UI
        return `Exit Pages (${exitPages?.length || 0})`;
      default:
        return `Top Pages (${topPages?.length || 0})`;
    }
  };

  // Determine which pages to show based on the selected view
  const pagesToShow = React.useMemo(() => {
    let result;
    switch (currentPageView) {
      case "entry":
        result = entryPages || [];
        break;
      case "exit":
        result = exitPages || [];
        break;
      case "exit-links":
        // Map exit links to the same shape as page views for rendering
        result = exitLinks?.map(link => ({ 
          path: link.url, 
          visitors: link.visitors 
        })) || [];
        break;
      default:
        result = topPages || [];
    }
    return result;
  }, [currentPageView, topPages, entryPages, exitPages, exitLinks]);

  return (
    <div className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4 overflow-hidden">
      <div
        ref={pageModalRef}
        className="bg-[#2A2A2A] rounded-lg w-full max-w-2xl max-h-[80vh] overflow-hidden flex flex-col"
        onClick={(e) => e.stopPropagation()}
      >
        <div className="flex items-center justify-between p-4 border-b border-gray-700">
          <h2 className="text-lg font-semibold text-white">
            {getModalTitle()}
          </h2>
          <button
            onClick={() => setShowPageModal(false)}
            className="text-gray-400 hover:text-white"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-5 w-5"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fillRule="evenodd"
                d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                clipRule="evenodd"
              />
            </svg>
          </button>
        </div>
        <div className="overflow-y-auto p-4 flex-grow">
          <div className="space-y-1">
            {pagesToShow && pagesToShow.length > 0 ? (
              pagesToShow.map((page, i) => (
                <div
                  key={i}
                  className={`flex items-center justify-between p-1 sm:p-2 rounded text-xs sm:text-sm relative overflow-hidden group cursor-pointer ${
                    (currentPageView === "exit-links" && activeFilters.some(
                      (filter) => filter.type === "page" && filter.value === `external_link:${page.path}`
                    )) || (currentPageView !== "exit-links" && activeFilters.some(
                      (filter) => 
                        (currentPageView === "entry" ? 
                          filter.type === "entry-page" : 
                          filter.type === "page") && 
                        filter.value === page.path
                    ))
                      ? "bg-blue-500/20"
                      : ""
                  }`}
                  onClick={() => 
                    currentPageView === "exit-links" 
                      ? handleExitLinkClick(page.path)
                      : handlePageFilterClick(page)
                  }
                >
                  <div
                    className="absolute inset-0 bg-blue-500/40 transition-transform origin-left group-hover:bg-blue-500/20"
                    style={{
                      transform: `scaleX(${page.visitors / maxPageVisitors})`,
                    }}
                  />
                  <span className="text-white truncate max-w-[70%] relative flex items-center gap-2">
                    {currentPageView === "exit-links" ? (
                      <>
                        <img
                          src={`https://www.google.com/s2/favicons?domain=${getFaviconDomain(page.path)}&sz=32`}
                          alt=""
                          className="w-4 h-4 flex-shrink-0"
                          onError={(e) => {
                            const target = e.target as HTMLImageElement;
                            target.onerror = null; // Prevent infinite loop
                            
                            // Try DuckDuckGo as first fallback
                            const domain = getFaviconDomain(page.path);
                            target.src = `https://icons.duckduckgo.com/ip3/${domain}.ico`;
                            
                            // Add another fallback in case DuckDuckGo fails
                            target.onerror = () => {
                              target.onerror = null;
                              target.src = "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='16' height='16'%3E%3Cpath fill='none' stroke='%234B5563' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14'%3E%3C/path%3E%3C/svg%3E";
                            };
                          }}
                        />
                        <span className="truncate">{page.path}</span>
                      </>
                    ) : (
                      page.path
                    )}
                  </span>
                  <span className="text-white ml-2 flex-shrink-0 relative flex items-center gap-2">
                    {page.visitors}
                    {currentPageView === "exit-links" && (
                      <button
                        onClick={(e) => handleOpenExternalLink(e, page.path)}
                        className="ml-2 p-1 hover:bg-blue-500/20 rounded"
                        title="Open link"
                      >
                        <svg
                          className="w-4 h-4"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"
                          />
                        </svg>
                      </button>
                    )}
                  </span>
                </div>
              ))
            ) : (
              <div className="text-gray-400 text-center py-4">
                No pages found
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

export default PageModal; 