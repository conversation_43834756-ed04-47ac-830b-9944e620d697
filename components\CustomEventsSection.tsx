import { CustomEvent } from "@/hooks/useCustomEvents";
import Link from "next/link";
import React, { Dispatch, SetStateAction } from "react";
import { toast } from "react-hot-toast";

type CustomEventsSectionProps = {
  isLoadingEvents: boolean;
  customEvents: CustomEvent[];
  handleShowJourneyList: (eventName: string) => Promise<void>;
  setShowJourneyListModal: Dispatch<SetStateAction<boolean>>;
  handleJourneyClick: (
    eventName: string,
    visitorId: string,
    timestamp: string
  ) => void;
};

function CustomEventsSection({
  isLoadingEvents,
  customEvents,
  handleShowJourneyList,
  setShowJourneyListModal,
  handleJourneyClick,
}: CustomEventsSectionProps) {
  function handleCopy(text: string) {
    navigator.clipboard.writeText(text);
    toast.success("Copied to clipboard!", {
      style: {
        background: "#333",
        color: "#fff",
      },
      iconTheme: {
        primary: "#3b82f6",
        secondary: "#333",
      },
    });
  }

  const codeSnippet =
    'window?.versatailor("pressed_get_started_hero", { description: "User pressed the Get Started Button in the Hero section." })';

  return (
    <div className="bg-[#2A2A2A] p-4 sm:p-6 rounded-lg border border-gray-700/50 mt-8 w-full max-w-full overflow-hidden">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-3">
          <div>
            <h2 className="text-xl font-semibold text-white">Custom Events</h2>
            <p className="text-sm text-gray-400">
              Track and analyze user interactions
            </p>
          </div>
        </div>
      </div>

      {isLoadingEvents ? (
        <div className="h-64 flex items-center justify-center">
          <div className="w-8 h-8 border-4 border-blue-500/30 border-t-blue-500 rounded-full animate-spin"></div>
        </div>
      ) : customEvents && customEvents.length > 0 ? (
        <div className="overflow-x-auto -mx-4 sm:-mx-6 px-4 sm:px-6">
          <div className="w-full inline-block align-middle">
            <table className="w-full min-w-[480px]">
              <thead>
                <tr className="text-left text-gray-400 text-sm border-b border-gray-700/50">
                  <th className="pb-4 font-medium w-[40%] sm:w-[40%]">
                    Event Name
                  </th>
                  <th className="pb-4 font-medium text-center w-[15%] sm:w-[15%]">
                    Count
                  </th>
                  <th className="pb-4 font-medium text-center w-[15%] sm:w-[15%]">
                    Unique Users
                  </th>
                  <th className="pb-4 font-medium text-right w-[30%] sm:w-[30%]">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-700/30">
                {customEvents.map((event, index) => (
                  <tr
                    key={index}
                    className="group hover:bg-gray-800/40 transition-colors duration-200"
                  >
                    <td className="py-4 text-white font-medium truncate pr-2 sm:text-sm text-xs">
                      {event.name}
                    </td>
                    <td className="py-4 text-center">
                      <span className="text-blue-400 font-medium bg-blue-500/10 px-2 sm:px-3 py-1 rounded-full text-xs sm:text-sm inline-block">
                        {event.name === "signup"
                          ? event.uniqueUsers
                          : event.count}
                      </span>
                    </td>
                    <td className="py-4 text-center">
                      <span className="text-gray-400 bg-gray-700/30 px-2 sm:px-3 py-1 rounded-full text-xs sm:text-sm inline-block">
                        {event.uniqueUsers}
                      </span>
                    </td>
                    <td className="py-4 text-right">
                      {event.count > 1 ? (
                        <button
                          onClick={() => {
                            handleShowJourneyList(event.name);
                            setShowJourneyListModal(true);
                          }}
                          className="inline-flex items-center justify-center gap-1 sm:gap-2 px-2 sm:px-4 py-1 sm:py-2 rounded-lg bg-blue-500/10 text-blue-400 hover:bg-blue-500/20 transition-colors duration-200 text-xs sm:text-sm font-medium group-hover:shadow-lg whitespace-nowrap"
                        >
                          <svg
                            className="w-3 h-3 sm:w-4 sm:h-4"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              strokeLinecap="round" 
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M4 6h16M4 10h16M4 14h16M4 18h16"
                            />
                          </svg>
                          <span className="whitespace-nowrap">
                            View All (
                            {event.name === "signup"
                              ? event.uniqueUsers
                              : event.count}
                            )
                          </span>
                        </button>
                      ) : (
                        <button
                          onClick={() =>
                            handleJourneyClick(
                              event.name,
                              event.visitorId,
                              event.timestamp
                            )
                          }
                          className="inline-flex items-center justify-center gap-1 sm:gap-2 px-2 sm:px-4 py-1 sm:py-2 rounded-lg bg-blue-500/10 text-blue-400 hover:bg-blue-500/20 transition-colors duration-200 text-xs sm:text-sm font-medium group-hover:shadow-lg"
                        >
                          <svg
                            className="w-3 h-3 sm:w-4 sm:h-4"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7"
                            />
                          </svg>
                          <span>Journey</span>
                        </button>
                      )}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      ) : (
        <div className="h-auto flex flex-col items-center justify-center text-gray-400 py-12">
          <div className="w-16 h-16 rounded-2xl bg-gray-800/50 flex items-center justify-center mb-6">
            <svg
              className="w-8 h-8 text-gray-600"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={1.5}
                d="M13 10V3L4 14h7v7l9-11h-7z"
              />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-white mb-2">No Events Yet</h3>
          <p className="text-gray-400 text-center mb-8">
            Start tracking user interactions with{" "}
            <Link
              href={"/docs/track-events"}
              target="_blank"
              className="text-blue-400 hover:underline"
            >
              custom events
            </Link>{" "}
          </p>
        </div>
      )}
    </div>
  );
}

export default CustomEventsSection;
