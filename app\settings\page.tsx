import React from 'react'
import Link from 'next/link'
import { Settings, Shield, CreditCard, FileText, Key, AlertTriangle } from 'lucide-react'
import { SignOutButton } from '@clerk/nextjs'
const page = () => {
    return (
        <div className="w-full min-h-screen bg-customGray text-gray-100">
            {/* Back to Sites Link */}
            <div className="w-full border-b border-gray-800">
                <div className="max-w-7xl mx-auto px-4 py-4">
                    <Link href="/dashboard" className="text-white hover:text-indigo-300 flex items-center gap-2">
                        ←
                    </Link>
                </div>
            </div>
            <div className="w-full max-w-7xl mx-auto px-4 py-8">
                <h1 className="text-2xl font-bold mb-8">Settings</h1>
                <div className="flex gap-8">
                    {/* Sidebar Navigation */}
                    <div className="w-64 flex-shrink-0">
                        <div className="space-y-1">
                            <nav className="space-y-1">
                                {[
                                    { name: 'Preferences', icon: Settings, current: true },
                                    // { name: 'Security', icon: Shield },
                                    // { name: 'Subscription', icon: CreditCard },
                                    // { name: 'Invoices', icon: FileText },
                                    // { name: 'API Keys', icon: Key },
                                    // { name: 'Danger Zone', icon: AlertTriangle },
                                ].map((item) => (
                                    <a
                                        key={item.name}
                                        href="#"
                                        className={`flex items-center px-4 py-2 text-sm rounded-md ${item.current
                                            ? 'bg-customGrayLight text-white'
                                            : 'text-gray-300 hover:bg-customGrayLight hover:text-white'
                                            }`}
                                    >
                                        <item.icon className="mr-3 h-5 w-5" />
                                        {item.name}
                                    </a>
                                ))}
                            </nav>
                        </div>
                    </div>

                    {/* Main Content */}
                    <div className="flex-1">
                        <div className="space-y-6">
                            {/* Theme Section */}
                        </div>
                    </div>
                </div>
                <div className="flex justify-end mt-2">
                    <SignOutButton />
                </div>
            </div>
        </div>
    )
}

export default page
