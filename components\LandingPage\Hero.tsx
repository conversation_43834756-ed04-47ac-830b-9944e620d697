"use client";
import Image from "next/image";
import React, { useState, useEffect, useRef } from "react";
import { Button } from "../ui/button";
import Link from "next/link";
import { useUser } from "@clerk/nextjs";
import { useRouter } from "next/navigation";
function Hero() {
  const [isVideoLoaded, setIsVideoLoaded] = useState(false);
  const [hasError, setHasError] = useState(false);
  const videoRef = useRef<HTMLVideoElement>(null);
  const errorRetryCount = useRef(0);

  useEffect(() => {
    const video = videoRef.current;
    if (video) {
      if (video.readyState >= 3) {
        setIsVideoLoaded(true);
      }

      const handleError = () => {
        console.error("Video playback error occurred");
        if (errorRetryCount.current < 3) {
          errorRetryCount.current += 1;
          console.log(
            `Attempting to recover playback - attempt ${errorRetryCount.current}`
          );

          // Try to recover playback
          video.load();
          video.play().catch((err) => {
            console.error("Recovery attempt failed:", err);
            if (errorRetryCount.current >= 3) {
              setHasError(true);
            }
          });
        } else {
          setHasError(true);
        }
      };

      // Add error event listener
      video.addEventListener("error", handleError);

      // Add stalled and waiting event handlers
      video.addEventListener("stalled", () => {
        video.play().catch(console.error);
      });

      video.addEventListener("waiting", () => {
        video.play().catch(console.error);
      });

      // Backup timeout to prevent infinite loading state
      const timeoutId = setTimeout(() => {
        if (!isVideoLoaded) {
          setHasError(true);
        }
      }, 10000); // 10 second timeout

      return () => {
        clearTimeout(timeoutId);
        video.removeEventListener("error", handleError);
      };
    }
  }, [isVideoLoaded]);
  const { isSignedIn } = useUser();
  const router = useRouter();
  useEffect(() => {
    if (isSignedIn) {
      if (process.env.NODE_ENV !== "development") {
        router.push("/dashboard");
      }
    }
  });

  function handleGoDashboard() {
    window?.versatailor("pressed_get_started_hero", {
      description: "User pressed the Get Started Button in the Hero section.",
    });
  }
  return (
    <section className="w-full pt-6 pb-12 md:pt-12 md:pb-24">
      <div className="container mx-auto px-4 md:px-6 flex flex-col items-center">
        <div className="flex flex-col items-center space-y-8 w-full">
          {/* Text Content Section */}
          <div className="w-full max-w-[800px] text-center">
            <div className="mx-auto">
              <h2 className="text-3xl font-bold tracking-tighter sm:text-5xl xl:text-6xl/none mb-4 text-zinc-100">
                Scale your business with{" "}
                <span className="text-[#204ec8]">data-backed</span> decisions
              </h2>
              <p className="text-gray-300 md:text-xl mx-auto">
                Get actionable insights from your website traffic. Make
                data-driven decisions to optimize your online business
                performance.
              </p>
              <Link href="/dashboard">
                <Button className="mt-4" onClick={handleGoDashboard}>
                  Get Started
                </Button>
              </Link>
            </div>
          </div>
          {/* Demo Video Section */}
          {/* <div className="w-full max-w-[900px] aspect-video rounded-xl overflow-hidden bg-gray-800 relative">
            {!isVideoLoaded && !hasError && (
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="w-8 h-8 border-4 border-primary border-t-transparent rounded-full animate-spin"></div>
              </div>
            )}
            {hasError ? (
              <div className="absolute inset-0 flex items-center justify-center">
                <Image
                  src="/placeholder.svg"
                  alt="Video placeholder"
                  fill
                  className="object-cover"
                  priority
                />
              </div>
            ) : (
              <video
                ref={videoRef}
                className={`w-full h-full object-cover transition-opacity duration-300 ${
                  isVideoLoaded ? "opacity-100" : "opacity-0"
                }`}
                autoPlay
                muted
                loop
                playsInline
                onLoadedData={handleVideoLoad}
                onCanPlay={handleVideoLoad}
                onError={() => {
                  console.error("Video failed to load");
                  setHasError(true);
                }}
              >
                <source src="https://i.imgur.com/Q69BT6A.mp4" type="video/mp4" />
                <source src="https://i.imgur.com/Q69BT6A.mp4" type="video/webm" />
              </video>
            )}
          </div> */}
          <div className="w-full max-w-[1200px] rounded-xl overflow-hidden bg-gradient-to-b from-zinc-800/30 to-zinc-900/30 p-2 backdrop-blur-sm border border-zinc-800/50">
            <Image
              src="/screenshots/hero_large.png"
              alt="Hero Large"
              width={1000}
              height={1000}
              className="w-full h-auto rounded-lg shadow-2xl transition-all duration-300 hover:scale-[1.02]"
              priority
              quality={95}
            />
          </div>
          {/* Waitlist Section */}
          {/* <div className="flex flex-col items-center space-y-4">
            <div className="flex flex-col items-center gap-2 min-[400px]:flex-row">
              <Waitlist
                appearance={{
                  elements: {
                    formButtonPrimary:
                      "bg-primary hover:bg-primary/90 text-primary-foreground shadow transition-colors",
                    formInputText:
                      "bg-background border border-input text-foreground shadow-sm",
                    card: "bg-zinc-900 shadow-lg border border-zinc-800",
                    header: "text-zinc-100",
                    headerTitle: "text-2xl font-bold text-zinc-100",
                    headerSubtitle: "text-zinc-400",
                    socialButtonsBlockButton:
                      "border border-zinc-800 bg-zinc-900 hover:bg-zinc-800 text-zinc-100",
                    dividerLine: "bg-zinc-800",
                    dividerText: "text-zinc-400",
                    formFieldLabel: "text-zinc-100",
                    footer: "text-zinc-400",
                    formFieldInput:
                      "bg-zinc-900 border border-zinc-800 text-zinc-100",
                  },
                  variables: {
                    colorPrimary: "#0ea5e9",
                    colorText: "#f4f4f5",
                    colorBackground: "#18181b",
                    colorInputBackground: "#27272a",
                    colorInputText: "#f4f4f5",
                    borderRadius: "0.5rem",
                  },
                }}
              />
            </div>
          </div> */}
        </div>
      </div>
    </section>
  );
}

export default Hero;
