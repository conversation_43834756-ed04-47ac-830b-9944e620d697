import { NextRequest, NextResponse } from "next/server";
import { auth } from "@clerk/nextjs/server";
import Website from "@/models/website";
import Event from "@/models/event";
import connect from "@/util/db";

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ websiteId: string }> }
) {
  try {
    await connect();
    const { userId } = await auth();

    if (!userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { websiteId } = await params;

    // Verify website ownership
    const website = await Website.findOne({
      id: websiteId,
      userId,
    });

    if (!website) {
      return NextResponse.json({ error: "Website not found" }, { status: 404 });
    }

    if (!website.revenueAttributionEnabled) {
      return NextResponse.json(
        { error: "Revenue attribution not enabled" },
        { status: 400 }
      );
    }

    // Check for recent webhook events (payment events in last 30 days)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const recentPaymentEvents = await Event.find({
      websiteId,
      type: "payment",
      timestamp: { $gte: thirtyDaysAgo },
    })
      .sort({ timestamp: -1 })
      .limit(1);

    const isActive = website.stripeWebhookId ? true : false;
    const lastReceived =
      recentPaymentEvents.length > 0
        ? recentPaymentEvents[0].timestamp
        : undefined;

    let error: string | undefined;
    if (!website.stripeWebhookId) {
      error = "No webhook configured";
    } else if (recentPaymentEvents.length === 0) {
      error = "No recent payment events received";
    }

    return NextResponse.json({
      isActive,
      lastReceived,
      error,
      webhookId: website.stripeWebhookId,
    });
  } catch (error) {
    console.error("Error fetching webhook status:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
