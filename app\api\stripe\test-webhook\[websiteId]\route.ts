import { NextRequest, NextResponse } from "next/server";
import { auth } from "@clerk/nextjs/server";
import Website from "@/models/website";
import connect from "@/util/db";
import Stripe from "stripe";
import { decryptApi<PERSON>ey } from "@/lib/stripe-utils";

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ websiteId: string }> }
) {
  try {
    await connect();
    const { userId } = await auth();

    if (!userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { websiteId } = await params;

    // Verify website ownership
    const website = await Website.findOne({
      id: websiteId,
      userId,
    }).select("+stripeApiKey");

    if (!website) {
      return NextResponse.json({ error: "Website not found" }, { status: 404 });
    }

    if (!website.revenueAttributionEnabled || !website.stripeApiKey) {
      return NextResponse.json(
        { error: "Revenue attribution not properly configured" },
        { status: 400 }
      );
    }

    if (!website.stripeWebhookId) {
      return NextResponse.json(
        { error: "No webhook configured" },
        { status: 400 }
      );
    }

    try {
      // Initialize Stripe with the website's decrypted API key
      const decryptedApiKey = decryptApiKey(website.stripeApiKey);
      const stripe = new Stripe(decryptedApiKey, {
        apiVersion: "2025-02-24.acacia",
      });

      // Try to retrieve the webhook endpoint to test connectivity
      const webhookEndpoint = await stripe.webhookEndpoints.retrieve(
        website.stripeWebhookId
      );

      if (!webhookEndpoint) {
        return NextResponse.json(
          { error: "Webhook endpoint not found in Stripe" },
          { status: 404 }
        );
      }

      // Check if webhook is enabled
      if (!webhookEndpoint.enabled_events.includes("checkout.session.completed") &&
          !webhookEndpoint.enabled_events.includes("invoice.payment_succeeded")) {
        return NextResponse.json(
          { error: "Webhook is not configured for payment events" },
          { status: 400 }
        );
      }

      return NextResponse.json({
        success: true,
        message: "Webhook connection test successful",
        webhookStatus: {
          id: webhookEndpoint.id,
          url: webhookEndpoint.url,
          enabled: webhookEndpoint.status === "enabled",
          events: webhookEndpoint.enabled_events,
        },
      });
    } catch (stripeError: any) {
      console.error("Stripe API error:", stripeError);
      return NextResponse.json(
        { 
          error: "Failed to connect to Stripe webhook",
          details: stripeError.message 
        },
        { status: 400 }
      );
    }
  } catch (error) {
    console.error("Error testing webhook:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
} 