"use client";
import { useEffect } from "react";
import { usePathname, useSearchParams } from "next/navigation";
import { v4 as uuidv4 } from "uuid";

const getSessionId = () => {
  if (typeof window === "undefined") return "";
  let sessionId = localStorage.getItem("analytics_session_id");
  if (!sessionId) {
    sessionId = uuidv4();
    localStorage.setItem("analytics_session_id", sessionId);
  }
  return sessionId;
};

// Store the initial referrer for the session
const getInitialReferrer = () => {
  if (typeof window === "undefined") return null;
  let initialReferrer = sessionStorage.getItem("initial_referrer");
  if (!initialReferrer && document.referrer) {
    initialReferrer = document.referrer;
    sessionStorage.setItem("initial_referrer", initialReferrer);
  }
  return initialReferrer;
};

// Check if the current navigation is part of an auth flow
const isAuthRedirect = (referrer: string) => {
  try {
    const url = new URL(referrer);
    return (
      url.hostname.includes("google.com") ||
      url.hostname.includes("accounts.google.com") ||
      url.pathname.includes("oauth") ||
      url.pathname.includes("signin") ||
      url.pathname.includes("login") ||
      url.pathname.includes("clerk.accounts.dev")
    );
  } catch (e) {
    return false;
  }
};

export function AnalyticsProvider({ children }: { children: React.ReactNode }) {
  const pathname = usePathname();
  const searchParams = useSearchParams();

  const isLocalhost =
    typeof window !== "undefined" &&
    (window.location.hostname === "localhost" ||
      window.location.hostname === "127.0.0.1");

  const isAnalyticsSession = pathname.includes("/analytics");

  // Store analytics access in localStorage if accessing analytics page
  useEffect(() => {
    if (isAnalyticsSession && typeof window !== "undefined") {
      localStorage.setItem("has_accessed_analytics", "true");
    }
  }, [isAnalyticsSession]);

  useEffect(() => {
    const trackPageView = async () => {
      const hasAccessedAnalytics =
        localStorage.getItem("has_accessed_analytics") === "true";

      const sessionId = getSessionId();

      try {
        // Get the current referrer
        const currentReferrer = document.referrer;

        // Get the initial referrer for this session
        const initialReferrer = getInitialReferrer();

        // Determine which referrer to use
        let referrerToUse = currentReferrer;
        let referrerUrl = null;

        // We'll still check if this is an auth redirect, but we won't change the referrer
        // The getTopReferrers function will handle appending to previous referrers
        const isAuthRedirectFlag = currentReferrer ? isAuthRedirect(currentReferrer) : false;

        // Extract domain from referrer
        if (referrerToUse) {
          try {
            const url = new URL(referrerToUse);
            referrerUrl = url.hostname;
          } catch (e) {
            console.error("Failed to parse referrer URL:", e);
          }
        }

        await fetch("/api/analytics", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            path: pathname,
            referrer: referrerToUse,
            referrerUrl,
            userAgent: navigator.userAgent,
            sessionId,
            isAuthRedirect: isAuthRedirectFlag,
          }),
        });
      } catch (error) {
        console.error("Failed to track analytics:", error);
      }
    };

    trackPageView();
  }, [pathname, searchParams]); // Track when path or search params change

  return <>{children}</>;
}
