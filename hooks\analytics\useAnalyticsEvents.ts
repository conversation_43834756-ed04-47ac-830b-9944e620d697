import { useState, useEffect, useCallback } from 'react';
import { Event, Period, CustomPeriod, Filter } from './types';
import { getDateRangeFromPeriod, getPreviousPeriodRange } from '@/lib/periodHelpers';
import { generateCacheKey } from './utils';
import { useAnalyticsCache } from './useAnalyticsCache';

export function useAnalyticsEvents(
  websiteId: string,
  selectedPeriod: Period,
  activeFilters: Filter[],
  customPeriod?: CustomPeriod
) {
  const [events, setEvents] = useState<Event[]>([]);
  const [prevPeriodEvents, setPrevPeriodEvents] = useState<Event[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  const {
    sessionCache,
    findBestCacheMatch,
    shouldInvalidateCache,
    setCacheEntry,
    API_CACHE_TIMEOUT,
  } = useAnalyticsCache();

  const getDateRange = useCallback(
    (period: Period, customPeriodParam?: CustomPeriod) => {
      return getDateRangeFromPeriod(period, customPeriod, customPeriodParam);
    },
    [customPeriod]
  );

  useEffect(() => {
    const controller = new AbortController();
    let isMounted = true;
    const requestsInFlight = new Set<string>();

    async function fetchEvents() {
      if (!websiteId) return;

      try {
        const cacheInvalidated = shouldInvalidateCache(selectedPeriod);

        const { from, to } = getDateRange(selectedPeriod, customPeriod);
        const { from: prevFrom, to: prevTo } = getPreviousPeriodRange(
          selectedPeriod,
          from,
          to
        );

        const customPeriodForCache = customPeriod ? {
          startDate: customPeriod.startDate.toISOString(),
          endDate: customPeriod.endDate.toISOString()
        } : undefined;

        const currentPeriodCacheKey = generateCacheKey(
          websiteId,
          selectedPeriod,
          from,
          to,
          customPeriodForCache,
          selectedPeriod
        );
        const prevPeriodCacheKey = generateCacheKey(
          websiteId,
          selectedPeriod,
          prevFrom,
          prevTo,
          customPeriodForCache,
          selectedPeriod
        );

        const now = Date.now();

        let currentPeriodData: Event[] = [];
        let prevPeriodData: Event[] = [];
        let fetchCurrentPeriod = true;
        let fetchPrevPeriod = true;

        if (!cacheInvalidated && selectedPeriod !== "today") {
          let cachedCurrent = sessionCache.events.get(currentPeriodCacheKey);

          if (!cachedCurrent) {
            cachedCurrent = findBestCacheMatch(currentPeriodCacheKey, selectedPeriod);
          }

          if (cachedCurrent && now - cachedCurrent.timestamp < API_CACHE_TIMEOUT) {
            currentPeriodData = cachedCurrent.data;
            fetchCurrentPeriod = false;
            if (isMounted) {
              setEvents(currentPeriodData);
            }
          }

          let cachedPrev = sessionCache.events.get(prevPeriodCacheKey);

          if (!cachedPrev) {
            cachedPrev = findBestCacheMatch(prevPeriodCacheKey, selectedPeriod);
          }

          if (cachedPrev && now - cachedPrev.timestamp < API_CACHE_TIMEOUT) {
            prevPeriodData = cachedPrev.data;
            fetchPrevPeriod = false;
            if (isMounted) {
              setPrevPeriodEvents(prevPeriodData);
            }
          }

          if (!fetchCurrentPeriod && !fetchPrevPeriod && isMounted) {
            setIsLoading(false);
            return;
          }
        }

        if ((fetchCurrentPeriod || fetchPrevPeriod) && isMounted) {
          setIsLoading(true);
        }

        const timestamp = selectedPeriod === "today" ? `&_t=${Date.now()}` : "";

        if (fetchCurrentPeriod || fetchPrevPeriod) {
          const fetchPromises: Promise<Response>[] = [];

          if (fetchCurrentPeriod) {
            const apiUrl = `/api/events?websiteId=${websiteId}&startDate=${from.toISOString()}&endDate=${to.toISOString()}${timestamp}`;

            if (!requestsInFlight.has(apiUrl)) {
              requestsInFlight.add(apiUrl);
              fetchPromises.push(
                fetch(apiUrl, { signal: controller.signal }).finally(() => {
                  requestsInFlight.delete(apiUrl);
                })
              );
            }
          }

          if (fetchPrevPeriod) {
            const apiUrl = `/api/events?websiteId=${websiteId}&startDate=${prevFrom.toISOString()}&endDate=${prevTo.toISOString()}`;

            if (!requestsInFlight.has(apiUrl)) {
              requestsInFlight.add(apiUrl);
              fetchPromises.push(
                fetch(apiUrl, { signal: controller.signal }).finally(() => {
                  requestsInFlight.delete(apiUrl);
                })
              );
            }
          }

          if (fetchPromises.length > 0) {
            const responses = await Promise.all(fetchPromises);
            const jsonData = await Promise.all(responses.map((r) => r.json()));

            if (isMounted) {
              let jsonIndex = 0;

              if (fetchCurrentPeriod) {
                currentPeriodData = jsonData[jsonIndex++];
                setCacheEntry(currentPeriodCacheKey, currentPeriodData, 'events');
                setEvents(currentPeriodData);
              }

              if (fetchPrevPeriod) {
                prevPeriodData = jsonData[jsonIndex];
                setCacheEntry(prevPeriodCacheKey, prevPeriodData, 'events');
                setPrevPeriodEvents(prevPeriodData);
              }
            }
          }
        }
      } catch (err) {
        if (!controller.signal.aborted) {
          console.error("Error fetching events:", err);
        }
      } finally {
        if (isMounted) {
          setIsLoading(false);
        }
      }
    }

    fetchEvents();

    return () => {
      isMounted = false;
      controller.abort();
    };
  }, [
    websiteId,
    selectedPeriod,
    customPeriod,
    getDateRange,
    sessionCache,
    findBestCacheMatch,
    shouldInvalidateCache,
    setCacheEntry,
    API_CACHE_TIMEOUT,
  ]);

  return {
    events,
    prevPeriodEvents,
    isLoading,
  };
} 