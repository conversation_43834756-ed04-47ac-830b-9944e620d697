import { useState, useRef } from "react";
import { <PERSON>a<PERSON>p<PERSON>, <PERSON>a<PERSON><PERSON><PERSON>, FaCheck, FaTimes, FaFileCsv, FaFileArchive, FaUnlink, FaExclamationTriangle } from "react-icons/fa";

interface PlausibleImportModalProps {
  websiteId: string;
  domain: string;
  onClose: () => void;
  modalRef: React.RefObject<HTMLDivElement | null>;
}

export default function PlausibleImportModal({
  websiteId,
  domain,
  onClose,
  modalRef,
}: PlausibleImportModalProps) {
  const [file, setFile] = useState<File | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [progress, setProgress] = useState<number>(0);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0] || null;
    setFile(selectedFile);
    setError(null);

    if (selectedFile && !selectedFile.name.endsWith('.zip')) {
      setError("Please upload a ZIP file exported from Plausible");
      setFile(null);
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  const handleImport = async () => {
    if (!file) {
      setError("Please select a ZIP file to import");
      return;
    }

    try {
      setIsLoading(true);
      setError(null);
      setSuccess(null);
      setProgress(0);

      // Create a FormData object to send the file
      const formData = new FormData();
      formData.append('file', file);
      formData.append('websiteId', websiteId);
      formData.append('domain', domain);

      // Use XMLHttpRequest to track upload progress
      const xhr = new XMLHttpRequest();

      xhr.upload.addEventListener('progress', (event) => {
        if (event.lengthComputable) {
          const percentComplete = Math.round((event.loaded / event.total) * 50); // Up to 50% for upload
          setProgress(percentComplete);
        }
      });

      xhr.addEventListener('load', async () => {
        if (xhr.status >= 200 && xhr.status < 300) {
          try {
            const responseData = JSON.parse(xhr.responseText);
            setProgress(100);
            setSuccess(responseData.message || "Import completed successfully");
          } catch (err) {
            setError("Error processing server response");
          }
        } else {
          try {
            const responseData = JSON.parse(xhr.responseText);
            setError(responseData.error || responseData.details || "Import failed");
          } catch (err) {
            setError(`Import failed with status ${xhr.status}`);
          }
        }
        setIsLoading(false);
      });

      xhr.addEventListener('error', () => {
        setError("Network error occurred during upload");
        setIsLoading(false);
      });

      xhr.addEventListener('abort', () => {
        setError("Upload was aborted");
        setIsLoading(false);
      });

      // Start the upload
      xhr.open('POST', '/api/import/plausible/upload');
      xhr.send(formData);

      // Update progress during processing
      let processingProgress = 50;
      const processingInterval = setInterval(() => {
        if (processingProgress < 95) {
          processingProgress += 1;
          setProgress(processingProgress);
        } else {
          clearInterval(processingInterval);
        }
      }, 300);

      // Clear interval on component unmount or when done
      return () => {
        clearInterval(processingInterval);
        xhr.abort();
      };
    } catch (err) {
      setIsLoading(false);
      setError(
        err instanceof Error
          ? err.message
          : "An unknown error occurred during import"
      );
    }
  };

 

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div
        ref={modalRef}
        className="bg-[#2A2A2A] p-6 rounded-lg w-full max-w-lg max-h-[90vh] overflow-y-auto"
      >
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-bold flex items-center gap-2">
            <svg className="text-blue-400 w-6 h-6" viewBox="0 0 88 88" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M44 88C68.3005 88 88 68.3005 88 44C88 19.6995 68.3005 0 44 0C19.6995 0 0 19.6995 0 44C0 68.3005 19.6995 88 44 88Z" fill="currentColor" />
              <path d="M44 88C68.3005 88 88 68.3005 88 44C88 19.6995 68.3005 0 44 0C19.6995 0 0 19.6995 0 44C0 68.3005 19.6995 88 44 88Z" fill="currentColor" />
              <path d="M16.6211 55.143L33.1318 38.6323C33.7979 37.9662 34.8849 37.9662 35.551 38.6323L44.4974 47.5787L63.7229 28.3532C64.389 27.6871 65.476 27.6871 66.1421 28.3532L71.3789 33.59V55.143C71.3789 57.4813 69.4813 59.3789 67.143 59.3789H20.8571C18.5188 59.3789 16.6211 57.4813 16.6211 55.143Z" fill="white" />
            </svg> Import Plausible Data
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-white transition-colors"
          >
            <FaTimes size={20} />
          </button>
        </div>

        <div className="mb-6">
          <p className="text-gray-300 mb-4">
            Import your analytics data from Plausible by uploading the ZIP file containing exported CSV files.
          </p>
          <p className="text-gray-300 mb-4">Please refer to this <a className="text-blue-400" href="https://plausible.io/docs/export-stats" target="_blank" rel="noopener noreferrer">tutorial</a> on how to export from Plausible.</p>
          {error && (
            <div className="bg-red-900/30 border border-red-900 text-red-300 p-3 rounded mb-4 flex items-center gap-2">
              <FaTimes className="text-red-400 flex-shrink-0" />
              <p>{error}</p>
            </div>
          )}

          {success && (
            <div className="bg-green-900/30 border border-green-900 text-green-300 p-3 rounded mb-4 flex items-center gap-2">
              <FaCheck className="text-green-400 flex-shrink-0" />
              <p>{success}</p>
            </div>
          )}

     

          <div className="space-y-6">
            <div className="bg-[#1C1C1C] border-2 border-dashed border-gray-700 rounded-lg p-8 text-center">
              <FaFileCsv className="text-blue-400 text-4xl mx-auto mb-3" />
              <h3 className="text-gray-300 font-medium mb-2">
                Upload your Plausible Export ZIP file
              </h3>
              <p className="text-gray-400 text-sm mb-4">
                The ZIP file should contain CSV files exported from Plausible
              </p>

              <input
                type="file"
                ref={fileInputRef}
                accept=".zip"
                onChange={handleFileChange}
                className="hidden"
                id="plausible-file-upload"
              />

              <label
                htmlFor="plausible-file-upload"
                className="px-4 py-2 bg-blue-600 text-white rounded cursor-pointer hover:bg-blue-700 transition-colors inline-flex items-center gap-2"
              >
                <FaUpload /> Choose ZIP File
              </label>

              {file && (
                <div className="mt-3 text-sm text-gray-300 flex items-center justify-center gap-2">
                  <FaFileArchive className="text-blue-400" />
                  <span className="truncate max-w-[200px]">{file.name}</span>
                  <span className="text-gray-400">
                    ({Math.round(file.size / 1024)}KB)
                  </span>
                </div>
              )}
            </div>

            {isLoading && (
              <div className="mt-4">
                <div className="flex justify-between text-sm text-gray-400 mb-1">
                  <span>Importing data...</span>
                  <span>{progress}%</span>
                </div>
                <div className="w-full bg-gray-700 rounded-full h-2.5">
                  <div
                    className="bg-blue-600 h-2.5 rounded-full transition-all duration-300"
                    style={{ width: `${progress}%` }}
                  ></div>
                </div>
                <p className="text-xs text-gray-500 mt-2">
                  {progress < 50
                    ? "Uploading file..."
                    : progress < 95
                      ? "Processing data and importing records..."
                      : "Almost done..."}
                </p>
              </div>
            )}

          </div>
        </div>

        <div className="flex justify-end gap-3">
          <button
            onClick={onClose}
            className="px-4 py-2 rounded-md text-gray-300 hover:bg-gray-700 transition-colors"
          >
            Cancel
          </button>
          <button
            onClick={handleImport}
            disabled={isLoading || !file}
            className={`px-4 py-2 rounded-md flex items-center gap-2 ${isLoading || !file
              ? "bg-blue-900/50 text-blue-300/50 cursor-not-allowed"
              : "bg-blue-600 text-white hover:bg-blue-700"
              } transition-colors`}
          >
            {isLoading ? (
              <>
                <FaSpinner className="animate-spin" /> Importing...
              </>
            ) : (
              <>
                <FaUpload /> Import Data
              </>
            )}
          </button>
        </div>
      </div>
    </div>
  );
} 