import React from "react";
import { Metadata } from "next";
import CodeBlock from "@/components/CodeBlock";

export const metadata: Metadata = {
  title: "Track Custom Events - Versatailor Documentation",
  description:
    "Learn how to implement and track custom events in your application using Versatailor's analytics platform.",
};

function TrackEventsPage() {
  return (
    <div className="max-w-3xl mx-auto py-8 px-4">
      <h1 className="text-3xl font-bold mb-6">Track Custom Events</h1>

      <section className="mb-12">
        <h2 className="text-2xl font-semibold mb-4">Overview</h2>
        <p className="text-gray-200 mb-4">
          Custom events allow you to track specific user interactions and
          behaviors in your application. With Versatailor&apos;s analytics
          platform, you can implement custom event tracking to gather valuable
          insights about how users interact with your features.
        </p>
      </section>

      <section className="mb-12">
        <h2 className="text-2xl font-semibold mb-4">Implementation</h2>
        <p className="text-gray-200 mb-4">
          To track custom events, use the{" "}
          <code className="bg-gray-800 px-2 py-1 rounded">versatailor</code>{" "}
          function available in the global window object. Here&apos;s a basic
          example:
        </p>
        <CodeBlock
          code={`window?.versatailor("submit_button_click", {
  description: "User clicked the submit button",
});`}
          language="javascript"
        />
      </section>

      <section className="mb-12">
        <h2 className="text-2xl font-semibold mb-4">Event Dashboard</h2>
        <p className="text-gray-200 mb-4">
          The custom events dashboard provides a comprehensive view of your
          tracked events, including:
        </p>
        <ul className="list-disc list-inside space-y-2 text-gray-200 mb-6">
          <li>Event name and description</li>
          <li>Total event count</li>
          <li>Unique users who triggered the event</li>
          <li>User journey analysis</li>
        </ul>
        <div className="bg-gray-800 rounded-lg p-6 mb-6">
          <h3 className="text-xl font-semibold mb-3">Event Metrics</h3>
          <p className="text-gray-300 mb-2">
            Each event in the dashboard displays key metrics:
          </p>
          <ul className="list-disc list-inside space-y-2 text-gray-300">
            <li>
              <strong>Count:</strong> Total number of times the event was
              triggered
            </li>
            <li>
              <strong>Unique Users:</strong> Number of distinct users who
              triggered the event
            </li>
            <li>
              <strong>Journey:</strong> Access to detailed user journey
              information
            </li>
          </ul>
        </div>
      </section>

      <section className="mb-12">
        <h2 className="text-2xl font-semibold mb-4">User Journey Analysis</h2>
        <p className="text-gray-200 mb-4">
          For each custom event, you can analyze the user&apos;s journey to
          understand the context and flow of actions:
        </p>
        <ul className="list-disc list-inside space-y-2 text-gray-200 mb-6">
          <li>View all instances of a specific event</li>
          <li>Analyze the sequence of actions before and after the event</li>
          <li>Understand user behavior patterns</li>
        </ul>
      </section>

      <section className="mb-12">
        <h2 className="text-2xl font-semibold mb-4">Best Practices</h2>
        <div className="space-y-4">
          <div className="bg-gray-800 rounded-lg p-6">
            <h3 className="text-xl font-semibold mb-3">Naming Conventions</h3>
            <p className="text-gray-300 mb-2">
              Use clear and consistent naming patterns for your events:
            </p>
            <CodeBlock
              code={`// Good examples
window?.versatailor("signup_form_submitted", { ... });
window?.versatailor("product_added_to_cart", { ... });

// Avoid
window?.versatailor("clicked", { ... }); // Too vague
window?.versatailor("btn_1_click", { ... }); // Not descriptive`}
              language="javascript"
            />
          </div>
        </div>
      </section>
    </div>
  );
}

export default TrackEventsPage;
