import { useCallback, useMemo } from 'react';
import { Event, ChartDataPoint, CustomPeriod, Period } from './types';
import { processEventsIntoChartData } from '@/lib/chartData';
import { normalizeDate } from './utils';

export function useAnalyticsCharts(
  events: Event[],
  selectedPeriod: Period,
  customPeriod?: CustomPeriod,
  sessionCache?: Map<string, { data: ChartDataPoint[]; timestamp: number }>
) {
  const processChartDataWithCache = useCallback(
    (eventsToProcess: Event[], period: Period): ChartDataPoint[] => {
      if (!sessionCache) {
        return processEventsIntoChartData(eventsToProcess, period, customPeriod!);
      }

      const eventsHash = JSON.stringify(eventsToProcess.map((e) => e.id)).slice(0, 100);
      const cacheKey = `chart_${eventsHash}_${period}${
        customPeriod
          ? `_custom_${normalizeDate(
              new Date(customPeriod.startDate),
              period
            )}_${normalizeDate(new Date(customPeriod.endDate), period)}`
          : ""
      }`;

      // Force cache refresh for specific periods
      if (
        period === "all" ||
        period === "last30d" ||
        period === "today" ||
        period === "wtd" ||
        period === "last24h" ||
        period === "mtd"
      ) {
        sessionCache.delete(cacheKey);
      }

      // Add year and month to cache key for ytd
      const now = new Date();
      const cacheKeyYtd = `chart_${eventsHash}_ytd_${now.getFullYear()}_${now.getMonth()}`;

      const cachedChart =
        period === "ytd"
          ? sessionCache.get(cacheKeyYtd)
          : sessionCache.get(cacheKey);

      if (cachedChart) {
        return cachedChart.data;
      }

      const chartData = processEventsIntoChartData(
        eventsToProcess,
        period,
        customPeriod!
      );

      const cacheEntry = {
        data: chartData,
        timestamp: Date.now(),
      };

      if (period === "ytd") {
        sessionCache.set(cacheKeyYtd, cacheEntry);
      } else {
        sessionCache.set(cacheKey, cacheEntry);
      }

      return chartData;
    },
    [customPeriod, sessionCache]
  );

  const chartData = useMemo(
    () => processChartDataWithCache(events, selectedPeriod),
    [events, selectedPeriod, processChartDataWithCache]
  );

  return {
    chartData,
    processChartDataWithCache,
  };
} 