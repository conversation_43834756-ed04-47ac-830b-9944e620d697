import MuxPlayer from "@mux/mux-player-react";

function VideoPlayer({
  path,
  videoName,
}: {
  path: string;
  videoName?: string;
}) {
  function handlePlay() {
    window?.versatailor(`played_${videoName}`, `User played ${videoName}`);
  }

  return (
    <div>
      <MuxPlayer
        playbackId={path}
        metadata={{
          video_title: videoName,
          viewer_user_id: "Placeholder (optional)",
        }}
        poster={`https://image.mux.com/${path}/animated.gif?width=640&fps=15&start=0&end=5`}
        style={{ aspectRatio: "16/9", borderRadius: "6px" }}
        onPlay={handlePlay}
      />
    </div>
  );
}

export default VideoPlayer;
