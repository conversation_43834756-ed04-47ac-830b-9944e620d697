import {
  Period,
  ChartDataPoint,
  Filter,
  CustomPeriod,
  Event,
  AnalyticsData,
} from "@/lib/types";

export interface CacheEntry<T> {
  data: T;
  timestamp: number;
}

export interface SessionCache {
  events: Map<string, CacheEntry<Event[]>>;
  filteredResults: Map<string, CacheEntry<Event[]>>;
  chartData: Map<string, CacheEntry<ChartDataPoint[]>>;
  cacheVersion: string;
}

export interface AnalyticsHookProps {
  websiteId: string;
  selectedPeriod: Period;
  activeFilters: Filter[];
  customPeriod?: CustomPeriod;
}

export interface MetricsData {
  uniqueVisitors: number;
  prevUniqueVisitors: number;
  visitorsDelta: number | null;
  visitorsDeltaPercentage: number | null;
  bounceRate: number;
  prevBounceRate: number;
  bounceRateDelta: number | null;
  avgVisitTime: number;
  prevAvgVisitTime: number;
  avgVisitTimeDelta: number | null;
  avgVisitTimeDeltaPercentage: number | null;
}

export interface DateRange {
  from: Date;
  to: Date;
}

export type {
  Period,
  ChartDataPoint,
  Filter,
  CustomPeriod,
  Event,
  AnalyticsData,
};

export type ChunkedAnalytics = {
  basic: {
    isLoading: boolean;
    uniqueVisitors: number;
    currentVisitors: number;
    totalPageviews: number;
  };
  metrics: {
    bounceRate: number;
    avgVisitTime: number;
    visitorsDelta: number | null;
    pageviewsDelta: number | null;
  };
  deltas: {
    visitorsDeltaPercentage: number | null;
    bounceRateDelta: number | null;
    avgVisitTimeDeltaPercentage: number | null;
    avgVisitTimeDelta: number | null;
  };
  charts: {
    chartData: ChartDataPoint[];
    visitorsSeries: ChartDataPoint[];
  };
  details: {
    topPages: any[];
    entryPages: any[];
    exitPages: any[];
    topReferrers: any[];
    operatingSystems: any[];
    browsers: any[];
    devices: any[];
    topLocations: any[];
  };
  events: {
    events: Event[];
    allEvents: Event[];
  };
};
