import React from "react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>Title,
  CardDescription,
  CardContent,
} from "../ui/card";
import Image from "next/image";

function Testimonials() {
  return (
    <section id="testimonials" className="w-full py-12 md:py-24 lg:py-32">
      <div className="container px-4 md:px-6">
        <div className="flex flex-col items-center justify-center space-y-4 text-center">
          <div className="space-y-2">
            <div className="inline-block rounded-lg bg-primary px-3 py-1 text-sm text-primary-foreground">
              Testimonials
            </div>
            <h2 className="text-3xl font-bold tracking-tighter md:text-4xl/tight">
              Trusted by innovative companies
            </h2>
            <p className="max-w-[900px] text-gray-300 md:text-xl/relaxed lg:text-base/relaxed xl:text-xl/relaxed">
              See what our customers have to say about StreamLine.
            </p>
          </div>
        </div>
        <div className="mx-auto grid max-w-5xl items-center gap-6 py-12 md:grid-cols-2 lg:gap-12">
          <Card className="bg-customGrayLight border-gray-700/50">
            <CardHeader>
              <div className="flex items-center gap-4">
                <Image
                  src="/placeholder.svg?height=60&width=60"
                  width={60}
                  height={60}
                  alt="Avatar"
                  className="rounded-full"
                />
                <div>
                  <CardTitle className="text-lg">Sarah Johnson</CardTitle>
                  <CardDescription>CEO, TechFlow</CardDescription>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <p className="text-gray-300">
                &quot;StreamLine has transformed how we understand our users.
                The insights we&apos;ve gained have directly contributed to a
                32% increase in user retention.&quot;
              </p>
            </CardContent>
          </Card>
          <Card className="bg-customGrayLight border-gray-700/50">
            <CardHeader>
              <div className="flex items-center gap-4">
                <Image
                  src="/placeholder.svg?height=60&width=60"
                  width={60}
                  height={60}
                  alt="Avatar"
                  className="rounded-full"
                />
                <div>
                  <CardTitle className="text-lg">Michael Chen</CardTitle>
                  <CardDescription>Product Manager, DataSphere</CardDescription>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <p className="text-gray-300">
                &quot;The user journey tracking feature has been invaluable for
                our product team. We&apos;ve identified and fixed several UX
                issues that were causing drop-offs.&quot;
              </p>
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  );
}

export default Testimonials;
