!function(){"use strict";if(/^localhost$|^127(\.[0-9]+){0,2}\.[0-9]+$|^\[::1?\]$/.test(window.location.hostname)||"file:"===window.location.protocol||window!==window.parent)return void console.warn("VersaTailor: Tracking disabled on localhost, file protocol, or inside iframe");const t=document.currentScript,n="data-",e=t.getAttribute.bind(t),o=t.src.includes("versatailor.com")?"https://versatailor.com/api/events":new URL("/api/events",window.location.origin).href,i=e(n+"website-id"),a=e(n+"domain");function s(t,n,e){let o="";if(e){const t=new Date;t.setTime(t.getTime()+24*e*60*60*1e3),o="; expires="+t.toUTCString()}document.cookie=t+"="+(n||"")+o+"; path=/"}function r(t){const n=t+"=",e=document.cookie.split(";");for(let t=0;t<e.length;t++){let o=e[t];for(;" "===o.charAt(0);)o=o.substring(1,o.length);if(0===o.indexOf(n))return o.substring(n.length,o.length)}return null}function c(){const t=window.location.href;if(!t)return void console.warn("VersaTailor: Unable to collect href. This may indicate incorrect script implementation or browser issues.");const n={websiteId:i,domain:a,href:t,referrer:document.referrer||null,timestamp:new Date().toISOString(),viewport:{width:window.innerWidth,height:window.innerHeight},userAgent:navigator.userAgent||"unknown",location:{city:null,country:null}};const e=function(){let t=r("versatailor_visitor_id");return t||(t=generateUUID(),s("versatailor_visitor_id",t,365)),t}();const o=u();return n.visitorId=e,n.sessionId=o,n}function generateUUID(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(t){const n=16*Math.random()|0;return("x"==t?n:3&n|8).toString(16)})}function f(t,n){if("true"===localStorage.getItem("versatailor_ignore"))return void(n&&n({status:200}));const e=localStorage.getItem(`excluded_devices_${t.websiteId}`);if(e){const o=JSON.parse(e),i=localStorage.getItem("device_ip"),a=o.some(t=>t.userAgent===navigator.userAgent&&t.ip===i);if(a)return void(n&&n({status:200}))}if(!t||!t.websiteId||!t.domain)return console.error("VersaTailor Analytics: Invalid event data",t),void(n&&n({status:400,error:"Invalid event data"}));const i=new XMLHttpRequest;i.open("POST",o,!0),i.setRequestHeader("Content-Type","application/json"),i.timeout=5e3,i.onreadystatechange=function(){i.readyState===XMLHttpRequest.DONE&&(200===i.status?s("versatailor_session_id",u(),1/48):console.error("VersaTailor Analytics: Error sending event",i.status,i.responseText),n&&n({status:i.status}))},i.onerror=function(){console.error("VersaTailor Analytics: Network error occurred"),n&&n({status:0})},i.ontimeout=function(){console.error("VersaTailor Analytics: Request timed out"),n&&n({status:-1})};try{i.send(JSON.stringify(t))}catch(t){console.error("VersaTailor Analytics: Error sending request",t),n&&n({status:-2,error:t.message})}}async function getLocationData(){try{const t=new AbortController,n=setTimeout(()=>t.abort(),3e3),e=await fetch("https://ipapi.co/json/",{signal:t.signal});if(clearTimeout(n),!e.ok)throw new Error("Failed to fetch location data");const o=await e.json();if(null===o.city&&null===o.country_name&&null===o.region)throw new Error("Location data is empty");return{city:o.city||null,country:o.country_name||null,region:o.region||null}}catch(t){return console.warn("VersaTailor: Unable to fetch location data:",t),{city:null,country:null,region:null}}}function d(t){const n=c();n.type="pageview",n.initialPageview=!0,f(n,t),getLocationData().then(t=>{if(t&&(t.city||t.country||t.region)){const e={...n,location:t,type:"pageview_update",initialPageview:!1};f(e)}}).catch(t=>{console.warn("VersaTailor: Error getting location:",t)})}function l(t,n,e){const o=c();o.type=t,o.extraData=n,o.initialEvent=!0,f(o,e),getLocationData().then(e=>{if(e&&(e.city||e.country||e.region)){const i={...o,location:e,type:t+"_update",initialEvent:!1};f(i)}}).catch(t=>{console.warn("VersaTailor: Error getting location:",t)})}function x(t){var n;t&&t.href&&(n=t.href,window.location.hostname!==new URL(n,window.location.origin).hostname)&&l("external_link",{url:t.href,text:t.textContent.trim()})}function u(){let t=r("versatailor_session_id");return t||(t="sxxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(t){const n=16*Math.random()|0;return("x"==t?n:3&n|8).toString(16)}),s("versatailor_session_id",t,1/48)),t}if(window.versatailor=function(t,n){t?!["signup","payment"].includes(t)||n?.email?["signup","payment"].includes(t)?l(t,{email:n.email}):l("custom",{eventName:t,...n}):console.warn(`VersaTailor: Missing email for ${t} event`):console.warn("VersaTailor: Missing event_name for custom event")},document.addEventListener("click",function(t){x(t.target.closest("a"))}),document.addEventListener("keydown",function(t){if("Enter"===t.key||" "===t.key){x(t.target.closest("a"))}}),!i||!a)return void console.warn("VersaTailor Analytics: Missing website ID or domain");d();let w=window.location.pathname;const p=window.history.pushState;window.history.pushState=function(){p.apply(this,arguments),w!==window.location.pathname&&(w=window.location.pathname,d())},window.addEventListener("popstate",function(){w!==window.location.pathname&&(w=window.location.pathname,d())})}();