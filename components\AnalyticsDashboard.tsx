import { memo, useCallback } from "react";
import { FixedSizeList as List } from "react-window";
import { Period, Filter, CustomPeriod } from "@/lib/types";
import { useChunkedAnalytics } from "@/hooks/analytics/useChunkedData";

// Add throttle utility
function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean;
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => (inThrottle = false), limit);
    }
  };
}

// Basic metrics component
const BasicMetrics = memo(function BasicMetrics({
  data,
}: {
  data: ReturnType<typeof useChunkedAnalytics>["basic"];
}) {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      {/* Your basic metrics UI */}
    </div>
  );
});

BasicMetrics.displayName = "BasicMetrics";

// Chart component with throttled interactions
const AnalyticsChart = memo(function AnalyticsChart({
  data,
}: {
  data: ReturnType<typeof useChunkedAnalytics>["charts"];
}) {
  const handleHover = useCallback(
    throttle((event: React.MouseEvent) => {
      // Your hover handling logic
    }, 16),
    []
  );

  return (
    <div className="relative">
      {/* Your chart component with throttled interactions */}
    </div>
  );
});

AnalyticsChart.displayName = "AnalyticsChart";

// Virtualized list component
const VirtualizedList = memo(function VirtualizedList<T>({
  data,
  renderItem,
  itemHeight = 40,
  maxHeight = 400,
}: {
  data: T[];
  renderItem: (item: T, index: number) => React.ReactNode;
  itemHeight?: number;
  maxHeight?: number;
}) {
  const Row = useCallback(
    ({ index, style }: { index: number; style: React.CSSProperties }) => {
      return <div style={style}>{renderItem(data[index], index)}</div>;
    },
    [data, renderItem]
  );

  return (
    <List
      height={Math.min(data.length * itemHeight, maxHeight)}
      itemCount={data.length}
      itemSize={itemHeight}
      width="100%"
    >
      {Row}
    </List>
  );
});

VirtualizedList.displayName = "VirtualizedList";

// Details section with virtualized lists
const DetailsSection = memo(function DetailsSection({
  data,
}: {
  data: ReturnType<typeof useChunkedAnalytics>["details"];
}) {
  const renderPage = useCallback(
    (page: any, index: number) => (
      <div className="flex justify-between items-center px-4 py-2 hover:bg-gray-50">
        <span className="text-sm text-gray-600">{page.path}</span>
        <span className="text-sm font-medium">{page.views}</span>
      </div>
    ),
    []
  );

  const renderReferrer = useCallback(
    (referrer: any, index: number) => (
      <div className="flex justify-between items-center px-4 py-2 hover:bg-gray-50">
        <span className="text-sm text-gray-600">{referrer.source}</span>
        <span className="text-sm font-medium">{referrer.visits}</span>
      </div>
    ),
    []
  );

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
      <div className="bg-white rounded-lg shadow">
        <div className="p-4 border-b">
          <h3 className="text-lg font-medium">Top Pages</h3>
        </div>
        <VirtualizedList data={data.topPages} renderItem={renderPage} />
      </div>

      <div className="bg-white rounded-lg shadow">
        <div className="p-4 border-b">
          <h3 className="text-lg font-medium">Top Referrers</h3>
        </div>
        <VirtualizedList data={data.topReferrers} renderItem={renderReferrer} />
      </div>

      <div className="bg-white rounded-lg shadow">
        <div className="p-4 border-b">
          <h3 className="text-lg font-medium">Entry Pages</h3>
        </div>
        <VirtualizedList data={data.entryPages} renderItem={renderPage} />
      </div>

      <div className="bg-white rounded-lg shadow">
        <div className="p-4 border-b">
          <h3 className="text-lg font-medium">Exit Pages</h3>
        </div>
        <VirtualizedList data={data.exitPages} renderItem={renderPage} />
      </div>
    </div>
  );
});

DetailsSection.displayName = "DetailsSection";

interface AnalyticsDashboardProps {
  websiteId: string;
  selectedPeriod: Period;
  activeFilters: Filter[];
  customPeriod?: CustomPeriod;
}

export const AnalyticsDashboard = memo(function AnalyticsDashboard({
  websiteId,
  selectedPeriod,
  activeFilters,
  customPeriod,
}: AnalyticsDashboardProps) {
  const analytics = useChunkedAnalytics(
    websiteId,
    selectedPeriod,
    activeFilters,
    customPeriod
  );

  if (analytics.basic.isLoading) {
    return <div>Loading...</div>;
  }

  return (
    <div className="space-y-6">
      <BasicMetrics data={analytics.basic} />
      <AnalyticsChart data={analytics.charts} />
      <DetailsSection data={analytics.details} />
    </div>
  );
});
