import { useMemo } from "react";
import {
  Period,
  Filter,
  CustomPeriod,
  Event,
  AnalyticsData,
} from "@/lib/types";
import {
  getTopPages,
  getTopReferrers,
  getOperatingSystems,
  getTopLocations,
  getBrowsers,
  getDevices,
  getEntryPages,
  getExitPages,
  getExitLinks,
} from "@/lib/analytics";
import { useAnalyticsEvents } from "./analytics/useAnalyticsEvents";
import { useAnalyticsMetrics } from "./analytics/useAnalyticsMetrics";
import { useAnalyticsCharts } from "./analytics/useAnalyticsCharts";
import { useAnalyticsRealtime } from "./analytics/useAnalyticsRealtime";
import { useAnalyticsCache } from "./analytics/useAnalyticsCache";
import { useAnalyticsFilters } from "./analytics/useAnalyticsFilters";
import type { AnalyticsHookProps, ChunkedAnalytics } from "./analytics/types";


// Add a new type for chunked data

// Add a new hook for chunked data access

// Modify the original hook to use chunked processing
export function useAnalyticsData(props: AnalyticsHookProps): AnalyticsData {
  const { websiteId, selectedPeriod, activeFilters, customPeriod } = props;
  const { sessionCache } = useAnalyticsCache();
  const {
    events: unfilteredEvents,
    prevPeriodEvents: unfilteredPrevEvents,
    isLoading,
  } = useAnalyticsEvents(
    websiteId,
    selectedPeriod,
    activeFilters,
    customPeriod
  );
  const realtimeVisitors = useAnalyticsRealtime(websiteId);

  // Apply filters to events
  const { filteredEvents: events, filteredPrevEvents: prevPeriodEvents } =
    useAnalyticsFilters(unfilteredEvents, unfilteredPrevEvents, activeFilters);

  const metrics = useAnalyticsMetrics(events, prevPeriodEvents, selectedPeriod);
  const { chartData } = useAnalyticsCharts(
    events,
    selectedPeriod,
    customPeriod,
    sessionCache.chartData
  );

  const analyticsData = useMemo(() => {
    const pageData = {
      topPages: getTopPages(events),
      entryPages: getEntryPages(events),
      exitPages: getExitPages(events),
      exitLinks: getExitLinks(events),
      topReferrers: getTopReferrers(events),
      operatingSystems: getOperatingSystems(events),
      browsers: getBrowsers(events),
      devices: getDevices(events),
      topLocations: getTopLocations(events),
    };

    return {
      isLoading,
      chartData,
      uniqueVisitors: metrics.uniqueVisitors,
      currentVisitors: realtimeVisitors,
      bounceRate: metrics.bounceRate,
      avgVisitTime: metrics.avgVisitTime,
      ...pageData,
      visitorsDeltaPercentage: metrics.visitorsDeltaPercentage,
      visitorsDelta: metrics.visitorsDelta,
      bounceRateDelta: metrics.bounceRateDelta,
      avgVisitTimeDeltaPercentage: metrics.avgVisitTimeDeltaPercentage,
      avgVisitTimeDelta: metrics.avgVisitTimeDelta,
      events,
      allEvents: unfilteredEvents,
      totalPageviews: events.filter((e) => e.type === "pageview").length,
      pageviewsDelta: null,
      visitorsSeries: chartData,
    } as AnalyticsData;
  }, [
    isLoading,
    chartData,
    metrics,
    realtimeVisitors,
    events,
    unfilteredEvents,
  ]);

  return analyticsData;
}
