import { NextRequest, NextResponse } from "next/server";
import { auth } from "@clerk/nextjs/server";
import Website from "@/models/website";
import Event from "@/models/event";
import connect from "@/util/db";

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ websiteId: string }> }
) {
  try {
    await connect();
    const { userId } = await auth();

    if (!userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { websiteId } = await params;
    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get("limit") || "10");

    // Verify website ownership
    // Conditionally include userId based on environment
    const websiteQuery = process.env.NODE_ENV === "production"
      ? { id: websiteId, userId }
      : { id: websiteId };

    const website = await Website.findOne(websiteQuery);

    if (!website) {
      return NextResponse.json({ error: "Website not found" }, { status: 404 });
    }

    // Only check revenueAttributionEnabled in production
    if (process.env.NODE_ENV === "production" && !website.revenueAttributionEnabled) {
      return NextResponse.json(
        { error: "Revenue attribution not enabled" },
        { status: 400 }
      );
    }

    // Fetch recent payment events
    const recentPaymentEvents = await Event.find({
      websiteId,
      type: "payment",
      "revenueData.amount": { $exists: true },
      isBot: { $ne: true },
    })
      .sort({ timestamp: -1 })
      .limit(Math.min(limit, 50)) // Cap at 50 events
      .select("timestamp revenueData referrer location");

    const events = recentPaymentEvents.map(event => ({
      id: event.id,
      timestamp: event.timestamp,
      amount: event.revenueData?.amount || 0,
      currency: event.revenueData?.currency || "usd",
      customerEmail: event.revenueData?.customerEmail,
      stripeChargeId: event.revenueData?.stripeChargeId,
      referrer: event.referrer,
      location: event.location,
    }));

    return NextResponse.json({
      events,
      total: events.length,
    });
  } catch (error) {
    console.error("Error fetching revenue events:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
} 