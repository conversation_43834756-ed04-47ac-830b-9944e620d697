import { User } from "@clerk/nextjs/server";

export function getFirstName(user: User) {
  if (user.firstName) {
    return user.firstName;
  }

  const externalAccounts = user.externalAccounts;
  if (externalAccounts && externalAccounts.length > 0) {
    const googleAccount = externalAccounts.find(
      (account) => account.provider === "google"
    );

    const githubAccount = externalAccounts.find(
      (account) => account.provider === "github"
    );

    if (googleAccount?.firstName) {
      return googleAccount.firstName;
    }

    if (githubAccount?.firstName) {
      return githubAccount.firstName;
    }

    if (googleAccount?.username || githubAccount?.username) {
      return googleAccount?.username || githubAccount?.username;
    }
  }

  
}
