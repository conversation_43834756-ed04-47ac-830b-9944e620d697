import { useState, useEffect } from 'react';
import { Website } from '@/lib/types';

export function useWebsiteData(websiteId: string) {
  const [website, setWebsite] = useState<Website | null>(null);
  const [websites, setWebsites] = useState<Website[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');

  // Fetch single website data
  useEffect(() => {
    const fetchWebsite = async () => {
      try {
        const response = await fetch(`/api/websites/${websiteId}`);
        if (!response.ok) throw new Error('Website not found');
        const data = await response.json();
        setWebsite(data);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to load data');
      } finally {
        setIsLoading(false);
      }
    };

    fetchWebsite();
  }, [websiteId]);

  // Fetch all websites
  useEffect(() => {
    const fetchWebsites = async () => {
      try {
        const response = await fetch('/api/websites');
        if (!response.ok) throw new Error('Failed to fetch websites');
        const data = await response.json();
        setWebsites(data);
      } catch (err) {
        console.error('Error fetching websites:', err);
      }
    };
    fetchWebsites();
  }, []);

  return { website, websites, isLoading, error };
} 