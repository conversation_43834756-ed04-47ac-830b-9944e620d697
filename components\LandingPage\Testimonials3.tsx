import Image from "next/image";
import { StaticImageData } from "next/image";
import config from "@/config";


const list: {
  name: string;
  text: string;
  img?: string | StaticImageData;
}[] = [
  {
    name: "<PERSON>",
    text: "Wow, Apply <PERSON><PERSON><PERSON> has been a total game-changer for me!<highlight>I used to dread the admin work that came with job hunting</highlight>, but now? It's a breeze. The AI thing they've got going on is seriously impressive. I just paste in the job details, and boom – it's all organized for me. <highlight>Saved me so much time</highlight> that I actually enjoyed my job search for once!",
  },
  {
    name: "<PERSON>",
    text: "The analytics feature is a game-changer – <highlight>it showed me which job boards were actually worth my time</highlight>. No more shooting in the dark! Before I knew it, I had a great job offer in my inbox. <highlight>Six weeks from start to finish.</highlight> ",
  },
  {
    name: "u/Acceptable-Young1102",
    text: "This thing is super clean and easy to use. The interface is really intuitive. Definitely going to help me with my job search. <highlight>I'm excited to ditch my spreadsheets.</highlight>",
  },
];

function Testimonial({ i }: { i: number }) {
  const testimonial = list[i];

  if (!testimonial) return null;

  return (
    <li key={i}>
      <figure className="relative max-w-lg h-full p-6 md:p-10 bg-base-200 rounded-2xl max-md:text-sm flex flex-col">
        <blockquote className="relative flex-1">
          <p className="text-base-content/80 leading-relaxed">
            {testimonial.text
              .split(/<highlight>|<\/highlight>/)
              .map((part, index) =>
                index % 2 === 0 ? (
                  part
                ) : (
                  <span
                    key={index}
                    className="font-semibold bg-white text-black p-1 rounded-md"
                  >
                    {part}
                  </span>
                )
              )}
          </p>
        </blockquote>
        <figcaption className="relative flex items-center justify-start gap-4 pt-4 mt-4 md:gap-8 md:pt-8 md:mt-8 border-t border-base-content/5">
          <div className="w-full flex items-center justify-between gap-2">
            <div>
              <div className="font-medium text-base-content md:mb-0.5">
                {testimonial.name}
              </div>
            </div>

            <div className="overflow-hidden rounded-full bg-base-300 shrink-0">
              {testimonial.img ? (
                <Image
                  className="w-10 h-10 md:w-12 md:h-12 rounded-full object-cover"
                  src={list[i].img!}
                  alt={`${list[i].name}'s testimonial for ${config.appName}`}
                  width={48}
                  height={48}
                />
              ) : (
                <span className="w-10 h-10 md:w-12 md:h-12 rounded-full flex justify-center items-center text-lg font-medium bg-base-300">
                  {testimonial.name.charAt(0)}
                </span>
              )}
            </div>
          </div>
        </figcaption>
      </figure>
    </li>
  );
}

async function Testimonials3() {

  return (
    <section id="reviews" className="bg-graybg">
      <div className="py-24 px-8 max-w-7xl mx-auto">
        <div className="flex flex-col text-center w-full mb-20">
          <div className="mb-8">
            <h2 className="sm:text-5xl text-4xl font-extrabold text-base-content">
              120+ Job seekers are accelerating
              their careers!
            </h2>
          </div>
          <p className="lg:w-2/3 mx-auto leading-relaxed text-base text-base-content/80">
            Don&apos;t just take our word for it. Here&apos;s how Apply Nexus is
            transforming job searches and helping people land their dream roles
            faster.
          </p>
        </div>

        <ul
          role="list"
          className="flex flex-col items-center lg:flex-row lg:items-stretch gap-6 lg:gap-8"
        >
          {[...Array(4)].map((e, i) => (
            <Testimonial key={i} i={i} />
          ))}
        </ul>
      </div>
    </section>
  );
}

export default Testimonials3;
