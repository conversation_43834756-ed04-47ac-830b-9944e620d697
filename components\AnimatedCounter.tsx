"use client";
import { useState, useEffect, useRef } from "react";

function AnimatedCounter({ value }: { value: number }) {
  const [displayValue, setDisplayValue] = useState(value);
  const [direction, setDirection] = useState<"up" | "down" | null>("down");
  const [key, setKey] = useState(0);
  const prevValueRef = useRef(value);

  useEffect(() => {
    if (value !== prevValueRef.current) {
      setDirection(value > prevValueRef.current ? "up" : "down");
      setDisplayValue(value);
      setKey(prev => prev + 1);
      prevValueRef.current = value;
    }
  }, [value]);

  return (
    <div className="relative overflow-hidden h-[1.75em] text-xl sm:text-2xl">
      <div
        key={key}
        className={`absolute w-full ${
          direction === "up"
            ? "animate-slide-up"
            : direction === "down"
            ? "animate-slide-down"
            : ""
        }`}
      >
        {displayValue}
      </div>
    </div>
  );
}

export default AnimatedCounter;
