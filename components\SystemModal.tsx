import React, { Dispatch, JS<PERSON>, RefObject, SetStateAction } from "react";
import { useFilterState } from "@/hooks/useFilterState";
import { useOSIcon } from "@/hooks/useOSIcon";
import { OperatingSystem } from "@/lib/types";

type SystemModalProps = {
  systemModalRef: RefObject<HTMLDivElement | null>;
  systemView: "os" | "browser" | "device";
  setSystemView: Dispatch<SetStateAction<"os" | "browser" | "device">>;
  operatingSystems: OperatingSystem[];
  maxOSVisitors: number;
  browsers: {
    name: string;
    visitors: number;
  }[];
  maxBrowserVisitors: number;
  getBrowserIcon: (browserName: string) => JSX.Element;
  devices: {
    name: string;
    visitors: number;
  }[];
  maxDeviceVisitors: number;
  getDeviceIcon: (deviceName: string) => JSX.Element;
  setShowSystemModal: Dispatch<SetStateAction<boolean>>;
};

function SystemModal({
  systemModalRef,
  systemView,
  setSystemView,
  operatingSystems,
  maxOSVisitors,
  browsers,
  maxBrowserVisitors,
  getBrowserIcon,
  devices,
  maxDeviceVisitors,
  getDeviceIcon,
  setShowSystemModal,
}: SystemModalProps) {
  const { handleFilterClick, activeFilters } = useFilterState();
  const { getOSIcon } = useOSIcon();

  const handleSystemFilterClick = (
    type: "system",
    value: string,
    label: string
  ) => {
    // First apply the filter
    handleFilterClick(type, value, label);
    // Then close the modal
    setTimeout(() => setShowSystemModal(false), 100);
  };

  return (
    <div 
      className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4 overflow-hidden"
      onClick={() => setShowSystemModal(false)}
    >
      <div
        ref={systemModalRef}
        className="bg-[#2A2A2A] rounded-lg w-full max-w-2xl max-h-[80vh] overflow-hidden flex flex-col"
        onClick={(e) => e.stopPropagation()}
      >
        <div className="flex items-center justify-between p-4 border-b border-gray-700">
          <h2 className="text-lg font-semibold text-white">System Info</h2>
          <button
            onClick={() => setShowSystemModal(false)}
            className="text-gray-400 hover:text-white"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-5 w-5"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fillRule="evenodd"
                d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                clipRule="evenodd"
              />
            </svg>
          </button>
        </div>
        <div className="flex justify-center p-2 border-b border-gray-700">
          <div className="flex rounded-lg p-1">
            <button
              onClick={() => setSystemView("os")}
              className={`px-2 py-1 rounded-md text-xs sm:text-sm transition-colors ${
                systemView === "os"
                  ? "bg-blue-500 text-white"
                  : "text-gray-400 hover:text-white"
              }`}
            >
              OS
            </button>
            <button
              onClick={() => setSystemView("browser")}
              className={`px-2 py-1 rounded-md text-xs sm:text-sm transition-colors ${
                systemView === "browser"
                  ? "bg-blue-500 text-white"
                  : "text-gray-400 hover:text-white"
              }`}
            >
              Browser
            </button>
            <button
              onClick={() => setSystemView("device")}
              className={`px-2 py-1 rounded-md text-xs sm:text-sm transition-colors ${
                systemView === "device"
                  ? "bg-blue-500 text-white"
                  : "text-gray-400 hover:text-white"
              }`}
            >
              Device
            </button>
          </div>
        </div>
        <div className="overflow-y-auto p-4 flex-grow">
          <div className="space-y-1">
            {systemView === "os" &&
              operatingSystems.map((os, i) => (
                <div
                  key={i}
                  className={`flex items-center justify-between p-1 sm:p-2 rounded transition-colors text-xs sm:text-sm relative overflow-hidden group cursor-pointer ${
                    activeFilters.some(
                      (filter) =>
                        filter.type === "system" && filter.value === os.name
                    )
                      ? "bg-blue-500/20"
                      : ""
                  }`}
                  onClick={() =>
                    handleSystemFilterClick("system", os.name, `OS: ${os.name}`)
                  }
                >
                  <div
                    className="absolute inset-0 bg-blue-500/40 transition-transform origin-left group-hover:bg-blue-500/20"
                    style={{
                      transform: `scaleX(${os.visitors / maxOSVisitors})`,
                    }}
                  />
                  <span className="text-white flex items-center gap-2 relative">
                    {getOSIcon(os.name)}
                    {os.name}
                  </span>
                  <span className="text-white relative">{os.visitors}</span>
                </div>
              ))}
            {systemView === "browser" &&
              browsers.map((browser, i) => (
                <div
                  key={i}
                  className={`flex items-center justify-between p-1 sm:p-2 rounded transition-colors text-xs sm:text-sm relative overflow-hidden group cursor-pointer ${
                    activeFilters.some(
                      (filter) =>
                        filter.type === "system" && filter.value === browser.name
                    )
                      ? "bg-blue-500/20"
                      : ""
                  }`}
                  onClick={() =>
                    handleSystemFilterClick(
                      "system",
                      browser.name,
                      `Browser: ${browser.name}`
                    )
                  }
                >
                  <div
                    className="absolute inset-0 bg-blue-500/40 transition-transform origin-left group-hover:bg-blue-500/20"
                    style={{
                      transform: `scaleX(${browser.visitors / maxBrowserVisitors})`,
                    }}
                  />
                  <span className="text-white flex items-center gap-2 relative">
                    {getBrowserIcon(browser.name)}
                    {browser.name}
                  </span>
                  <span className="text-white relative">{browser.visitors}</span>
                </div>
              ))}
            {systemView === "device" &&
              devices.map((device, i) => (
                <div
                  key={i}
                  className={`flex items-center justify-between p-1 sm:p-2 rounded transition-colors text-xs sm:text-sm relative overflow-hidden group cursor-pointer ${
                    activeFilters.some(
                      (filter) =>
                        filter.type === "system" && filter.value === device.name
                    )
                      ? "bg-blue-500/20"
                      : ""
                  }`}
                  onClick={() =>
                    handleSystemFilterClick(
                      "system",
                      device.name,
                      `Device: ${device.name}`
                    )
                  }
                >
                  <div
                    className="absolute inset-0 bg-blue-500/40 transition-transform origin-left group-hover:bg-blue-500/20"
                    style={{
                      transform: `scaleX(${device.visitors / maxDeviceVisitors})`,
                    }}
                  />
                  <span className="text-white flex items-center gap-2 relative">
                    {getDeviceIcon(device.name)}
                    {device.name}
                  </span>
                  <span className="text-white relative">{device.visitors}</span>
                </div>
              ))}
          </div>
        </div>
      </div>
    </div>
  );
}

export default SystemModal;