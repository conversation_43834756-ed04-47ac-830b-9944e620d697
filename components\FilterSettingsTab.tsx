import React, { Dispatch, SetStateAction, useState, useEffect } from "react";
import { Badge } from "@/components/ui/badge";

type Props = {
  privateRoutes: string[];
  setPrivateRoutes: Dispatch<SetStateAction<string[]>>;
  excludeAuthVisits: boolean;
  websiteId: string;
  setExcludeAuthVisits: Dispatch<SetStateAction<boolean>>;
};
function FilterSettingsTab({
  privateRoutes,
  setPrivateRoutes,
  excludeAuthVisits,
  websiteId,
  setExcludeAuthVisits,
}: Props) {
  const [newRoute, setNewRoute] = useState("");
  const [isSaving, setIsSaving] = useState(false);
  const [saveError, setSaveError] = useState<string | null>(null);
  const [saveSuccess, setSaveSuccess] = useState(false);
  const [isExcludingDevice, setIsExcludingDevice] = useState(false);
  const [deviceExcluded, setDeviceExcluded] = useState(false);
  const [deviceInfo, setDeviceInfo] = useState<{ ip: string | null; location: { city: string | null; country: string | null; region: string | null } | null }>(
    { ip: null, location: null }
  );

  // Check if this device is already excluded
  useEffect(() => {
    const excludedDevices = localStorage.getItem(`excluded_devices_${websiteId}`);
    const deviceIp = localStorage.getItem('device_ip');
    
    if (excludedDevices && deviceIp) {
      try {
        const devices = JSON.parse(excludedDevices);
        const isExcluded = devices.some(
          (device: { userAgent: string; ip: string }) => 
            device.userAgent === navigator.userAgent && 
            device.ip === deviceIp
        );
        setDeviceExcluded(isExcluded);
      } catch (e) {
        console.error("Error checking device exclusion:", e);
      }
    }
  }, [websiteId]);

  const getDeviceInfo = async () => {
    try {
      // Fetch IP and location data
      const response = await fetch("https://ipapi.co/json/");
      if (!response.ok) throw new Error("Failed to fetch location data");
      
      const data = await response.json();
      const ipAddress = data.ip || null;
      const location = {
        city: data.city || null,
        country: data.country_name || null,
        region: data.region || null,
      };

      // Store IP in localStorage for future comparison
      if (ipAddress) {
        localStorage.setItem('device_ip', ipAddress);
      }

      return { ip: ipAddress, location };
    } catch (error) {
      console.warn("Unable to fetch device info:", error);
      return { ip: null, location: null };
    }
  };

  const handleExcludeDevice = async () => {
    try {
      setIsExcludingDevice(true);

      // Get device information
      const deviceData = await getDeviceInfo();
      setDeviceInfo(deviceData);

      if (!deviceData.ip) {
        throw new Error("Could not determine device IP");
      }

      // Create device info object
      const device = {
        userAgent: navigator.userAgent,
        ip: deviceData.ip,
        location: deviceData.location,
        excludedAt: new Date().toISOString(),
      };

      // Get existing excluded devices or create new array
      let excludedDevices = [];
      const existingData = localStorage.getItem(`excluded_devices_${websiteId}`);
      
      if (existingData) {
        try {
          excludedDevices = JSON.parse(existingData);
        } catch (e) {
          console.error("Error parsing excluded devices:", e);
        }
      }

      // Check if device is already excluded
      const alreadyExcluded = excludedDevices.some(
        (d: { userAgent: string; ip: string }) => 
          d.userAgent === device.userAgent && d.ip === device.ip
      );

      if (!alreadyExcluded) {
        // Add new device to excluded list
        excludedDevices.push(device);
        localStorage.setItem(`excluded_devices_${websiteId}`, JSON.stringify(excludedDevices));
        setDeviceExcluded(true);
        setSaveSuccess(true);
        setTimeout(() => setSaveSuccess(false), 2000);
      }
    } catch (error) {
      setSaveError(error instanceof Error ? error.message : "Failed to exclude device");
      setTimeout(() => setSaveError(null), 3000);
    } finally {
      setIsExcludingDevice(false);
    }
  };

  const handleIncludeDevice = () => {
    try {
      // Get existing excluded devices
      const existingData = localStorage.getItem(`excluded_devices_${websiteId}`);
      if (!existingData) return;
      
      const deviceIp = localStorage.getItem('device_ip');
      if (!deviceIp) return;

      // Filter out this device
      let excludedDevices = JSON.parse(existingData);
      excludedDevices = excludedDevices.filter(
        (d: { userAgent: string; ip: string }) => 
          !(d.userAgent === navigator.userAgent && d.ip === deviceIp)
      );

      // Update localStorage
      localStorage.setItem(`excluded_devices_${websiteId}`, JSON.stringify(excludedDevices));
      setDeviceExcluded(false);
      setSaveSuccess(true);
      setTimeout(() => setSaveSuccess(false), 2000);
    } catch (error) {
      setSaveError(error instanceof Error ? error.message : "Failed to include device");
      setTimeout(() => setSaveError(null), 3000);
    }
  };

  const handleRemoveRoute = (routeToRemove: string) => {
    const newRoutes = privateRoutes.filter((route) => route !== routeToRemove);
    setPrivateRoutes(newRoutes);
    saveSettings(excludeAuthVisits, newRoutes);
  };

  const handleAddRoute = (route: string) => {
    let trimmedRoute = route.trim();
    
    // Add "/**" to the end of the route if it doesn't already have a trailing wildcard
    if (trimmedRoute && !trimmedRoute.endsWith("/**") && !trimmedRoute.endsWith("**")) {
      trimmedRoute = `${trimmedRoute}/**`;
    }
    
    if (trimmedRoute && !privateRoutes.includes(trimmedRoute)) {
      const newRoutes = [...privateRoutes, trimmedRoute];
      setPrivateRoutes(newRoutes);
      saveSettings(excludeAuthVisits, newRoutes);
    }
    setNewRoute("");
  };

  const handleExcludeAuthChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.checked;
    setExcludeAuthVisits(newValue);
    saveSettings(newValue, privateRoutes);
  };

  const saveSettings = async (
    newExcludeAuth: boolean,
    newPrivateRoutes: string[]
  ) => {
    try {
      setIsSaving(true);
      setSaveError(null);

      const response = await fetch(`/api/websites/${websiteId}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          excludeAuthVisits: newExcludeAuth,
          privateRoutes: newPrivateRoutes,
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to save settings");
      }

      setSaveSuccess(true);
      setTimeout(() => setSaveSuccess(false), 2000);
    } catch (error) {
      setSaveError(
        error instanceof Error ? error.message : "An error occurred"
      );
      setTimeout(() => setSaveError(null), 3000);
    } finally {
      setIsSaving(false);
    }
  };

  // Update existing routes to add "/**" if they don't have it
  useEffect(() => {
    if (privateRoutes.length > 0) {
      const updatedRoutes = privateRoutes.map(route => {
        // If it already has the correct format, keep it as is
        if (route.endsWith("/**")) return route;
        // If it has the incorrect format (**), fix it
        if (route.endsWith("**")) {
          return route.slice(0, -2) + "/**";
        }
        // Otherwise add the wildcard
        return `${route}/**`;
      });
      
      // Only update if there were changes
      if (JSON.stringify(updatedRoutes) !== JSON.stringify(privateRoutes)) {
        setPrivateRoutes(updatedRoutes);
        saveSettings(excludeAuthVisits, updatedRoutes);
      }
    }
  }, [privateRoutes, excludeAuthVisits, setPrivateRoutes, websiteId]);

  return (
    <div className="p-6 space-y-6">
      <div className="space-y-4">
        <h2 className="text-xl font-semibold flex items-center gap-2">
          <svg
            className="w-5 h-5 text-blue-500"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"
            />
          </svg>
          Visit Filter Settings
        </h2>
        <div className="space-y-4">
          <div className="flex items-center gap-3">
            <div className="flex-1">
              <label className="flex items-center gap-2 cursor-pointer">
                <input
                  type="checkbox"
                  checked={excludeAuthVisits}
                  onChange={handleExcludeAuthChange}
                  className="w-4 h-4 rounded border-gray-600 text-blue-500 focus:ring-blue-500 focus:ring-offset-gray-800 bg-gray-700"
                />
                <span>Exclude authenticated visits</span>
                <Badge variant="outline" className="text-xs bg-yellow-500/20 text-yellow-400 border-yellow-500/30 cursor-default">Beta</Badge>
              </label>
              <div className="flex items-center gap-2">
                <p className="text-sm text-gray-400">
                  When enabled, any visitor who accesses your private routes
                  will be identified as authenticated and their future visits
                  won&apos;t be counted in analytics.
                </p>
                {isSaving && (
                  <div className="flex items-center gap-2 text-gray-400">
                    <div className="w-3 h-3 border-2 border-gray-400 border-t-transparent rounded-full animate-spin"></div>
                    <span className="text-xs">Saving...</span>
                  </div>
                )}
                {saveSuccess && (
                  <div className="flex items-center gap-1 text-green-400">
                    <svg
                      className="w-3 h-3"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M5 13l4 4L19 7"
                      />
                    </svg>
                    <span className="text-xs">Saved</span>
                  </div>
                )}
                {saveError && (
                  <div className="flex items-center gap-1 text-red-400">
                    <svg
                      className="w-3 h-3"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M6 18L18 6M6 6l12 12"
                      />
                    </svg>
                    <span className="text-xs">Error saving</span>
                  </div>
                )}
              </div>
            </div>
          </div>

          {excludeAuthVisits && (
            <div className="space-y-2">
              <label className="block text-sm font-medium">
                Private Routes
              </label>
              <div className="space-y-3">
                <input
                  type="text"
                  value={newRoute}
                  onChange={(e) => setNewRoute(e.target.value)}
                  onKeyDown={(e) => {
                    if (e.key === "Enter" || e.key === ",") {
                      e.preventDefault();
                      handleAddRoute(newRoute);
                    }
                  }}
                  placeholder="Type a route and press Enter (e.g., /dashboard)"
                  className="w-full px-3 py-2 bg-[#1C1C1C] border border-gray-700 rounded-lg focus:ring-2 focus:ring-blue-500/20 focus:border-blue-500 text-sm text-gray-300 placeholder-gray-500"
                />
                <div className="flex flex-wrap gap-2">
                  {privateRoutes.map((route, index) => (
                    <div
                      key={index}
                      className="flex items-center gap-1 bg-blue-500/20 text-blue-400 px-2 py-1 rounded-lg text-sm"
                    >
                      <span>{route}</span>
                      <button
                        onClick={() => handleRemoveRoute(route)}
                        className="hover:text-blue-300 transition-colors"
                      >
                        <svg
                          className="w-4 h-4"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M6 18L18 6M6 6l12 12"
                          />
                        </svg>
                      </button>
                    </div>
                  ))}
                </div>
              </div>
              <p className="text-xs text-gray-400">
                List the routes that are only accessible to authenticated users.
                Press Enter or comma to add a route. &quot;/**&quot; will be automatically added to the end of each route.
              </p>
            </div>
          )}

          {/* Device Exclusion Section */}
          <div className="pt-4 border-t border-gray-800">
            <h3 className="text-lg font-medium mb-2 flex items-center gap-2">
              <svg
                className="w-5 h-5 text-blue-500"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                />
              </svg>
              Device Exclusion
            </h3>
            <p className="text-sm text-gray-400 mb-3">
              Exclude your current device from analytics tracking. This will prevent your own visits from being counted.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-3 items-start sm:items-center">
              {!deviceExcluded ? (
                <button
                  onClick={handleExcludeDevice}
                  disabled={isExcludingDevice}
                  className="inline-flex items-center gap-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg text-sm font-medium transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isExcludingDevice ? (
                    <>
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                      Processing...
                    </>
                  ) : (
                    <>
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728A9 9 0 015.636 5.636m12.728 12.728L5.636 5.636" />
                      </svg>
                      Exclude this device
                    </>
                  )}
                </button>
              ) : (
                <button
                  onClick={handleIncludeDevice}
                  className="inline-flex items-center gap-2 px-4 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg text-sm font-medium transition-colors"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                  Device excluded - Click to undo
                </button>
              )}
            </div>
            
            {deviceExcluded && (
              <p className="text-xs text-green-400 mt-2">
                This device is excluded from analytics tracking.
              </p>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
export default FilterSettingsTab;
