import { headers } from "next/headers";
import { WebhookEvent } from "@clerk/nextjs/server";
import { NextResponse } from "next/server";
import { Webhook } from "svix";
import connect from "@/util/db";
import User from "@/models/user";
import { getFirstName } from "@/util/getUserInfo";
import { Resend } from "resend";
import { clerkClient } from "@clerk/nextjs/server";
import Welcome from "@/emails/Welcome";

const resend = new Resend(process.env.RESEND_API_KEY);

export async function POST(req: Request) {
  const WEBHOOK_SECRET = process.env.CLERK_WEBHOOK_SECRET;

  if (!WEBHOOK_SECRET) {
    throw new Error(
      "Please add CLERK_WEBHOOK_SECRET from Clerk Dashboard to .env"
    );
  }

  // Get the headers
  const headerPayload = await headers();
  const svix_id = headerPayload.get("svix-id");
  const svix_timestamp = headerPayload.get("svix-timestamp");
  const svix_signature = headerPayload.get("svix-signature");

  // If there are no headers, error out
  if (!svix_id || !svix_timestamp || !svix_signature) {
    return new Response("Error occured -- no svix headers", {
      status: 400,
    });
  }

  // Get the body
  const payload = await req.json();
  const body = JSON.stringify(payload);

  // Create a new Svix instance with your secret
  const wh = new Webhook(WEBHOOK_SECRET);

  let evt: WebhookEvent;

  try {
    evt = wh.verify(body, {
      "svix-id": svix_id,
      "svix-timestamp": svix_timestamp,
      "svix-signature": svix_signature,
    }) as WebhookEvent;
  } catch (err) {
    console.error("Error verifying webhook:", err);
    return new Response("Error occured", {
      status: 400,
    });
  }

  const eventType = evt.type;

  // Handle the webhook
  try {
    await connect();

    switch (eventType) {
      case "user.created":
        const userId = evt.data.id;
        const email = evt.data.email_addresses[0]?.email_address;

        // Basic check for email
        if (!email) {
          console.error(
            "User created event missing email address for user:",
            userId
          );
          return NextResponse.json(
            { error: "User email missing" },
            { status: 400 }
          );
        }

        const existingUser = await User.findOne({ clerkId: userId });

        if (!existingUser) {
          // Fetch user details from Clerk *only* if the user doesn't exist in our DB yet
          const client = await clerkClient();
          const clerkUser = await client.users.getUser(userId);

          let firstName = getFirstName(clerkUser) || email.split("@")[0];

          const accessUntilDate = new Date(
            Date.now() + 14 * 24 * 60 * 60 * 1000
          );

          const user = new User({
            clerkId: userId, // Use defined userId
            email: email,
            name:
              `${evt.data.first_name || ""} ${
                evt.data.last_name || ""
              }`.trim() || "Anonymous",
            websites: [],
            preferences: {
              theme: "system",
              emailNotifications: true,
            },
            createdAt: new Date(),
            lastLoginAt: new Date(),
            accessUntil: accessUntilDate,
            priceId: null,
          });

          await Promise.all([
            resend.contacts.create({
              email: email,
              firstName: firstName,
              unsubscribed: false,
              audienceId: process.env.RESEND_AUDIENCE_KEY!,
            }),

            resend.emails.send({
              from: "Versatailor <<EMAIL>>",
              to: email,
              subject: `Welcome to Versatailor, ${firstName}!`,
              react: Welcome({ firstName }),
            }),
          ]);

          try {
            const savedUser = await user.save();
          } catch (saveError) {
            console.error("Error saving user:", saveError);
            throw saveError;
          }
        }
        break;

      case "user.updated":
        // Handle user updates if needed
        await User.findOneAndUpdate(
          { clerkId: evt.data.id },
          {
            email: evt.data.email_addresses[0].email_address,
            name:
              `${evt.data.first_name || ""} ${
                evt.data.last_name || ""
              }`.trim() || "Anonymous",
          },
          { new: true }
        );
        break;
    }

    return NextResponse.json({ message: "Webhook processed successfully" });
  } catch (error) {
    console.error("Error processing webhook:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

export const runtime = "nodejs";
