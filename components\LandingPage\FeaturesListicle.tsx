"use client";
import React, { useState, use<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, useRef } from "react";
import config from "@/config";
import { IconType } from "react-icons";
import {
  FaChart<PERSON>ine,
  FaClock,
  FaUserCheck,
  FaHeadset,
  FaCodeBranch,
  FaBrain,
} from "react-icons/fa6";
import { motion, AnimatePresence } from "framer-motion";
import Image from "next/image";
import Link from "next/link";
import { TbPresentationAnalytics } from "react-icons/tb";
import { Button } from "../ui/button";
import VideoPlayer from "../VideoPlayer";

const featureIcons: { [key: string]: IconType } = {
  chart: FaChartLine,
  clock: FaClock,
  userCheck: FaUserCheck,
  headset: FaHeadset,
  branch: FaCodeBranch,
  brain: FaBrain,
};

const features = [
  {
    name: "Web Analytics",
    description:
      "Track your website traffic, analyze user behavior, and make data-driven decisions to optimize your online business performance.",
    icon: "chart",
    hasDemo: true,
  },
  {
    name: "Hour By Hour Analytics",
    description:
      "Filter your analytics by hour to better understand spikes in traffic and user engagement.",
    icon: "clock",
    hasDemo: true,
  },
  {
    name: "Conversion Analytics",
    description:
      "Filter out authenticated visits to get a clear view of your landing page's true conversion rate and understand how new visitors interact with your site.",
    icon: "userCheck",
    hasDemo: true,
  },
  // {
  //   name: "Journey Tracking",
  //   description:
  //     "Track user journeys across your website to understand how users navigate your site and identify opportunities for improvement.",
  //   icon: "flag",
  //   hasDemo: true,
  // },
  {
    name: "Support",
    description:
      "We're here to help you every step of the way. Our 24/7 customer support is here to assist you at any hour to better understand your data.",
    icon: "headset",
  },
  {
    name: "Innovation",
    description:
      "We're always innovating to bring you the best possible experience. Enjoy regular updates and new features designed to keep you ahead of the curve.",
    icon: "branch",
  },
];

function FeaturesListicle() {
  const [selectedFeature, setSelectedFeature] = useState(features[0].name);
  const [isAutoScrolling, setIsAutoScrolling] = useState(true);

  useEffect(() => {
    if (isAutoScrolling) {
      const interval = setInterval(() => {
        const currentIndex = features.findIndex(
          (f) => f.name === selectedFeature
        );
        const nextIndex = (currentIndex + 1) % features.length;
        setSelectedFeature(features[nextIndex].name);
      }, 25000);

      return () => clearInterval(interval);
    }
  }, [selectedFeature, isAutoScrolling]);

  return (
    <section className="w-full py-24" id="features">
      <div className="max-w-7xl mx-auto px-4">
        {/* Subtle section title */}
        <div className="text-center mb-8">
          <span className="text-lg font-medium tracking-wider text-zinc-400 uppercase">
            Features
          </span>
        </div>
        {/* Heading section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-12"
        >
          <h2 className="text-3xl font-bold tracking-tighter sm:text-5xl xl:text-6xl/none mb-4 text-zinc-100">
            Discover the Power of{" "}
            <span className="text-[#204ec8]">{config.appName}</span>
          </h2>
          <p className="text-gray-300 md:text-xl mx-auto max-w-2xl">
            {config.appName} is packed with features designed to help you
            understand your data and make data-driven decisions to optimize your
            online business performance.
          </p>
        </motion.div>
        {/* Feature buttons */}
        <div className="mb-12 max-w-full">
          <p className="text-center text-sm text-zinc-400 mb-4 md:hidden">
            Swipe to explore features &rarr;
          </p>
          <div className="flex gap-4 pb-4 overflow-x-auto snap-x snap-mandatory scrollbar-hide md:flex-wrap md:justify-center lg:flex-nowrap lg:max-w-none">
            {features.map((feature, index) => {
              const IconComponent = featureIcons[feature.icon];
              const isSelected = selectedFeature === feature.name;

              return (
                <motion.div
                  key={feature.name}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.4, delay: index * 0.1 }}
                  className={`group flex flex-col items-center justify-center p-5 rounded-xl cursor-pointer transition-all duration-300 border snap-center shrink-0 min-w-[160px] md:w-[180px] lg:flex-1 hover:scale-105 relative ${
                    isSelected
                      ? "bg-gradient-to-b from-blue-900/30 to-zinc-900/40 border-blue-700/50 shadow-lg shadow-blue-900/20"
                      : "bg-zinc-900/30 border-zinc-800/50 hover:border-zinc-700/50 hover:bg-zinc-800/30"
                  }`}
                  onClick={() => {
                    setSelectedFeature(feature.name);
                    setIsAutoScrolling(false);
                  }}
                >
                  <div className="relative">
                    <IconComponent
                      size={28}
                      className={`mb-3 transition-transform group-hover:scale-110 ${
                        isSelected ? "text-blue-400" : "text-zinc-400"
                      }`}
                    />
                  </div>
                  <span
                    className={`font-medium text-sm text-center leading-snug ${
                      isSelected ? "text-blue-100" : "text-zinc-300"
                    }`}
                  >
                    {feature.name}
                  </span>

                  {isSelected && (
                    <motion.div
                      layoutId="activeTab"
                      className="absolute inset-0 border-2 border-blue-500/50 rounded-xl"
                      initial={false}
                      transition={{
                        type: "spring",
                        bounce: 0.2,
                        duration: 0.6,
                      }}
                    />
                  )}
                </motion.div>
              );
            })}
          </div>
        </div>

        {/* Content section with fixed height */}
        <div className="min-h-[500px]">
          <AnimatePresence mode="wait">
            <motion.div
              key={selectedFeature}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.4 }}
              className="rounded-xl overflow-hidden bg-gradient-to-b from-zinc-800/30 to-zinc-900/30 p-2 backdrop-blur-sm border border-zinc-800/50"
            >
              <div className="bg-zinc-900/30 rounded-xl p-8 h-full">
                <div className="flex flex-col md:flex-row gap-8">
                  <div className="flex-1">
                    {(() => {
                      const selectedFeatureDetails = features.find(
                        (f) => f.name === selectedFeature
                      );

                      if (!selectedFeatureDetails) return null;

                      const IconComponent =
                        featureIcons[selectedFeatureDetails.icon];

                      return (
                        <div className="flex flex-col">
                          <h3 className="flex items-center font-semibold text-white text-2xl mb-4">
                            {IconComponent && (
                              <IconComponent
                                size={24}
                                className="mr-4 text-white"
                              />
                            )}
                            {selectedFeatureDetails.name}
                          </h3>
                          <p className="text-gray-300 leading-relaxed">
                            {selectedFeatureDetails.description}
                          </p>
                          {selectedFeatureDetails.name === "Support" && (
                            <Link
                              href="mailto:<EMAIL>"
                              className="inline-block mt-4"
                              target="_blank"
                              rel="noopener noreferrer"
                            >
                              <Button className="text-white hover:text-white/90 border-zinc-700">
                                Contact Support
                              </Button>
                            </Link>
                          )}
                          {selectedFeatureDetails.name === "Innovation" && (
                            <Link
                              href="https://versatailor.featurebase.app/"
                              target="_blank"
                              rel="noopener noreferrer"
                              className="inline-block mt-4"
                            >
                              <Button className="text-white hover:text-white/90 border-zinc-700">
                                View Our Roadmap
                              </Button>
                            </Link>
                          )}
                        </div>
                      );
                    })()}
                  </div>
                  <div className="flex-1">
                    {(() => {
                      const selectedFeatureDetails = features.find(
                        (f) => f.name === selectedFeature
                      );

                      if (!selectedFeatureDetails?.hasDemo) return null;

                      if (selectedFeatureDetails.name === "Web Analytics") {
                        return (
                          <div className="rounded-lg bg-zinc-900/30 border border-zinc-800/50 p-4">
                            <div className="flex gap-3 mb-4 text-zinc-300 items-center">
                              <TbPresentationAnalytics className="text-white" />
                              <h3 className="text-zinc-100">
                                Track and analyze your website&apos;s
                                performance
                              </h3>
                            </div>
                            <VideoPlayer
                              path="s7C1zY5EjsMdh2x4fZSqwJOA8IN01GzrI01XP01xfGIDSs"
                              videoName="AnalyticsDemo"
                            />
                            <div className="flex justify-end mt-4"></div>
                          </div>
                        );
                      }

                      if (
                        selectedFeatureDetails.name === "Hour By Hour Analytics"
                      ) {
                        return (
                          <div className="rounded-lg bg-zinc-900/30 border border-zinc-800/50 p-4">
                            <div className="flex gap-3 mb-4 text-zinc-300 items-center">
                              <TbPresentationAnalytics className="text-white" />
                              <h3 className="text-zinc-100">
                                Better understand your spikes and traffic trends
                              </h3>
                            </div>
                            <VideoPlayer
                              path="rWnS02Fpala8Xx4RCabEm01YvdVDNZFCu5Dka00GuVQjx00"
                              videoName="hourlyFiltering"
                            />
                            <div className="flex justify-end mt-4"></div>
                          </div>
                        );
                      }

                      if (
                        selectedFeatureDetails.name === "Conversion Analytics"
                      ) {
                        return (
                          <div className="rounded-lg bg-zinc-900/30 border border-zinc-800/50 p-4">
                            <div className="flex gap-3 mb-4 text-zinc-300 items-center">
                              <TbPresentationAnalytics className="text-white" />
                              <h3 className="text-zinc-100">
                                Measure the TRUE conversion rate of your landing
                                page
                              </h3>
                            </div>
                            <VideoPlayer
                              path="rl7RrauXsOjjySEQbWDV02EAyoq00u5NVSn6IUnjNnZRk"
                              videoName="authedDemo"
                            />
                            <div className="flex justify-end mt-4"></div>
                          </div>
                        );
                      }
                      if (selectedFeatureDetails.name === "Autofill") {
                        return (
                          <div className="rounded-lg bg-zinc-900/30 border border-zinc-800/50 p-4">
                            <div className="flex gap-3 items-center mb-4">
                              <FaChartLine className="text-white" />
                              <h3 className="text-zinc-100">Autofill Demo</h3>
                            </div>
                            <Image
                              src="/autofill.gif"
                              alt="Autofilling demo GIF"
                              width={500}
                              height={300}
                              className="rounded-xl border border-zinc-800/50 shadow-2xl transition-all duration-300 hover:scale-[1.02]"
                              unoptimized
                            />
                            <div className="flex justify-end mt-4">
                              <Link href={"/dashboard"}>
                                <Button
                                  className="bg-white hover:bg-white/90 text-white-foreground"
                                  onClick={() => {
                                    window?.versatailor(
                                      "get_started_autofill",
                                      {
                                        description:
                                          "Clicked on get started button in the auto-fill section",
                                      }
                                    );
                                  }}
                                >
                                  Get Started
                                </Button>
                              </Link>
                            </div>
                          </div>
                        );
                      }
                    })()}
                  </div>
                </div>
              </div>
            </motion.div>
          </AnimatePresence>
        </div>
      </div>
    </section>
  );
}

export default FeaturesListicle;
