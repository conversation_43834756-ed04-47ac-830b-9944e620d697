import { NextRequest, NextResponse } from "next/server";
import { headers } from "next/headers";
import <PERSON><PERSON> from "stripe";
import connect from "@/util/db";
import Website from "@/models/website";
import Event from "@/models/event";
import { decryptApiKey, createStripeClient } from "@/lib/stripe-utils";

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ websiteId: string }> }
) {
  try {
    await connect();
    
    const { websiteId } = await params;
    const body = await request.text();
    const signature = (await headers()).get("stripe-signature");

    if (!signature) {
      return NextResponse.json(
        { error: "Missing stripe-signature header" },
        { status: 400 }
      );
    }

    // Find the website and verify it exists with revenue attribution enabled
    const website = await Website.findOne({ 
      id: websiteId, 
      revenueAttributionEnabled: true 
    }).select("+stripeWebhookSecret +stripeApiKey");

    if (!website) {
      return NextResponse.json(
        { error: "Website not found or revenue attribution not enabled" },
        { status: 404 }
      );
    }

    if (!website.stripeWebhookSecret) {
      return NextResponse.json(
        { error: "Webhook secret not configured" },
        { status: 400 }
      );
    }

    // Verify webhook signature
    let event: Stripe.Event;
    try {
      // Create temporary Stripe instance just for webhook verification
      const decryptedApiKey = decryptApiKey(website.stripeApiKey!);
      const stripe = createStripeClient(decryptedApiKey);
      
      event = stripe.webhooks.constructEvent(
        body,
        signature,
        website.stripeWebhookSecret
      );
    } catch (err: any) {
      console.error(`Webhook signature verification failed: ${err.message}`);
      return NextResponse.json(
        { error: "Webhook signature verification failed" },
        { status: 400 }
      );
    }

    // Handle relevant event types
    if (event.type === "checkout.session.completed") {
      await handleCheckoutSessionCompleted(event, websiteId);
    } else if (event.type === "invoice.payment_succeeded") {
      await handleInvoicePaymentSucceeded(event, websiteId);
    }

    return NextResponse.json({ received: true });

  } catch (error: any) {
    console.error("Revenue webhook error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

async function handleCheckoutSessionCompleted(
  event: Stripe.Event,
  websiteId: string
) {
  try {
    const session = event.data.object as Stripe.Checkout.Session;
    
    console.log(`Processing checkout session completed: ${session.id} for website: ${websiteId}`);
    
    // Check for idempotency - don't process the same event twice
    const existingEvent = await Event.findOne({
      "revenueData.stripeSessionId": session.id,
      websiteId
    });

    if (existingEvent) {
      console.log(`Revenue event already processed for session ${session.id}`);
      return;
    }

    // Extract visitor attribution from metadata
    const visitorId = session.metadata?.versatailor_visitor_id || session.metadata?.visitorId;
    const sessionId = session.metadata?.versatailor_session_id || session.metadata?.sessionId;

    let attributionData = null;

    // Try to find attribution data from visitor's session
    if (visitorId || sessionId) {
      attributionData = await findVisitorAttribution(websiteId, visitorId, sessionId);
    }

    // For checkout sessions, customer is always created if customer_email is provided
    // The customer exists in the session means it was either existing or newly created
    const isNewCustomer = session.customer_creation === "always" || 
                          (session.customer_creation === "if_required" && session.customer);

    // Get product information from subscription or fallback
    const productNames: string[] = [];
    
    // Since line_items aren't expanded in webhooks, we'll get product info differently
    if (session.mode === "subscription" && session.subscription) {
      // For subscriptions, we can't easily get product names from the webhook
      // We'll use a generic name or could expand the subscription in a separate call if needed
      productNames.push("Subscription");
    } else {
      // For one-time payments, also use generic name
      productNames.push("Product");
    }

    // Validate required fields before creating event
    if (!session.amount_total || session.amount_total <= 0) {
      console.error(`Invalid amount for session ${session.id}: ${session.amount_total}`);
      return;
    }

    // Create revenue event
    const revenueEventData = {
      type: "payment",
      websiteId,
      domain: attributionData?.domain || "unknown",
      href: attributionData?.href || "/",
      referrer: attributionData?.referrer || null,
      timestamp: new Date(event.created * 1000),
      viewport: attributionData?.viewport || null,
      location: attributionData?.location || null,
      visitorId: visitorId || `stripe_${session.customer || session.id}`,
      sessionId: sessionId || `stripe_session_${session.id}`,
      userAgent: attributionData?.userAgent || null,
      isBot: false,
      clientInfo: attributionData?.clientInfo || null,
      isAuthVisitor: false,
      revenueData: {
        amount: session.amount_total,
        currency: session.currency || "usd",
        stripeChargeId: null, // Will be populated from payment_intent if available
        stripeCustomerId: session.customer as string,
        stripeSessionId: session.id,
        customerEmail: session.customer_details?.email || session.customer_email || null,
        isNewCustomer,
        isRecurring: session.mode === "subscription",
        subscriptionId: session.subscription as string || undefined,
        productNames,
        paymentType: session.mode === "subscription" ? "subscription" : "one_time",
      },
    };

    const revenueEvent = new Event(revenueEventData);
    await revenueEvent.save();
    
    console.log(`Revenue event created successfully for checkout session ${session.id}`);
  } catch (error: any) {
    const session = event.data.object as Stripe.Checkout.Session;
    console.error(`Error processing checkout session ${session.id}:`, error);
    console.error(`Error details:`, {
      message: error.message,
      stack: error.stack,
      websiteId,
      sessionId: session.id
    });
    throw error; // Re-throw to trigger 500 response
  }
}

async function handleInvoicePaymentSucceeded(
  event: Stripe.Event,
  websiteId: string
) {
  try {
    const invoice = event.data.object as Stripe.Invoice;
    
    console.log(`Processing invoice payment succeeded: ${invoice.id} for website: ${websiteId}`);
    
    // Check for idempotency
    const existingEvent = await Event.findOne({
      "revenueData.stripeChargeId": invoice.charge,
      websiteId
    });

    if (existingEvent) {
      console.log(`Revenue event already processed for invoice ${invoice.id}`);
      return;
    }

    // For subscription renewals, try to find original attribution
    let attributionData = null;
    if (invoice.customer) {
      attributionData = await findCustomerAttribution(websiteId, invoice.customer as string);
    }

    // Validate required fields
    if (!invoice.amount_paid || invoice.amount_paid <= 0) {
      console.error(`Invalid amount for invoice ${invoice.id}: ${invoice.amount_paid}`);
      return;
    }

    // Create revenue event for subscription renewal
    const revenueEventData = {
      type: "payment",
      websiteId,
      domain: attributionData?.domain || "unknown",
      href: attributionData?.href || "/",
      referrer: attributionData?.referrer || null,
      timestamp: new Date(event.created * 1000),
      viewport: attributionData?.viewport || null,
      location: attributionData?.location || null,
      visitorId: attributionData?.visitorId || `stripe_customer_${invoice.customer}`,
      sessionId: attributionData?.sessionId || `stripe_invoice_${invoice.id}`,
      userAgent: attributionData?.userAgent || null,
      isBot: false,
      clientInfo: attributionData?.clientInfo || null,
      isAuthVisitor: false,
      revenueData: {
        amount: invoice.amount_paid,
        currency: invoice.currency || "usd",
        stripeChargeId: invoice.charge as string,
        stripeCustomerId: invoice.customer as string,
        stripeSessionId: null,
        customerEmail: invoice.customer_email || null,
        isNewCustomer: false,
        isRecurring: true,
        subscriptionId: invoice.subscription as string || undefined,
        productNames: invoice.lines.data.map(line => line.description || "Subscription").filter(Boolean),
        paymentType: "renewal",
      },
    };

    const revenueEvent = new Event(revenueEventData);
    await revenueEvent.save();
    
    console.log(`Revenue event created successfully for invoice payment ${invoice.id}`);
  } catch (error: any) {
    const invoice = event.data.object as Stripe.Invoice;
    console.error(`Error processing invoice payment ${invoice.id}:`, error);
    console.error(`Error details:`, {
      message: error.message,
      stack: error.stack,
      websiteId,
      invoiceId: invoice.id
    });
    throw error; // Re-throw to trigger 500 response
  }
}

async function findVisitorAttribution(
  websiteId: string,
  visitorId?: string,
  sessionId?: string
) {
  // Try to find the most recent non-payment event for this visitor/session
  const query: any = { websiteId, type: { $ne: "payment" } };
  
  if (visitorId) {
    query.visitorId = visitorId;
  } else if (sessionId) {
    query.sessionId = sessionId;
  } else {
    return null;
  }

  const recentEvent = await Event.findOne(query)
    .sort({ timestamp: -1 })
    .limit(1);

  if (!recentEvent) return null;

  return {
    domain: recentEvent.domain,
    href: recentEvent.href,
    referrer: recentEvent.referrer,
    viewport: recentEvent.viewport,
    location: recentEvent.location,
    userAgent: recentEvent.userAgent,
    clientInfo: recentEvent.clientInfo,
    visitorId: recentEvent.visitorId,
    sessionId: recentEvent.sessionId,
  };
}

async function findCustomerAttribution(websiteId: string, customerId: string) {
  // Find the first event for this customer to get original attribution
  const firstEvent = await Event.findOne({
    websiteId,
    "revenueData.stripeCustomerId": customerId
  }).sort({ timestamp: 1 });

  if (!firstEvent) return null;

  return {
    domain: firstEvent.domain,
    href: firstEvent.href,
    referrer: firstEvent.referrer,
    viewport: firstEvent.viewport,
    location: firstEvent.location,
    userAgent: firstEvent.userAgent,
    clientInfo: firstEvent.clientInfo,
    visitorId: firstEvent.visitorId,
    sessionId: firstEvent.sessionId,
  };
} 