import { ChartDataPoint, Period } from "@/lib/types";
import { logger } from "@/lib/utils/logger";
import React, { useEffect, useState } from "react";
import {
  Area,
  Bar,
  CartesianGrid,
  ResponsiveContainer,
  Scatter,
  ScatterProps,
  Tooltip,
  XAxis,
  YAxis,
  ReferenceDot,
  ComposedChart,
  Line,
} from "recharts";
import { useFilterState } from "@/hooks/useFilterState";
import type {
  RevenueFilters,
  RevenueMetrics,
} from "@/hooks/useRevenueAnalytics";

interface ChartProps {
  chartData: ChartDataPoint[];
  xAxisInterval: number;
  selectedPeriod: Period;
  startDate?: Date;
  endDate?: Date;
  websiteId: string;
  customPeriod?: {
    startDate: Date;
    endDate: Date;
  };
  revenueFilters?: RevenueFilters;
  revenueData?: RevenueMetrics | null;
  isRevenueLoading?: boolean;
  isRevenueEnabled?: boolean;
}

// Custom tooltip for combined chart
const CombinedTooltip = ({ active, payload, label }: any) => {
  if (active && payload && payload.length) {
    const visitorsData = payload.find((p: any) => p.dataKey === "visitors");
    const revenueData = payload.find((p: any) => p.dataKey === "revenue");

    logger.debug("Tooltip payload:", payload);
    logger.debug("Revenue data:", revenueData);

    return (
      <div className="bg-[#363636] border border-gray-700/50 rounded-lg p-2 sm:p-3 shadow-lg max-w-[200px] sm:max-w-none">
        <p className="text-gray-300 text-xs sm:text-sm font-medium mb-1 sm:mb-2 truncate">
          {label}
        </p>

        {visitorsData && (
          <div className="flex items-center gap-1 sm:gap-2 mb-1">
            <div className="w-2 h-2 sm:w-3 sm:h-3 rounded-full bg-blue-400 shrink-0"></div>
            <span className="text-blue-400 font-medium text-xs sm:text-sm truncate">
              Visitors: {visitorsData.value || 0}
            </span>
          </div>
        )}

        {revenueData && (
          <div className="flex items-center gap-1 sm:gap-2 mb-1">
            <div className="w-2 h-2 sm:w-3 sm:h-3 rounded bg-green-400 shrink-0"></div>
            <span className="text-green-400 font-medium text-xs sm:text-sm truncate">
              Revenue:{" "}
              {new Intl.NumberFormat("en-US", {
                style: "currency",
                currency: "USD",
                notation: window.innerWidth < 640 ? "compact" : "standard",
                minimumFractionDigits: 2,
                maximumFractionDigits: 2,
              }).format(revenueData.value / 100)}
            </span>
          </div>
        )}

        {revenueData && revenueData.payload && (
          <p className="text-gray-400 text-xs mt-1">
            Orders: {revenueData.payload.orders || 0}
          </p>
        )}
      </div>
    );
  }
  return null;
};

function Chart({
  chartData,
  xAxisInterval,
  selectedPeriod,
  startDate,
  endDate,
  websiteId,
  customPeriod,
  revenueFilters,
  revenueData,
  isRevenueLoading = false,
  isRevenueEnabled = false,
}: ChartProps) {
  // Add debug logs for data processing
  logger.debug('Debug - Chart Component:', {
    selectedPeriod,
    startDate: startDate?.toISOString(),
    endDate: endDate?.toISOString(),
    customPeriod: {
      startDate: customPeriod?.startDate?.toISOString(),
      endDate: customPeriod?.endDate?.toISOString()
    }
  });

  logger.debug('Debug - Chart Data:', chartData.map(point => ({
    time: point.time,
    visitors: point.visitors,
    localEquivalent: new Date().toLocaleString()
  })));

  const { handleFilterClick } = useFilterState();
  const [isSmallScreen, setIsSmallScreen] = useState(false);
  const [isMediumScreen, setIsMediumScreen] = useState(false);
  const [currentHourPoint, setCurrentHourPoint] =
    useState<ChartDataPoint | null>(null);
  const [showReferenceDot, setShowReferenceDot] = useState(false);
  const [combinedChartData, setCombinedChartData] = useState(chartData);

  // Add function to calculate axis domains
  const calculateDomains = () => {
    const maxVisitors = Math.max(...combinedChartData.map(d => d.visitors || 0));
    const maxRevenue = Math.max(...combinedChartData.map(d => d.revenue || 0));
    
    // For visitors, ensure domain starts at 0 and has a nice upper bound
    const visitorsDomain = [0, Math.max(1, Math.ceil(maxVisitors * 1.1))];
    
    // For revenue, ensure domain starts at 0 and has a nice upper bound
    const revenueDomain = [0, Math.max(1, Math.ceil(maxRevenue * 4))];
    
    return { visitorsDomain, revenueDomain };
  };

  // Check for screen sizes on mount and window resize
  useEffect(() => {
    const checkScreenSize = () => {
      setIsSmallScreen(window.matchMedia("(max-width: 640px)").matches);
      setIsMediumScreen(
        window.matchMedia("(min-width: 641px) and (max-width: 1024px)").matches
      );
    };

    // Initial check
    checkScreenSize();

    // Add listener for window resize
    window.addEventListener("resize", checkScreenSize);

    // Cleanup
    return () => window.removeEventListener("resize", checkScreenSize);
  }, []);

  // Find current hour point and set delay for showing reference dot
  useEffect(() => {
    setShowReferenceDot(false);

    if (selectedPeriod === "today") {
      const currentHour = new Date().getHours();
      let timeString;
      if (currentHour === 0) {
        timeString = "12am";
      } else if (currentHour === 12) {
        timeString = "12pm";
      } else if (currentHour > 12) {
        timeString = `${currentHour - 12}pm`;
      } else {
        timeString = `${currentHour}am`;
      }

      const point = chartData.find((point) => point.time === timeString);
      setCurrentHourPoint(point || null);

      // Add delay before showing reference dot
      const timer = setTimeout(() => {
        setShowReferenceDot(true);
      }, 1500);

      return () => clearTimeout(timer);
    } else {
      setCurrentHourPoint(null);
    }
  }, [chartData, selectedPeriod]);

  // Helper function to check if we're viewing a single day
  const isSingleDayView = () => {
    if (["today", "yesterday"].includes(selectedPeriod)) return true;
    if (selectedPeriod === "custom" && startDate && endDate) {
      return startDate.toDateString() === endDate.toDateString();
    }
    return false;
  };

  // Calculate x-axis interval based on screen size and view type
  const getXAxisInterval = () => {
    if (isSingleDayView()) {
      if (isSmallScreen) {
        return 5; // Show every 6th hour on mobile
      }
      if (isMediumScreen) {
        return 3; // Show every 4th hour on tablets
      }
    }
    return xAxisInterval;
  };

  // Handler for clicking on chart data points
  function handleChartClick(data: any) {
    // Allow hour filtering for single day views on larger screens
    const allowHourFiltering = isSingleDayView() && !isSmallScreen;

    if (!allowHourFiltering) return;

    if (data && data.activePayload && data.activePayload.length > 0) {
      const clickedPoint = data.activePayload[0].payload;

      // Only apply filter if we have visitor data for this hour
      if (clickedPoint.visitors !== null) {
        // Set the clicked hour as an active filter
        handleFilterClick(
          "hour",
          clickedPoint.time,
          `Hour: ${clickedPoint.time}`
        );
      }
    }
  }

  const shouldEnableHourFiltering = isSingleDayView() && !isSmallScreen;

  // Fetch revenue chart data when needed
  useEffect(() => {
    if (isRevenueEnabled && websiteId) {
      const fetchRevenueChartData = async () => {
        try {
          const searchParams = new URLSearchParams({
            websiteId,
            period: selectedPeriod,
          });

          if (customPeriod) {
            searchParams.set("startDate", customPeriod.startDate.toISOString());
            searchParams.set("endDate", customPeriod.endDate.toISOString());
          }

          // Add revenue filters
          if (revenueFilters) {
            if (revenueFilters.amountRange.min !== null) {
              searchParams.set(
                "minAmount",
                revenueFilters.amountRange.min.toString()
              );
            }
            if (revenueFilters.amountRange.max !== null) {
              searchParams.set(
                "maxAmount",
                revenueFilters.amountRange.max.toString()
              );
            }
            if (revenueFilters.customerType !== "all") {
              searchParams.set("customerType", revenueFilters.customerType);
            }
            if (revenueFilters.paymentMethod !== "all") {
              searchParams.set("paymentMethod", revenueFilters.paymentMethod);
            }
            if (revenueFilters.paymentType !== "all") {
              searchParams.set("paymentType", revenueFilters.paymentType);
            }
            if (revenueFilters.currency !== "all") {
              searchParams.set("currency", revenueFilters.currency);
            }
            if (revenueFilters.dateRange.start) {
              searchParams.set(
                "revenueStartDate",
                revenueFilters.dateRange.start.toISOString()
              );
            }
            if (revenueFilters.dateRange.end) {
              searchParams.set(
                "revenueEndDate",
                revenueFilters.dateRange.end.toISOString()
              );
            }
          }

          const response = await fetch(
            `/api/analytics/revenue-chart?${searchParams.toString()}`
          );
          if (response.ok) {
            const data = await response.json();
            logger.debug("Revenue chart data:", data);
            
            const mappedData = chartData.map((point) => {
              const revenuePoint = data.find((d: any) => d.time === point.time);
              logger.debug("Mapping point:", {
                time: point.time,
                revenue: revenuePoint?.revenue,
              });
              return {
                ...point,
                revenue: revenuePoint?.revenue || 0,
                orders: revenuePoint?.orders || 0,
              };
            });
            logger.debug("Mapped chart data:", mappedData);
            setCombinedChartData(mappedData);
          } else {
            logger.error(
              "Failed to fetch revenue chart data:",
              await response.text()
            );
          }
        } catch (error) {
          logger.error("Failed to fetch revenue chart data:", error);
          // Fallback to empty revenue data
          setCombinedChartData(
            chartData.map((point) => ({
              ...point,
              revenue: 0,
              orders: 0,
            }))
          );
        }
      };

      fetchRevenueChartData();
    } else {
      // If revenue is not enabled, just use visitor data
      setCombinedChartData(
        chartData.map((point) => ({
          ...point,
          revenue: 0,
          orders: 0,
        }))
      );
    }
  }, [
    isRevenueEnabled,
    websiteId,
    selectedPeriod,
    customPeriod,
    revenueFilters,
    chartData,
  ]);

  return (
    <div className="space-y-3 sm:space-y-4" key={`chart-${websiteId}`}>
      {/* Chart Legend and Summary */}
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-3">
        <div className="flex items-center gap-4 sm:gap-6">
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 rounded-full bg-blue-400 shrink-0"></div>
            <span className="text-sm text-gray-400">Visitors</span>
          </div>
          {/* Always show revenue legend but grey out if disabled */}
          <div className="group relative">
            <div
              className={`flex items-center gap-2 ${!isRevenueEnabled ? "opacity-50 cursor-help" : ""}`}
            >
              <div className="w-3 h-3 rounded bg-green-400 shrink-0"></div>
              <span className="text-sm text-gray-400">Revenue</span>
            </div>
            {!isRevenueEnabled && (
              <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-gray-800 text-white text-xs rounded-lg shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-10">
                Enable revenue attribution in Settings → Revenue
                <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-800"></div>
              </div>
            )}
          </div>
        </div>

        {/* Always show revenue summary but grey out if disabled */}
        <div className="group relative">
          <div
            className={`text-sm text-gray-400 truncate ${!isRevenueEnabled ? "opacity-50 cursor-help" : ""}`}
          >
            <span className="hidden sm:inline">Total Revenue: </span>
            <span className="font-medium">
              {!isRevenueEnabled
                ? "$0.00"
                : revenueData
                  ? new Intl.NumberFormat("en-US", {
                      style: "currency",
                      currency: revenueData.totalRevenue.currency.toUpperCase(),
                      minimumFractionDigits: 2,
                      maximumFractionDigits: 2,
                    }).format(revenueData.totalRevenue.current)
                  : "$0.00"}
            </span>
          </div>
          {!isRevenueEnabled && (
            <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-gray-800 text-white text-xs rounded-lg shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-10">
              Enable revenue attribution in Settings → Revenue
              <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-800"></div>
            </div>
          )}
        </div>
      </div>

      <div className="h-[200px] sm:h-[250px] md:h-[300px] lg:h-[350px]">
        <ResponsiveContainer width="100%" height="100%">
          <ComposedChart
            data={combinedChartData}
            onClick={handleChartClick}
            margin={{
              top: 5,
              right: isRevenueEnabled ? 20 : 5,
              left: 5,
              bottom: 5,
            }}
            style={{
              cursor: shouldEnableHourFiltering ? "pointer" : "default",
            }}
          >
            <defs>
              <linearGradient id="colorVisitors" x1="0" y1="0" x2="0" y2="1">
                <stop
                  offset="5%"
                  stopColor="rgb(59, 130, 246)"
                  stopOpacity={0.1}
                />
                <stop
                  offset="95%"
                  stopColor="rgb(59, 130, 246)"
                  stopOpacity={0}
                />
              </linearGradient>
              <linearGradient id="colorRevenue" x1="0" y1="0" x2="0" y2="1">
                <stop
                  offset="0%"
                  stopColor="rgb(34, 197, 94)"
                  stopOpacity={1}
                />
                <stop
                  offset="100%"
                  stopColor="rgb(34, 197, 94)"
                  stopOpacity={1}
                />
              </linearGradient>
            </defs>

            <CartesianGrid
              strokeDasharray="3 3"
              vertical={false}
              stroke="rgba(255, 255, 255, 0.1)"
            />

            <XAxis
              dataKey="time"
              stroke="#9CA3AF"
              tick={{
                fill: "#9CA3AF",
                fontSize: isSmallScreen ? 11 : 12,
              }}
              axisLine={{ stroke: "rgba(255, 255, 255, 0.1)" }}
              interval={getXAxisInterval()}
              minTickGap={isSmallScreen ? 10 : 5}
              tickMargin={5}
              height={isSmallScreen ? 40 : 50}
              tickFormatter={(value) => value}
            />

            {/* Left Y-axis for visitors */}
            <YAxis
              yAxisId="visitors"
              stroke="#9CA3AF"
              tick={{
                fill: "#9CA3AF",
                fontSize: isSmallScreen ? 11 : 12,
              }}
              axisLine={{ stroke: "rgba(255, 255, 255, 0.1)" }}
              allowDecimals={false}
              orientation="left"
              width={isSmallScreen ? 40 : 60}
              domain={calculateDomains().visitorsDomain}
            />

            {/* Right Y-axis for revenue */}
            <YAxis
              yAxisId="revenue"
              stroke={!isRevenueEnabled ? "#4B5563" : "#9CA3AF"}
              tick={{ 
                fill: !isRevenueEnabled ? "#4B5563" : "#9CA3AF",
                fontSize: isSmallScreen ? 10 : 12,
              }}
              axisLine={{ stroke: "rgba(255, 255, 255, 0.1)" }}
              allowDecimals={true}
              orientation="right"
              width={isSmallScreen ? 50 : 70}
              domain={calculateDomains().revenueDomain}
              tickFormatter={
                (value) =>
                  isSmallScreen
                    ? // Shorter format on mobile
                      new Intl.NumberFormat("en-US", {
                        style: "currency",
                        currency: "USD",
                        notation: "compact",
                        minimumFractionDigits: 2,
                        maximumFractionDigits: 2,
                      }).format(value / 100)
                    : // Full format on larger screens
                      new Intl.NumberFormat("en-US", {
                        style: "currency",
                        currency: "USD",
                        minimumFractionDigits: 2,
                        maximumFractionDigits: 2,
                      }).format(value / 100)
              }
            />

            <Tooltip content={<CombinedTooltip />} />

            {/* Revenue bars (behind the line) - always show but grey out if disabled */}
            <Bar
              yAxisId="revenue"
              dataKey="revenue"
              name="Revenue"
              fill={
                !isRevenueEnabled
                  ? "rgba(75, 85, 99, 0.3)"
                  : "url(#colorRevenue)"
              }
              radius={[2, 2, 0, 0]}
              isAnimationActive={isRevenueEnabled}
              opacity={!isRevenueEnabled ? 0.3 : 0.7}
            />

            {/* Visitors area chart */}
            <Area
              yAxisId="visitors"
              type="monotone"
              dataKey="visitors"
              name="Visitors"
              stroke="rgb(59, 130, 246)"
              fillOpacity={1}
              fill="url(#colorVisitors)"
              strokeWidth={2}
              isAnimationActive={true}
            />

            {/* Reference dot for current hour */}
            {showReferenceDot &&
              currentHourPoint &&
              currentHourPoint.visitors !== null && (
                <ReferenceDot
                  yAxisId="visitors"
                  x={currentHourPoint.time}
                  y={currentHourPoint.visitors}
                  r={6}
                  fill="rgb(59, 130, 246)"
                  stroke="#fff"
                  isFront={true}
                />
              )}
          </ComposedChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
}

export default Chart;
