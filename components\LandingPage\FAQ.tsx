"use client";

import config from "@/config";
import Image from "next/image";
import { useRef, useState } from "react";
import type { JSX } from "react";
import { Tooltip } from "react-tooltip";

// <FAQ> component is a lsit of <Item> component
// Just import the FAQ & add your FAQ content to the const faqList arrayy below.

interface FAQItemProps {
  question: string;
  answer: JSX.Element;
}

const faqList: FAQItemProps[] = [
  {
    question: `What exactly does ${config.appName} offer?`,
    answer: (
      <div className="space-y-2 leading-relaxed">
        {config.appName} offers three powerful features:
        <ul className="list-disc pl-5 mt-2">
          <li>
            <strong>Web Analytics:</strong> Track your website traffic and
            analyze user behavior to make data-driven decisions.
          </li>
          <li>
            <strong>Hour-by-Hour Analytics:</strong> Get detailed hourly
            breakdowns to understand traffic spikes and engagement patterns.
          </li>
          <li>
            <strong>Conversion Analytics:</strong> Filter out authenticated
            visits to measure your landing page&apos;s true conversion rate.
          </li>
        </ul>
      </div>
    ),
  },
  {
    question: "How can I get two months for free?",
    answer: (
      <p>
        If you sign up for the yearly plan, you will pay a significantly
        discounted price which will be the equivalent of having 2+ months for
        free.
      </p>
    ),
  },
  {
    question: "How is pricing structured?",
    answer: (
      <div className="space-y-2">
        <p>
          Our pricing is based on monthly events volume, starting from $9/month
          for 10k events. We offer flexible plans that scale with your needs:
        </p>
        <ul className="list-disc pl-5 mt-2">
          <li>10k events: $9/month</li>
          <li>100k events: $19/month</li>
          <li>200k events: $29/month</li>
          <li>And more plans for higher volumes</li>
        </ul>
        <p className="mt-2">
          We also offer yearly billing with 2 months free. You can start with a
          14-day free trial to test our features.
        </p>
      </div>
    ),
  },
  {
    question: "What counts as an event?",
    answer: (
      <p>
        Events are user interactions tracked on your website, such as page
        views, clicks, or custom actions. Our platform allows you to track these
        interactions to understand how users engage with your site.
        <br />
        <br />
        You can monitor these events through our analytics dashboard.
      </p>
    ),
  },
  {
    question: "Can I filter out authenticated users from my analytics?",
    answer: (
      <p>
        Yes! This is one of our key features. You can exclude authenticated
        users from your analytics data to get a clear view of how new visitors
        interact with your site.
        <br />
        <br />
        This helps you understand your true conversion rate and landing page
        performance without the distillation of returning authenticated users.
      </p>
    ),
  },
  {
    question: `How is ${config.appName} different from other analytics platforms?`,
    answer: (
      <div className="space-y-2">
        <p>Here&apos;s what makes {config.appName} stand out:</p>
        <ul className="list-disc pl-5 mt-2">
          <li>
            Conversion tracking: You can track the conversion rate of your
            landing page by excluding authenticated users.
          </li>
          <li>
            Hour-by-hour traffic breakdown: You can click on any hour to see the
            breakdown of the traffic for that hour. Helpful for understanding
            spikes and trends
          </li>
          <li>
            We are constantly innovating and adding new features. You can view
            our complete roadmap at{" "}
            <a
              href="https://versatailor.featurebase.app"
              target="_blank"
              className="text-white underline"
            >
              our feature roadmap
            </a>
            .
          </li>
        </ul>
        <p>
          You can filter and analyze this data to make informed decisions about
          your business.
        </p>
      </div>
    ),
  },
  {
    question: `Is ${config.appName} GDPR compliant?`,
    answer: (
      <div className="space-y-2">
        <p>
          Yes, {config.appName} is GDPR compliant!
          <br />
          <br />
          Data privacy and security are a top priority for us. We prioritize
          data privacy and security, and go above and beyond to ensure that all
          data we collect is processed under the GDPR regulations.
          <br />
        </p>
      </div>
    ),
  },
  {
    question: `Do I need coding skills to use ${config.appName}?`,
    answer: (
      <div className="space-y-2">
        <p>
          Nope, {config.appName} doesn&apos;t require you to have any coding
          skills.
          <br />
          <br />
          Setting it up takes less than 2 minutes.
          <br />
        </p>
      </div>
    ),
  },
  {
    question: "I have more questions",
    answer: (
      <p>
        Feel free to reach out to our support team at any time via email at{" "}
        <a
          href="mailto:<EMAIL>"
          className="text-white underline"
        >
          <EMAIL>
        </a>
      </p>
    ),
  },
];

// Modified FaqItem props
interface FaqItemPropsExtended extends FAQItemProps {
  isOpen: boolean;
  onClick: () => void;
}

// Corrected function signature to destructure props directly
function FaqItem({ question, answer, isOpen, onClick }: FaqItemPropsExtended) {
  const accordion = useRef<HTMLDivElement>(null);
  // Removed local isOpen state

  return (
    <li className="mb-2">
      {" "}
      {/* Added margin-bottom */}
      <button
        className={`relative flex gap-4 items-center w-full px-6 py-4 text-base font-semibold text-left rounded-lg transition-colors duration-150 md:text-lg ${
          isOpen ? "bg-neutral-700" : "bg-neutral-800 hover:bg-neutral-700"
        }`} // Added bg, padding, rounded, conditional bg, removed border
        // Updated onClick handler
        onClick={(e) => {
          e.preventDefault();
          onClick(); // Call the passed handler
          // Removed local setIsOpen call
          // Use destructured 'question' prop
          window?.versatailor(`opened_${question}_faq`, {
            description: `Opened ${question} FAQ`,
          });
        }}
        aria-expanded={isOpen} // Use passed isOpen prop
      >
        {/* Adjusted text color and removed conditional color change */}
        {/* Use destructured 'question' prop */}
        <span className="flex-1 text-neutral-200">{question}</span>
        {/* Adjusted icon color */}
        <svg
          className={`flex-shrink-0 w-4 h-4 ml-auto fill-current text-neutral-400`}
          viewBox="0 0 16 16"
          xmlns="http://www.w3.org/2000/svg"
        >
          <rect
            y="7"
            width="16"
            height="2"
            rx="1"
            className={`transform origin-center transition duration-200 ease-out ${
              isOpen && "rotate-180"
            }`}
          />
          <rect
            y="7"
            width="16"
            height="2"
            rx="1"
            className={`transform origin-center rotate-90 transition duration-200 ease-out ${
              isOpen && "rotate-180 hidden"
            }`}
          />
        </svg>
      </button>
      {/* Added padding-left to align answer with question text */}
      <div
        ref={accordion}
        className={`transition-all duration-300 ease-in-out opacity-80 overflow-hidden pl-6`}
        style={
          isOpen
            ? { maxHeight: accordion.current?.scrollHeight ?? 0, opacity: 1 }
            : { maxHeight: 0, opacity: 0 }
        }
      >
        {/* Added padding top/bottom and adjusted text color */}
        {/* Use destructured 'answer' prop */}
        <div className="py-4 leading-relaxed text-neutral-300">{answer}</div>
      </div>
    </li>
  );
}

function FAQ() {
  // Add state for the open index
  const [openIndex, setOpenIndex] = useState<number | null>(null);

  // Handler to toggle accordion items
  const handleToggle = (index: number) => {
    setOpenIndex(openIndex === index ? null : index);
  };

  return (
    <section className="bg-black-bg" id="faq">
      <div className="py-24 px-8 max-w-7xl mx-auto flex flex-col md:flex-row gap-12">
        <div className="flex flex-col text-left basis-1/2">
          <p className="inline-block font-semibold text-white mb-4">FAQ</p>
          <p className="sm:text-4xl text-3xl font-extrabold text-base-content">
            Frequently Asked Questions
          </p>
          <p className="text-xl font-bold mt-8">
            Have another concern? You can always reach out to me on{" "}
            <a
              className="text-blue-500 cursor-pointer hover:text-blue-300"
              href="https://twitter.com/ziadbeshippin"
              target="_blank"
            >
              Twitter{" "}
            </a>
            or{" "}
            <a
              className="text-blue-500 cursor-pointer hover:text-blue-300"
              href={`mailto:${config.emails.supportEmail}`}
            >
              Email
            </a>
          </p>
        </div>

        <ul className="basis-1/2">
          {/* Pass isOpen status and handler to FaqItem, spread item props */}
          {faqList.map((item, i) => (
            <FaqItem
              key={i}
              {...item} // Spread question and answer
              isOpen={openIndex === i}
              onClick={() => handleToggle(i)}
            />
          ))}
        </ul>
      </div>
    </section>
  );
}

export default FAQ;
