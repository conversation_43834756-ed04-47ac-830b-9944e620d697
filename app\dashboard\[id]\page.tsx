"use client";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { useWebsiteData } from "@/hooks/useWebsiteData";
import { useAnalyticsData } from "@/hooks/useAnalyticsData";
import { useUIState } from "@/hooks/useUIState";
import { useBrowserIcon } from "@/hooks/useBrowserIcon";
import { useDeviceIcon } from "@/hooks/useDeviceIcon";
import { WebsiteDashboardSkeleton } from "@/components/Skeletons";
import { useState, useMemo, useRef, useEffect, use } from "react";
import { useCountryCode } from "@/hooks/useCountryCode";
import { useCustomEvents } from "@/hooks/useCustomEvents";
import { useFilterState } from "@/hooks/useFilterState";
import { useGroupedData } from "@/hooks/useGroupedData";
import FilterIndicator from "@/components/FilterIndicator";
import { useModalState } from "@/hooks/useModalState";
import BackArrow from "@/components/BackArrow";
import WebsiteDropdown from "@/components/WebsiteDropdown";
import PeriodDropdown from "@/components/PeriodDropdown";
import HeaderList from "@/components/HeaderList";
import Chart from "@/components/Chart";
import LocationSection from "@/components/LocationSection";
import PageSection from "@/components/PageSection";
import SysInfoSection from "@/components/SysInfoSection";
import CustomEventsSection from "@/components/CustomEventsSection";
import JourneyModal from "@/components/JourneyModal";
import JourneyListModal from "@/components/JourneyListModal";
import ReferrerSection from "@/components/ReferrerSection";
import LocationModal from "@/components/LocationModal";
import PageModal from "@/components/PageModal";
import ReferrerModal from "@/components/ReferrerModal";
import SystemModal from "@/components/SystemModal";
import RevenueFilters from "@/components/RevenueFilters";
import type { RevenueFilters as RevenueFiltersType } from "@/hooks/useRevenueAnalytics";
import { useRevenueAnalytics } from "@/hooks/useRevenueAnalytics";

export default function WebsiteDashboard({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const resolvedParams = use(params);
  const router = useRouter();
  const {
    website,
    websites,
    isLoading: isWebsiteLoading,
    error,
  } = useWebsiteData(resolvedParams.id);
  const {
    isDropdownOpen,
    setIsDropdownOpen,
    isPeriodDropdownOpen,
    setIsPeriodDropdownOpen,
    selectedPeriod,
    setSelectedPeriod,
    customPeriod,
    setCustomPeriod,
    xAxisInterval,
    dropdownRef,
    periodDropdownRef,
  } = useUIState();
  // Add state for custom events
  const { activeFilters, handleFilterClick, clearAllFilters } =
    useFilterState();
  const {
    customEvents,
    isLoadingEvents,
    selectedEventJourneys,
    handleShowJourneyList,
  } = useCustomEvents(
    resolvedParams.id,
    selectedPeriod,
    activeFilters,
    customPeriod
  );

  // Revenue filter state
  const [revenueFilters, setRevenueFilters] = useState<RevenueFiltersType>({
    amountRange: { min: null, max: null },
    customerType: "all",
    paymentMethod: "all",
    paymentType: "all",
    currency: "all",
    dateRange: { start: null, end: null },
  });

  const [showRevenueFilters, setShowRevenueFilters] = useState(false);

  const {
    showReferrerModal,
    setShowReferrerModal,
    showPageModal,
    setShowPageModal,
    showLocationModal,
    setShowLocationModal,
    showSystemModal,
    setShowSystemModal,
    showJourneyModal,
    setShowJourneyModal,
    showJourneyListModal,
    setShowJourneyListModal,
    locationView,
    setLocationView,
    systemView,
    setSystemView,
  } = useModalState();

  const [selectedJourney, setSelectedJourney] = useState<{
    eventName: string;
    visitorId: string;
    timestamp: string;
  } | null>(null);
  const [journeyEvents, setJourneyEvents] = useState<any[]>([]);
  const [isLoadingJourney, setIsLoadingJourney] = useState(false);
  const [currentPageView, setCurrentPageView] = useState<"all" | "entry" | "exit" | "exit-links">("all");

  const referrerModalRef = useRef<HTMLDivElement>(null);
  const pageModalRef = useRef<HTMLDivElement>(null);
  const locationModalRef = useRef<HTMLDivElement>(null);
  const systemModalRef = useRef<HTMLDivElement>(null);
  const journeyModalRef = useRef<HTMLDivElement>(null);

  const journeyListModalRef = useRef<HTMLDivElement>(null);

  // Revenue filter handlers
  const handleRevenueFiltersChange = (newFilters: RevenueFiltersType) => {
    setRevenueFilters(newFilters);
  };

  const handleApplyRevenueFilters = () => {
    // The filters are already applied via state, no additional action needed
    setShowRevenueFilters(false);
  };

  const handleClearRevenueFilters = () => {
    setRevenueFilters({
      amountRange: { min: null, max: null },
      customerType: "all",
      paymentMethod: "all",
      paymentType: "all",
      currency: "all",
      dateRange: { start: null, end: null },
    });
  };

  // Check if revenue filters are active
  const hasActiveRevenueFilters = useMemo(() => {
    return (
      revenueFilters.amountRange.min !== null ||
      revenueFilters.amountRange.max !== null ||
      revenueFilters.customerType !== "all" ||
      revenueFilters.paymentMethod !== "all" ||
      revenueFilters.paymentType !== "all" ||
      revenueFilters.currency !== "all" ||
      revenueFilters.dateRange.start !== null ||
      revenueFilters.dateRange.end !== null
    );
  }, [revenueFilters]);

  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (
        showReferrerModal &&
        referrerModalRef.current &&
        !referrerModalRef.current.contains(event.target as Node)
      ) {
        setShowReferrerModal(false);
      }
      if (
        showPageModal &&
        pageModalRef.current &&
        !pageModalRef.current.contains(event.target as Node)
      ) {
        setShowPageModal(false);
      }
      if (
        showLocationModal &&
        locationModalRef.current &&
        !locationModalRef.current.contains(event.target as Node)
      ) {
        setShowLocationModal(false);
      }
      if (
        showSystemModal &&
        systemModalRef.current &&
        !systemModalRef.current.contains(event.target as Node)
      ) {
        setShowSystemModal(false);
      }
      if (
        showJourneyModal &&
        journeyModalRef.current &&
        !journeyModalRef.current.contains(event.target as Node)
      ) {
        setShowJourneyModal(false);
      }
      if (
        showJourneyListModal &&
        journeyListModalRef.current &&
        !journeyListModalRef.current.contains(event.target as Node)
      ) {
        setShowJourneyListModal(false);
      }
    }

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [
    showReferrerModal,
    showPageModal,
    showLocationModal,
    showSystemModal,
    showJourneyModal,
    showJourneyListModal,
  ]);

  const {
    isLoading: isAnalyticsLoading,
    chartData,
    uniqueVisitors,
    currentVisitors,
    bounceRate,
    avgVisitTime,
    topPages,
    topReferrers,
    operatingSystems,
    browsers,
    devices,
    topLocations,
    visitorsDeltaPercentage,
    bounceRateDelta,
    avgVisitTimeDeltaPercentage,
    entryPages,
    exitPages,
    exitLinks,
  } = useAnalyticsData({
    websiteId: resolvedParams.id,
    selectedPeriod,
    activeFilters,
    customPeriod
  });

  // Fetch revenue analytics once at parent level
  const {
    revenueData,
    isLoading: isRevenueLoading,
    isRevenueEnabled,
    formatCurrency,
    formatPercentage
  } = useRevenueAnalytics(
    resolvedParams.id, 
    selectedPeriod, 
    customPeriod, 
    {
      // Pass system filters from activeFilters
      system: activeFilters.find(f => f.type === 'system') ? {
        type: systemView,
        value: activeFilters.find(f => f.type === 'system')!.value as string
      } : undefined
    }, 
    revenueFilters
  );

  const { getBrowserIcon } = useBrowserIcon();
  const { getDeviceIcon } = useDeviceIcon();

  const {
    groupedLocations,
    maxPageVisitors,
    getMaxLocationVisitors,
    maxOSVisitors,
    maxBrowserVisitors,
    maxDeviceVisitors,
    maxReferrerVisitors,
    groupedReferrers,
  } = useGroupedData(
    topLocations,
    topReferrers,
    operatingSystems,
    browsers,
    devices,
    topPages
  );

  const maxLocationVisitors = useMemo(() => {
    return getMaxLocationVisitors(locationView);
  }, [getMaxLocationVisitors, locationView]);

  const fetchVisitorJourney = async (
    visitorId: string,
    eventTimestamp: string
  ) => {
    try {
      setIsLoadingJourney(true);
      const response = await fetch(
        `/api/events?websiteId=${resolvedParams.id}&visitorId=${visitorId}&endTime=${eventTimestamp}`
      );
      if (!response.ok) throw new Error("Failed to fetch journey");
      const events = await response.json();
      setJourneyEvents(events);
    } catch (error) {
      console.error("Error fetching journey:", error);
      setJourneyEvents([]);
    } finally {
      setIsLoadingJourney(false);
    }
  };

  const handleJourneyClick = (
    eventName: string,
    visitorId: string,
    timestamp: string
  ) => {
    setSelectedJourney({ eventName, visitorId, timestamp });
    setShowJourneyModal(true);
    fetchVisitorJourney(visitorId, timestamp);
  };

  if (isWebsiteLoading || isAnalyticsLoading)
    return <WebsiteDashboardSkeleton />;
  if (error) return <div className="p-4 text-red-500">{error}</div>;
  if (!website) return <div className="p-4">Website not found</div>;

  return (
    <div className="min-h-screen bg-[#1C1C1C] text-gray-100 overflow-x-hidden">
      <div className="container mx-auto px-4  max-w-full sm:max-w-6xl">
        {/* Show banner only if website data is loaded and no events have ever been received */}
        {!isWebsiteLoading && website && !website.hasReceivedEvents && (
          <div className="mb-4 p-4 bg-blue-900 text-blue-100 rounded-md border border-blue-700 text-center">
            Awaiting your first events... <br />
            <Link
              href={`/settings/${resolvedParams.id}`}
              className="underline font-semibold hover:text-blue-200"
            >
              Click here to verify script installation
            </Link>
          </div>
        )}
        <div className="mb-8">
          <div className="flex items-center flex-nowrap gap-2 sm:gap-3">
            <BackArrow />
            <WebsiteDropdown
              dropdownRef={dropdownRef}
              setIsDropdownOpen={setIsDropdownOpen}
              isDropdownOpen={isDropdownOpen}
              website={website}
              websites={websites}
            />
            <PeriodDropdown
              periodDropdownRef={periodDropdownRef}
              setIsPeriodDropdownOpen={setIsPeriodDropdownOpen}
              isPeriodDropdownOpen={isPeriodDropdownOpen}
              selectedPeriod={selectedPeriod}
              setSelectedPeriod={setSelectedPeriod}
              customPeriod={customPeriod}
              onCustomPeriodChange={setCustomPeriod}
            />
          </div>
        </div>
        <div className="bg-[#2A2A2A] p-4 sm:p-6 rounded-lg mb-4 sm:mb-8">
          <FilterIndicator
            activeFilters={activeFilters}
            onRemoveFilter={(index) =>
              handleFilterClick(
                activeFilters[index].type,
                activeFilters[index].value,
                activeFilters[index].label
              )
            }
            onClearAll={clearAllFilters}
          />
          
          {/* Revenue Filters Toggle - Only show if website has revenue attribution enabled */}
          {/* {website.revenueAttributionEnabled && (
            <div className="mb-4 flex items-center justify-between">
              <div className="flex items-center gap-2">
                <button
                  onClick={() => setShowRevenueFilters(!showRevenueFilters)}
                  className={`flex items-center gap-2 px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                    hasActiveRevenueFilters
                      ? "bg-blue-600 text-white"
                      : "bg-gray-700 text-gray-300 hover:bg-gray-600"
                  }`}
                >
                  <svg
                    className="w-4 h-4"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"
                    />
                  </svg>
                  Revenue Filters
                  {hasActiveRevenueFilters && (
                    <span className="bg-blue-500 text-white text-xs px-1.5 py-0.5 rounded-full">
                      Active
                    </span>
                  )}
                </button>
              </div>
              
              {hasActiveRevenueFilters && (
                <button
                  onClick={handleClearRevenueFilters}
                  className="text-xs text-gray-400 hover:text-white underline"
                >
                  Clear Revenue Filters
                </button>
              )}
            </div>
          )} */}

          {/* Revenue Filters Component */}
          {website.revenueAttributionEnabled && showRevenueFilters && (
            <div className="mb-6">
              <RevenueFilters
                filters={revenueFilters}
                onFiltersChange={handleRevenueFiltersChange}
                onApplyFilters={handleApplyRevenueFilters}
                onClearFilters={handleClearRevenueFilters}
              />
            </div>
          )}

          <HeaderList
            uniqueVisitors={uniqueVisitors}
            isAnalyticsLoading={isAnalyticsLoading}
            visitorsDeltaPercentage={visitorsDeltaPercentage}
            bounceRate={bounceRate}
            bounceRateDelta={bounceRateDelta}
            avgVisitTime={avgVisitTime}
            avgVisitTimeDeltaPercentage={avgVisitTimeDeltaPercentage}
            currentVisitors={currentVisitors}
            websiteId={resolvedParams.id}
            selectedPeriod={selectedPeriod}
            customPeriod={customPeriod}
            revenueFilters={revenueFilters}
            revenueData={revenueData}
            isRevenueLoading={isRevenueLoading}
            isRevenueEnabled={isRevenueEnabled}
            formatCurrency={formatCurrency}
            formatPercentage={formatPercentage}
          />
          <Chart
            chartData={chartData}
            xAxisInterval={xAxisInterval}
            selectedPeriod={selectedPeriod}
            startDate={customPeriod?.startDate}
            endDate={customPeriod?.endDate}
            websiteId={resolvedParams.id}
            customPeriod={customPeriod}
            revenueFilters={revenueFilters}
            revenueData={revenueData}
            isRevenueLoading={isRevenueLoading}
            isRevenueEnabled={isRevenueEnabled}
          />
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-8 mb-4 sm:mb-8">
          <ReferrerSection
            topReferrers={topReferrers}
            maxReferrerVisitors={maxReferrerVisitors}
            groupedReferrers={groupedReferrers}
            setShowReferrerModal={setShowReferrerModal}
            websiteDomain={website?.domain}
          />
          <PageSection
            topPages={topPages}
            entryPages={entryPages}
            exitPages={exitPages}
            exitLinks={exitLinks || []}
            maxPageVisitors={maxPageVisitors}
            setShowPageModal={setShowPageModal}
            setCurrentPageView={setCurrentPageView}
          />
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-8">
          <LocationSection
            topLocations={topLocations}
            groupedLocations={groupedLocations}
            getMaxLocationVisitors={getMaxLocationVisitors}
            setShowLocationModal={setShowLocationModal}
            locationView={locationView}
            setLocationView={setLocationView}
          />
          <SysInfoSection
            operatingSystems={operatingSystems}
            browsers={browsers}
            devices={devices}
            maxOSVisitors={maxOSVisitors}
            maxBrowserVisitors={maxBrowserVisitors}
            maxDeviceVisitors={maxDeviceVisitors}
            setShowSystemModal={setShowSystemModal}
            systemView={systemView}
            setSystemView={setSystemView}
          />
        </div>
        <CustomEventsSection
          isLoadingEvents={isLoadingEvents}
          customEvents={customEvents}
          handleShowJourneyList={handleShowJourneyList}
          setShowJourneyListModal={setShowJourneyListModal}
          handleJourneyClick={handleJourneyClick}
        />
        {showJourneyModal && (
          <JourneyModal
            selectedJourney={selectedJourney}
            journeyEvents={journeyEvents}
            setShowJourneyModal={setShowJourneyModal}
            isLoadingJourney={isLoadingJourney}
            journeyModalRef={journeyModalRef}
          />
        )}
        {showJourneyListModal && selectedEventJourneys && (
          <JourneyListModal
            journeyListModalRef={journeyListModalRef}
            selectedEventJourneys={selectedEventJourneys}
            setShowJourneyListModal={setShowJourneyListModal}
            handleJourneyClick={handleJourneyClick}
          />
        )}
        {showLocationModal && (
          <LocationModal
            locationModalRef={locationModalRef}
            locationView={locationView}
            groupedLocations={groupedLocations}
            maxLocationVisitors={maxLocationVisitors}
            setShowLocationModal={setShowLocationModal}
          />
        )}
        {showPageModal && (
          <PageModal
            pageModalRef={pageModalRef}
            topPages={topPages}
            entryPages={entryPages}
            exitPages={exitPages}
            exitLinks={exitLinks || []}
            maxPageVisitors={maxPageVisitors}
            currentPageView={currentPageView}
            setShowPageModal={setShowPageModal}
          />
        )}
        {showReferrerModal && (
          <ReferrerModal
            referrerModalRef={referrerModalRef}
            topReferrers={topReferrers}
            maxReferrerVisitors={maxReferrerVisitors}
            groupedReferrers={groupedReferrers}
            setShowReferrerModal={setShowReferrerModal}
            websiteDomain={website?.domain}
          />
        )}
        {showSystemModal && (
          <SystemModal
            systemModalRef={systemModalRef}
            systemView={systemView}
            setSystemView={setSystemView}
            operatingSystems={operatingSystems}
            maxOSVisitors={maxOSVisitors}
            browsers={browsers}
            maxBrowserVisitors={maxBrowserVisitors}
            getBrowserIcon={getBrowserIcon}
            devices={devices}
            maxDeviceVisitors={maxDeviceVisitors}
            getDeviceIcon={getDeviceIcon}
            setShowSystemModal={setShowSystemModal}
          />
        )}
      </div>
    </div>
  );
}
