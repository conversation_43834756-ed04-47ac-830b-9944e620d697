import { useMemo } from 'react';
import { Location, TopReferrer } from '@/lib/types';

export interface GroupedLocations {
  country: Array<{ visitors: number; country: string }>;
  region: Array<{ visitors: number; country: string; region: string }>;
  city: Location[];
}

export interface GroupedReferrers {
  // Add optional url to campaign grouping
  campaign: Array<{ visitors: number; campaign: string; url?: string }>;
  utm: Array<{ visitors: number; source: string | undefined; medium: string | undefined }>;
  referrer: TopReferrer[];
}

export function useGroupedData(
  topLocations: Location[],
  topReferrers: TopReferrer[],
  operatingSystems: Array<{ name: string; visitors: number }>,
  browsers: Array<{ name: string; visitors: number }>,
  devices: Array<{ name: string; visitors: number }>,
  topPages: Array<{ path: string; visitors: number }>
) {
  // Group locations by country, region, and city
  const groupedLocations = useMemo<GroupedLocations>(() => {
    const byCountry = topLocations.reduce((acc, loc) => {
      const country = loc.country;
      if (!acc[country]) {
        acc[country] = { visitors: 0, country };
      }
      acc[country].visitors += loc.visitors;
      return acc;
    }, {} as Record<string, { visitors: number; country: string }>);

    const byRegion = topLocations.reduce((acc, loc) => {
      const region = loc.region ?? "Unknown";
      const key = `${loc.country}-${region}`;
      if (!acc[key]) {
        acc[key] = { visitors: 0, country: loc.country, region };
      }
      acc[key].visitors += loc.visitors;
      return acc;
    }, {} as Record<string, { visitors: number; country: string; region: string }>);

    return {
      country: Object.values(byCountry).sort((a, b) => b.visitors - a.visitors),
      region: Object.values(byRegion).sort((a, b) => b.visitors - a.visitors),
      city: topLocations.sort((a, b) => b.visitors - a.visitors),
    };
  }, [topLocations]);

  // Group referrers by campaign and UTM
  const groupedReferrers = useMemo<GroupedReferrers>(() => {
    const byCampaign = topReferrers.reduce((acc, ref) => {
      // Prioritize ref parameter over other campaign parameters
      const campaign =
        ref.referralParams?.ref ||
        ref.referralParams?.campaign ||
        ref.referralParams?.utm_campaign ||
        "No Campaign";
      if (!acc[campaign]) {
        // Store the url along with campaign and visitors, converting null to undefined
        acc[campaign] = { visitors: 0, campaign, url: ref.url ?? undefined };
      }
      acc[campaign].visitors += ref.visitors;
      return acc;
      // Update the type definition for the accumulator
    }, {} as Record<string, { visitors: number; campaign: string; url?: string }>);

    const byUTM = topReferrers.reduce((acc, ref) => {
      // Only use utm parameters, not ref
      const utmSource = ref.referralParams?.utm_source;
      const utmMedium = ref.referralParams?.utm_medium;
      const key = utmSource
        ? `${utmSource}${utmMedium ? ` / ${utmMedium}` : ""}`
        : "No UTM";
      if (!acc[key]) {
        acc[key] = { visitors: 0, source: utmSource, medium: utmMedium };
      }
      acc[key].visitors += ref.visitors;
      return acc;
    }, {} as Record<string, { visitors: number; source: string | undefined; medium: string | undefined }>);

    return {
      campaign: Object.values(byCampaign).sort((a, b) => b.visitors - a.visitors),
      utm: Object.values(byUTM).sort((a, b) => b.visitors - a.visitors),
      referrer: topReferrers,
    };
  }, [topReferrers]);

  // Calculate max values for proportional bars
  const maxReferrerVisitors = useMemo(
    () => Math.max(...topReferrers.map((r) => r.visitors), 1),
    [topReferrers]
  );
  
  const maxPageVisitors = useMemo(
    () => Math.max(...topPages.map((p) => p.visitors), 1),
    [topPages]
  );
  
  const getMaxLocationVisitors = (locationView: 'country' | 'region' | 'city') => {
    const values = {
      country: Math.max(...groupedLocations.country.map((l) => l.visitors), 1),
      region: Math.max(...groupedLocations.region.map((l) => l.visitors), 1),
      city: Math.max(...groupedLocations.city.map((l) => l.visitors), 1),
    };
    return values[locationView];
  };
  
  const maxOSVisitors = useMemo(
    () => Math.max(...operatingSystems.map((os) => os.visitors), 1),
    [operatingSystems]
  );

  const maxBrowserVisitors = useMemo(
    () => Math.max(...browsers.map((b) => b.visitors), 1),
    [browsers]
  );

  const maxDeviceVisitors = useMemo(
    () => Math.max(...devices.map((d) => d.visitors), 1),
    [devices]
  );

  return {
    groupedLocations,
    groupedReferrers,
    maxReferrerVisitors,
    maxPageVisitors,
    getMaxLocationVisitors,
    maxOSVisitors,
    maxBrowserVisitors,
    maxDeviceVisitors
  };
}
