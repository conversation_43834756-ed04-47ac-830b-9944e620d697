import { NextResponse, NextRequest } from "next/server";

import { headers } from "next/headers";
import <PERSON><PERSON> from "stripe";
import configFile from "@/config";
import connect from "@/util/db";
import User from "@/models/user";
import { findCheckoutSession } from "@/lib/stripe";

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: "2025-02-24.acacia",
  typescript: true,
});
const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET;

// This is where we receive Stripe webhook events
// It used to update the user data, send emails, etc...
// By default, it'll store the user in the database
// See more: https://shipfa.st/docs/features/payments
export async function POST(req: NextRequest) {
  await connect();

  const body = await req.text();

  const signature = (await headers()).get("stripe-signature");

  let eventType;
  let event;

  // verify Stripe event is legit
  try {
    event = stripe.webhooks.constructEvent(body, signature!, webhookSecret!);
  } catch (err: any) {
    console.error(`Webhook signature verification failed. ${err.message}`);
    return NextResponse.json({ error: err.message }, { status: 400 });
  }

  eventType = event.type;

  try {
    switch (event.type) {
      case "checkout.session.completed": {
        const stripeObject: Stripe.Checkout.Session = event.data.object;
        const session = await findCheckoutSession(stripeObject.id);

        const customerId = session?.customer;
        const lineItems = await stripe.checkout.sessions.listLineItems(
          stripeObject.id
        );
        const priceId = lineItems.data[0]?.price?.id;
        const userId = stripeObject.client_reference_id;

        console.log("Looking for plan with priceId:", priceId);
        const plan = configFile.stripe.plans.find((p) => p.priceId === priceId);

        if (!plan) {
          console.error(`No plan found for priceId: ${priceId}`);
          console.log(
            "Available plans:",
            configFile.stripe.plans.map((p) => ({
              name: p.name,
              priceId: p.priceId,
            }))
          );
          break;
        }

        console.log("Found plan:", { name: plan.name, type: plan.type });

        let user;

        // Try to find user by all possible identifiers
        if (userId) {
          user = await User.findById(userId);
          console.log("User lookup by ID result:", !!user);
        }

        if (!user && customerId) {
          const customer = await stripe.customers.retrieve(
            customerId as string
          );
          if ("email" in customer && customer.email) {
            user = await User.findOne({ email: customer.email });
            console.log("User lookup by customer email result:", !!user);
          }
        }

        if (!user && stripeObject.customer_details?.email) {
          user = await User.findOne({
            email: stripeObject.customer_details.email,
          });
          console.log("User lookup by session email result:", !!user);
        }

        if (!user) {
          console.log("Creating new user");
          user = new User({
            email: stripeObject.customer_details?.email,
            customerId: plan.type === "subscription" ? customerId : null,
            priceId: priceId,
            hasPro: plan.name === "Pro" || plan.name === "Plus",
            hasPlus: plan.name === "Plus",
            accessExpiresAt: null,
          });
        } else {
          console.log("Updating existing user:", {
            id: user._id,
            currentPlan: user.priceId,
            newPlan: priceId,
          });

          if (plan.type === "subscription") {
            user.customerId = customerId;
          }
          user.priceId = priceId;

          // Add days to accessUntil based on plan type
          const now = new Date();
          let accessUntil;
          if (plan.frequency === "annual") {
            accessUntil = new Date(now.setDate(now.getDate() + 365)); // Annual plan: 365 days
          } else {
            accessUntil = new Date(now.setDate(now.getDate() + 30)); // Monthly plan: 30 days
          }
          user.accessUntil = accessUntil;
        }

        const savedUser = await user.save();
        console.log("User saved successfully:", {
          id: savedUser._id,
          email: savedUser.email,
          hasPro: savedUser.hasPro,
          hasPlus: savedUser.hasPlus,
          priceId: savedUser.priceId,
          accessUntil: savedUser.accessUntil,
        });

        break;
      }

      case "checkout.session.expired": {
        // User didn't complete the transaction
        // You don't need to do anything here, by you can send an email to the user to remind him to complete the transaction, for instance
        break;
      }

      case "customer.subscription.updated": {
        // The customer might have changed the plan (higher or lower plan, cancel soon etc...)
        // You don't need to do anything here, because Stripe will let us know when the subscription is canceled for good (at the end of the billing cycle) in the "customer.subscription.deleted" event
        // You can update the user data to show a "Cancel soon" badge for instance
        break;
      }

      case "customer.subscription.deleted": {
        // The customer subscription stopped
        // ❌ Revoke access to the product
        const stripeObject: Stripe.Subscription = event.data
          .object as Stripe.Subscription;

        const subscription = await stripe.subscriptions.retrieve(
          stripeObject.id
        );
        const user = await User.findOne({ customerId: subscription.customer });

        // Revoke access to your product
        user.hasPro = false;
        user.hasPlus = false;
        user.accessExpiresAt = null;
        await user.save();

        break;
      }

      case "invoice.paid": {
        const stripeObject: Stripe.Invoice = event.data
          .object as Stripe.Invoice;

        const priceId = stripeObject.lines.data[0].price!.id;
        const customerId = stripeObject.customer;

        const user = await User.findOne({ customerId });

        if (!user) {
          console.error(`No user found for customerId: ${customerId}`);
          break;
        }

        // Make sure the invoice is for the same plan (priceId) the user subscribed to
        if (user.priceId !== priceId) {
          console.warn(
            `Invoice priceId (${priceId}) doesn't match user's priceId (${user.priceId})`
          );
          break;
        }

        // Grant user access based on the plan
        const plan = configFile.stripe.plans.find((p) => p.priceId === priceId);

        if (!plan) {
          console.error(`No plan found for priceId: ${priceId}`);
          break;
        }

        // Added explicit check for plan to satisfy linter, though previous check should suffice
        if (plan) {
          if (plan.name === "Pro" || plan.name === "Plus") {
            user.hasPro = true;
            user.hasPlus = plan.name === "Plus";
          } else {
            user.hasPro = false;
            user.hasPlus = false;
          }
        }

        await user.save();

        break;
      }

      case "invoice.payment_failed":
        // A payment failed (for instance the customer does not have a valid payment method)
        // ❌ Revoke access to the product
        // ⏳ OR wait for the customer to pay (more friendly):
        //      - Stripe will automatically email the customer (Smart Retries)
        //      - We will receive a "customer.subscription.deleted" when all retries were made and the subscription has expired

        break;

      case "charge.refunded":
      case "charge.refund.updated": {
        const charge: Stripe.Charge = event.data.object as Stripe.Charge;
        const customerId = charge.customer as string;

        if (!customerId) {
          console.error("No customer ID found for refund");
          break;
        }

        const customer = (await stripe.customers.retrieve(
          customerId
        )) as Stripe.Customer;
        const user = await User.findOne({ email: customer.email });

        if (!user) {
          console.error(`No user found for email: ${customer.email}`);
          break;
        }

        // Check if it's a full refund
        if (charge.amount_refunded === charge.amount) {
          // Retrieve the associated invoice to check if it's a subscription or one-time payment
          const invoice = await stripe.invoices.retrieve(
            charge.invoice as string
          );
          const subscription = invoice.subscription;

          if (subscription) {
            // It's a subscription refund
            console.log(
              `Subscription refund processed for user: ${user._id}. Access will be revoked at the end of the billing cycle.`
            );
            // We don't need to do anything here, as the access will be revoked when we receive the 'customer.subscription.deleted' event
          } else {
            // It's a one-time payment refund
            user.hasPro = false;
            user.hasPlus = false;
            user.accessExpiresAt = null;
            await user.save();
            console.log(
              `One-time payment refund processed for user: ${user._id}. Access revoked immediately.`
            );
          }
        } else {
          // Partial refund - you might want to handle this differently
          console.log(
            `Partial refund processed for user: ${user._id}. No changes to access.`
          );
        }

        break;
      }

      default:
      // Unhandled event type
    }
  } catch (e: any) {
    console.error("stripe error: ", e.message);
  }

  return NextResponse.json({});
}
