import Image from "next/image";
import { FaQuestionCircle } from "react-icons/fa";

export function useOSIcon() {
  const getOSIcon = (osName: string) => {
    const name = osName.toLowerCase();
    if (name.includes("windows"))
      return (
        <Image
          src="/assets/windows.webp"
          alt="Windows"
          width={20}
          height={20}
          className="w-5 h-5"
        />
      );
    if (name.includes("mac") || name.includes("ios"))
      return (
        <Image
          src="/assets/apple.webp"
          alt="Apple"
          width={20}
          height={20}
          className="w-5 h-5"
        />
      );
    if (name.includes("linux"))
      return (
        <Image
          src="/assets/gnu_linux.webp"
          alt="GNU-Linux"
          width={20}
          height={20}
          className="w-5 h-5"
        />
      );
    if (name.includes("android"))
      return (
        <Image
          src="/assets/android.webp"
          alt="Android"
          width={20}
          height={20}
          className="w-5 h-5"
        />
      );
    return <FaQuestionCircle className="w-5 h-5" />;
  };

  return { getOSIcon };
}
