import type { Metada<PERSON> } from "next";
import { getWebsiteById } from "@/lib/actions/website-actions";

export async function generateMetadata({
  params,
}: {
  params: Promise<{ id: string }>;
}): Promise<Metadata> {
  // Fetch the website data
  const resolvedParams = await params;
  const website = await getWebsiteById(resolvedParams.id);
  console.log(website);
  // Use the domain in the title if available
  return {
    title: website?.domain
      ? `${website.domain} | VersaTailor`
      : "Dashboard | VersaTailor",
    description: website?.description || "VersaTailor website dashboard",
  };
}

export default function DashboardLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return <>{children}</>;
}
