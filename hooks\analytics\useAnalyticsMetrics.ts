import { useMemo } from 'react';
import { Event, MetricsData } from './types';
import { calculateBounceRate, calculateAvgVisitTime } from './utils';

export function useAnalyticsMetrics(
  events: Event[],
  prevEvents: Event[],
  selectedPeriod: string
): MetricsData {
  const { uniqueVisitors, prevUniqueVisitors } = useMemo(() => {
    const current = events.length
      ? new Set(events.map((event) => event.visitorId)).size
      : 0;

    const prev = prevEvents.length
      ? new Set(prevEvents.map((event) => event.visitorId)).size
      : 0;

    return { uniqueVisitors: current, prevUniqueVisitors: prev };
  }, [events, prevEvents]);

  const { visitorsDelta, visitorsDeltaPercentage } = useMemo(() => {
    const delta =
      selectedPeriod === "all" || !prevEvents.length
        ? null
        : uniqueVisitors - prevUniqueVisitors;

    const deltaPercentage =
      selectedPeriod === "all" ||
      !prevEvents.length ||
      prevUniqueVisitors === 0
        ? null
        : Math.round((delta! / prevUniqueVisitors) * 100);

    return { visitorsDelta: delta, visitorsDeltaPercentage: deltaPercentage };
  }, [selectedPeriod, prevEvents.length, uniqueVisitors, prevUniqueVisitors]);

  const { bounceRate, prevBounceRate, bounceRateDelta } = useMemo(() => {
    const current = calculateBounceRate(events);
    const prev = calculateBounceRate(prevEvents);
    const delta =
      selectedPeriod === "all" || !prevEvents.length
        ? null
        : current - prev;

    return {
      bounceRate: current,
      prevBounceRate: prev,
      bounceRateDelta: delta,
    };
  }, [events, prevEvents, selectedPeriod]);

  const {
    avgVisitTime,
    prevAvgVisitTime,
    avgVisitTimeDelta,
    avgVisitTimeDeltaPercentage,
  } = useMemo(() => {
    const current = calculateAvgVisitTime(events);
    const prev = calculateAvgVisitTime(prevEvents);
    const delta =
      selectedPeriod === "all" || !prevEvents.length
        ? null
        : current - prev;
    const deltaPercentage =
      selectedPeriod === "all" || !prevEvents.length || prev === 0
        ? null
        : Math.round((delta! / prev) * 100);

    return {
      avgVisitTime: current,
      prevAvgVisitTime: prev,
      avgVisitTimeDelta: delta,
      avgVisitTimeDeltaPercentage: deltaPercentage,
    };
  }, [events, prevEvents, selectedPeriod]);

  return {
    uniqueVisitors,
    prevUniqueVisitors,
    visitorsDelta,
    visitorsDeltaPercentage,
    bounceRate,
    prevBounceRate,
    bounceRateDelta,
    avgVisitTime,
    prevAvgVisitTime,
    avgVisitTimeDelta,
    avgVisitTimeDeltaPercentage,
  };
} 