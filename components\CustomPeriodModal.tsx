import React, { RefObject, Dispatch, SetStateAction } from 'react';
import { CustomPeriod } from '@/lib/types';

interface CustomPeriodModalProps {
  modalRef: RefObject<HTMLDivElement | null>;
  isOpen: boolean;
  onClose: () => void;
  onApply: (period: CustomPeriod) => void;
  initialStartDate?: Date;
  initialEndDate?: Date;
}

export default function CustomPeriodModal({
  modalRef,
  isOpen,
  onClose,
  onApply,
  initialStartDate = new Date(),
  initialEndDate = new Date(),
}: CustomPeriodModalProps) {
  const [startDate, setStartDate] = React.useState<Date>(initialStartDate);
  const [endDate, setEndDate] = React.useState<Date>(initialEndDate);
  const [selectedDates, setSelectedDates] = React.useState<Date[]>([]);
  const [currentMonth, setCurrentMonth] = React.useState(new Date());

  const handleDateClick = (date: Date) => {
    // Prevent selecting future dates
    if (date > new Date()) return;

    if (selectedDates.length === 0) {
      setSelectedDates([date]);
      setStartDate(date);
      setEndDate(date);
    } else if (selectedDates.length === 1) {
      const firstDate = selectedDates[0];
      if (date < firstDate) {
        setStartDate(date);
        setEndDate(firstDate);
        setSelectedDates([date, firstDate]);
      } else {
        setStartDate(firstDate);
        setEndDate(date);
        setSelectedDates([firstDate, date]);
      }
    } else {
      setSelectedDates([date]);
      setStartDate(date);
      setEndDate(date);
    }
  };

  const isDateInRange = (date: Date) => {
    if (selectedDates.length !== 2) return false;
    return date >= startDate && date <= endDate;
  };

  const isDateSelected = (date: Date) => {
    return selectedDates.some(
      (selectedDate) =>
        selectedDate.getDate() === date.getDate() &&
        selectedDate.getMonth() === date.getMonth() &&
        selectedDate.getFullYear() === date.getFullYear()
    );
  };

  const getDaysInMonth = (date: Date) => {
    const year = date.getFullYear();
    const month = date.getMonth();
    const daysInMonth = new Date(year, month + 1, 0).getDate();
    const firstDayOfMonth = new Date(year, month, 1).getDay();
    
    const days: Date[] = [];
    
    // Add previous month's days
    for (let i = 0; i < firstDayOfMonth; i++) {
      const prevDate = new Date(year, month, -i);
      days.unshift(prevDate);
    }
    
    // Add current month's days
    for (let i = 1; i <= daysInMonth; i++) {
      days.push(new Date(year, month, i));
    }
    
    // Add next month's days to complete the grid
    const remainingDays = 42 - days.length; // 6 rows * 7 days = 42
    for (let i = 1; i <= remainingDays; i++) {
      days.push(new Date(year, month + 1, i));
    }
    
    return days;
  };

  const handlePrevMonth = () => {
    setCurrentMonth(new Date(currentMonth.getFullYear(), currentMonth.getMonth() - 1));
  };

  const handleNextMonth = () => {
    setCurrentMonth(new Date(currentMonth.getFullYear(), currentMonth.getMonth() + 1));
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div
        ref={modalRef}
        className="bg-[#1C1C1C] rounded-lg shadow-xl w-full max-w-md overflow-hidden"
      >
        <div className="p-4 border-b border-gray-800 flex items-center justify-between">
          <h2 className="text-lg font-medium text-white">Select Date Range</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-white transition-colors"
          >
            <svg
              className="w-5 h-5"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </button>
        </div>

        <div className="p-4">
          <div className="flex items-center justify-between mb-4">
            <button
              onClick={handlePrevMonth}
              className="p-2 hover:bg-[#2A2A2A] rounded-lg transition-colors"
            >
              <svg
                className="w-5 h-5 text-gray-400"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M15 19l-7-7 7-7"
                />
              </svg>
            </button>
            <div className="text-white font-medium">
              {currentMonth.toLocaleString('default', {
                month: 'long',
                year: 'numeric',
              })}
            </div>
            <button
              onClick={handleNextMonth}
              className="p-2 hover:bg-[#2A2A2A] rounded-lg transition-colors"
            >
              <svg
                className="w-5 h-5 text-gray-400"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9 5l7 7-7 7"
                />
              </svg>
            </button>
          </div>

          <div className="grid grid-cols-7 gap-1 mb-1">
            {['Su', 'Mo', 'Tu', 'We', 'Th', 'Fr', 'Sa'].map((day) => (
              <div
                key={day}
                className="text-center text-sm text-gray-400 py-1"
              >
                {day}
              </div>
            ))}
          </div>

          <div className="grid grid-cols-7 gap-1">
            {getDaysInMonth(currentMonth).map((date, index) => {
              const isCurrentMonth = date.getMonth() === currentMonth.getMonth();
              const isToday = date.toDateString() === new Date().toDateString();
              const selected = isDateSelected(date);
              const inRange = isDateInRange(date);
              const isFutureDate = date > new Date();

              return (
                <button
                  key={index}
                  onClick={() => handleDateClick(date)}
                  disabled={isFutureDate}
                  className={`
                    p-2 text-sm rounded-lg transition-colors relative
                    ${isCurrentMonth ? 'text-white hover:bg-[#2A2A2A]' : 'text-gray-600'}
                    ${selected ? 'bg-blue-500 hover:bg-blue-600' : ''}
                    ${inRange && !selected ? 'bg-blue-500/20 hover:bg-blue-500/30' : ''}
                    ${isToday && !selected ? 'ring-1 ring-blue-500' : ''}
                    ${isFutureDate ? 'opacity-50 cursor-not-allowed hover:bg-transparent' : ''}
                  `}
                >
                  {date.getDate()}
                </button>
              );
            })}
          </div>

          <div className="mt-4 flex items-center justify-between text-sm text-gray-400">
            <div>
              {startDate.toLocaleDateString()} -{' '}
              {endDate.toLocaleDateString()}
            </div>
          </div>

          <div className="mt-4 flex items-center justify-end gap-2">
            <button
              onClick={onClose}
              className="px-4 py-2 text-sm text-gray-400 hover:text-white transition-colors"
            >
              Cancel
            </button>
            <button
              onClick={() => {
                onApply({ startDate, endDate });
                onClose();
              }}
              className="px-4 py-2 text-sm bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
            >
              Apply
            </button>
          </div>
        </div>
      </div>
    </div>
  );
} 