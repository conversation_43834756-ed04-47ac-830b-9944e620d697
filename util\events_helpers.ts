import { NextRequest } from "next/server";

const BOT_PATTERNS = [
  "bot",
  "spider",
  "crawler",
  "scraper",
  "preview",
  "ping",
  "slurp",
  "wget",
  "phantom",
  "headless",
  "python",
  "ruby",
  "curl",
  "postman",
  "facebookexternalhit",
  "whatsapp",
  "telegram",
  "discord",
  "semrush",
  "ahrefsbot",
  "googlebot",
  "bingbot",
  "yandexbot",
  "cloudfront",
  "cloudflare",
  "lighthouse",
  "pagespeed",
  "prerender",
  "snippets",
];

// Helper function to detect bots
export function isBot(request: NextRequest): boolean {
  const userAgent = request.headers.get("user-agent")?.toLowerCase() || "";

  // Check if user agent is missing
  if (!userAgent) return true;

  // Check if it matches known bot patterns
  if (BOT_PATTERNS.some((pattern) => userAgent.includes(pattern))) return true;

  // Check DNT (Do Not Track) header
  const dnt = request.headers.get("dnt");
  if (dnt === "1") return true;

  // Check for common bot headers
  const isHeadless =
    request.headers.get("chrome-lighthouse") ||
    request.headers.get("x-lighthouse") ||
    request.headers.get("puppeteer");
  if (isHeadless) return true;

  // Additional bot detection checks
  // Check for suspicious user agent characteristics
  if (userAgent.length < 20) return true; // Most legitimate browsers have longer UA strings
  if (
    !userAgent.includes("mozilla") &&
    !userAgent.includes("chrome") &&
    !userAgent.includes("safari") &&
    !userAgent.includes("firefox") &&
    !userAgent.includes("edge") &&
    !userAgent.includes("opera")
  ) {
    return true; // Most legitimate browsers include one of these names
  }

  // Check for missing or suspicious headers that legitimate browsers typically send
  const acceptHeader = request.headers.get("accept");
  const acceptLanguage = request.headers.get("accept-language");
  const acceptEncoding = request.headers.get("accept-encoding");

  if (!acceptHeader || !acceptLanguage || !acceptEncoding) {
    return true; // Legitimate browsers send these headers
  }

  return false;
}

export function extractReferralParams(url: string): Record<string, string> | null {
  try {
    const parsedUrl = new URL(url);
    const searchParams = parsedUrl.searchParams;
    const params: Record<string, string> = {};

    // Common referral parameters to check
    const referralParams = [
      "ref",
      "source",
      "utm_source",
      "utm_medium",
      "utm_campaign",
      "utm_term",
      "utm_content",
      "campaign",
      "referral",
      "affiliate",
      "fbclid",
      "gclid",
      "msclkid",
      "dclid",
    ];

    referralParams.forEach((param) => {
      const value = searchParams.get(param);
      if (value) params[param] = value;
    });

    return Object.keys(params).length > 0 ? params : null;
  } catch (error) {
    console.warn("Error parsing URL for referral params:", error);
    return null;
  }
}

// Update location validation helper to be more strict
export function validateLocation(location: any) {
  if (!location) return { city: null, country: null, region: null };
  // Only return location if at least one field has a valid value
  const validCity =
    typeof location.city === "string" && location.city.length > 0;
  const validCountry =
    typeof location.country === "string" && location.country.length > 0;
  const validRegion =
    typeof location.region === "string" && location.region.length > 0;

  return {
    city: validCity ? location.city : null,
    country: validCountry ? location.country : null,
    region: validRegion ? location.region : null,
  };
}
