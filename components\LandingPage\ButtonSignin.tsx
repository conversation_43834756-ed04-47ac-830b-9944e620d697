"use client";
import { useRouter } from "next/navigation";

function ButtonSignin({
  text = "Get started",
  extraStyle,
  onClick,
}: {
  text?: string;
  extraStyle?: string;
  onClick?: () => void;
}) {
  const router = useRouter();

  function handleClick() {
    router.push("/dashboard");
    onClick?.();
  }

  return (
    <button
      className={`btn ${extraStyle ? extraStyle : ""} bg-[#204ec8] text-primary-foreground shadow hover:bg-[#304d78]/90 p-3 rounded-lg`}
      onClick={handleClick}
    >
      {text}
    </button>
  );
}

export default ButtonSignin;
