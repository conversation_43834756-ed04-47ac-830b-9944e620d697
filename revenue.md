# Revenue Attribution Implementation Roadmap

> **Development Note**: This application is currently in active development. No backward compatibility, data migration, or legacy support is required during implementation. Database schema changes and breaking changes can be made freely.

## Phase 1: Core Revenue Infrastructure
**Goal**: Build the foundational backend systems for revenue tracking

### 1.1 Database Schema Updates
- [x] Update `models/website.ts` schema
  - [x] Add `stripeApiKey` field (encrypted, select: false)
  - [x] Add `stripeWebhookId` field 
  - [x] Add `stripeWebhookSecret` field (for signature verification)
  - [x] Add `revenueAttributionEnabled` boolean field
  - [x] Update TypeScript interface
- [x] Update `models/event.ts` schema
  - [x] Add optional `revenueData` object field
  - [x] Include amount, currency, stripeChargeId, stripeCustomerId, etc.
  - [x] Add revenue-specific indexes for performance
  - [x] Update TypeScript interface
- [x] Test database migrations
  - [x] Run schema updates on development database
  - [x] Verify new fields work correctly
  - [x] Test with sample data (no production data to migrate)

### 1.2 Stripe Integration Infrastructure
- [x] Create Stripe utility functions
  - [x] API key validation function
  - [x] Webhook creation function
  - [x] Webhook deletion function
  - [x] Permission validation for restricted keys
- [x] Implement API key encryption
  - [x] Create encryption/decryption utilities
  - [x] Set up environment variables for encryption keys
  - [x] Test encryption round-trip
- [x] Create Stripe client configuration
  - [x] Dynamic client creation with user API keys
  - [x] Error handling for invalid keys
  - [x] Rate limiting considerations

### 1.3 Webhook Handler API
- [x] Create `app/api/webhooks/revenue/[websiteId]/route.ts`
  - [x] Extract websiteId from URL parameters
  - [x] Verify website exists and has revenue attribution enabled
  - [x] Verify webhook signature using website's stored webhook secret
  - [x] Handle `checkout.session.completed` events
  - [x] Handle `invoice.payment_succeeded` events
  - [x] Implement idempotency using Stripe event IDs
- [x] Visitor attribution lookup logic
  - [x] Extract visitor/session IDs from metadata
  - [x] Query existing events for attribution data
  - [x] Fallback attribution strategies
- [x] Revenue event creation
  - [x] Create new Event with `type: "payment"`
  - [x] Use websiteId from URL parameters
  - [x] Populate all attribution fields
  - [x] Store complete revenue data
  - [x] Handle edge cases (missing data, etc.)

### 1.4 Webhook Setup API
- [x] Create `app/api/stripe/webhook-setup/route.ts`
  - [x] Validate Stripe API key permissions
  - [x] Auto-create webhook endpoint with website ID in URL: `/api/webhooks/revenue/{websiteId}`
  - [x] Store webhook endpoint ID and secret in website model
  - [x] Handle setup errors gracefully
- [x] Create webhook management functions
  - [x] Enable/disable webhooks
  - [x] Update webhook configuration
  - [x] Delete webhooks on website removal
- [x] Test webhook lifecycle
  - [x] Create webhook via API with website-specific URL
  - [x] Verify webhook receives events at correct endpoint
  - [x] Test webhook deletion

## Phase 2: Frontend Integration
**Goal**: Add revenue attribution to user-facing interfaces

### 2.1 Website Onboarding Updates
- [x] Update `AddWebsiteForm` component
  - [x] Add "Enable Revenue Attribution" checkbox
  - [x] Add conditional Stripe API key input field
  - [x] Add link to Stripe restricted API key creation
  - [x] Update form validation for revenue fields
- [x] Update form submission logic
  - [x] Handle revenue attribution setup
  - [x] Show setup progress indicators
  - [x] Handle setup errors with user-friendly messages
- [x] Test onboarding flow
  - [x] Test with valid Stripe keys
  - [x] Test with invalid keys
  - [x] Test error scenarios

### 2.2 Revenue Analytics API
- [x] Create `app/api/analytics/revenue/route.ts`
  - [x] Query payment events for metrics
  - [x] Calculate total revenue with deltas
  - [x] Calculate conversion rates
  - [x] Calculate average order value
  - [x] Return data in consistent format
- [x] Add revenue filtering support
  - [x] Filter by date ranges
  - [x] Filter by referrer/source
  - [x] Filter by location
- [x] Test API endpoints
  - [x] Unit tests for calculations
  - [x] Integration tests with sample data
  - [x] Performance testing with large datasets

### 2.3 HeaderList Revenue Metrics
- [x] Update `components/HeaderList.tsx`
  - [x] Add Total Revenue metric with delta
  - [x] Add Conversion Rate metric with delta
  - [x] Add Average Order Value metric with delta
  - [x] Add Revenue Per Visitor metric
- [x] Handle loading states for revenue data
  - [x] Show skeleton loaders
  - [x] Handle API errors gracefully
  - [x] Show empty states when no revenue data
- [x] Test responsive design
  - [x] Mobile layout for additional metrics
  - [x] Tablet layout optimization
  - [x] Desktop layout with all metrics

### 2.4 Chart Component Revenue Mode
- [x] Update `components/Chart.tsx`
  - [x] Add toggle switch for Visitors vs Revenue
  - [x] Implement revenue chart data processing
  - [x] Update tooltip to show revenue amounts
  - [x] Maintain existing click functionality for filtering
- [x] Revenue chart visualization
  - [x] Bar chart for revenue amounts
  - [x] Proper currency formatting
  - [x] Time period consistency with visitor charts
- [x] Test chart functionality
  - [x] Toggle between visitor and revenue modes
  - [x] Test with different time periods
  - [x] Test click filtering on revenue chart

## Phase 3: Advanced Revenue Features
**Goal**: Build sophisticated revenue analytics and management

### 3.1 Revenue Settings Tab
- [x] Create `components/RevenueSettingsTab.tsx`
  - [x] Toggle revenue attribution on/off
  - [x] Update Stripe API key interface
  - [x] Show webhook status (active/inactive)
  - [x] Test webhook connection button
- [x] Revenue configuration options
  - [x] Currency display preferences
- [x] Recent revenue events viewer
  - [x] Show last 10 revenue events
  - [x] Display attribution details
  - [x] Link to full analytics view

### 3.2 Advanced Analytics Functions
- [x] Add revenue functions to `lib/analytics.ts`
  - [x] `getTotalRevenue()` with period comparison
  - [x] `getRevenueByPeriod()` for chart data
  - [x] `getConversionRate()` calculation
  - [x] `getRevenueByReferrer()` attribution
  - [x] `getRevenueByLocation()` geographic breakdown
- [x] Revenue attribution analysis
  - [x] First-touch attribution
  - [x] Last-touch attribution
- [x] Customer analytics
  - [x] New vs returning customer revenue
  - [x] Customer lifetime value basics

### 3.3 Enhanced Revenue Filtering
- [x] Add revenue-specific filters
  - [x] Filter by revenue amount ranges
  - [x] Filter by customer type (new/returning)
  - [x] Filter by payment method
  - [x] Filter by subscription vs one-time
- [x] Update filter UI components
  - [x] Revenue filter dropdown
  - [x] Amount range slider
  - [x] Customer type toggles
- [x] Apply filters to all revenue views
  - [x] Header metrics respect filters
  - [x] Charts update with filters
  - [x] Attribution reports filter correctly

## Phase 4: Polish & Testing
**Goal**: Ensure production readiness and reliability

### 4.1 Comprehensive Testing
- [ ] Unit Tests
  - [ ] Revenue calculation functions
  - [ ] Webhook payload processing
  - [ ] API key encryption/decryption
  - [ ] Chart data transformations
- [ ] Integration Tests
  - [ ] End-to-end webhook flow
  - [ ] Revenue attribution accuracy
  - [ ] API endpoint functionality
  - [ ] Database operations
- [ ] Security Tests
  - [ ] API key storage security
  - [ ] Webhook signature verification
  - [ ] Access control validation
  - [ ] Input sanitization

### 4.2 Error Handling & Edge Cases
- [ ] Webhook error scenarios
  - [ ] Handle malformed webhook payloads
  - [ ] Handle missing visitor attribution
  - [ ] Handle duplicate webhook events
  - [ ] Handle webhook delivery failures
- [ ] API error handling
  - [ ] Invalid Stripe API keys
  - [ ] Stripe API rate limits
  - [ ] Network failures
  - [ ] Database connection issues
- [ ] User experience error states
  - [ ] Revenue setup failures
  - [ ] Missing revenue data states
  - [ ] Loading error recovery

### 4.3 Performance Optimization
- [ ] Database query optimization
  - [ ] Index revenue event queries
  - [ ] Optimize large dataset queries
  - [ ] Implement query result caching
- [ ] API response optimization
  - [ ] Cache revenue calculations
  - [ ] Implement pagination for large datasets
  - [ ] Optimize real-time data updates
- [ ] Frontend performance
  - [ ] Lazy load revenue components
  - [ ] Optimize chart rendering
  - [ ] Minimize API calls

### 4.4 Documentation & Monitoring
- [ ] User Documentation
  - [ ] Revenue attribution setup guide
  - [ ] Stripe integration instructions
  - [ ] Revenue metrics explanation
  - [ ] Troubleshooting guide
- [ ] Developer Documentation
  - [ ] API endpoint documentation
  - [ ] Webhook payload examples
  - [ ] Integration code samples
  - [ ] Security best practices
- [ ] Monitoring & Alerting
  - [ ] Webhook delivery monitoring
  - [ ] Revenue calculation accuracy monitoring
  - [ ] Error rate alerting
  - [ ] Performance monitoring

---

## Implementation Notes

### Development Environment Benefits
- No existing production data to migrate
- Database schema changes can be made freely
- Breaking changes to APIs are acceptable
- No need for feature flags or gradual rollouts during development

### Required Environment Variables
```bash
# Add to .env.local
STRIPE_API_KEY_ENCRYPTION_KEY=your-32-character-encryption-key-here
```

### Dependencies
- Stripe API integration
- Database migration tools
- Encryption libraries
- Chart.js or similar for revenue visualizations

### Risk Mitigation
- Implement comprehensive logging for webhook processing
- Create backup attribution strategies for missing visitor data
- Plan for Stripe API rate limits and failures
- Design graceful degradation when revenue features are unavailable

### Success Metrics
- [ ] 95% webhook delivery success rate
- [ ] < 2 second revenue analytics API response times
- [ ] Zero revenue attribution data loss
- [ ] 90% user adoption rate for revenue attribution feature

### Timeline Estimates
- **Phase 1**: 2-3 weeks
- **Phase 2**: 2-3 weeks  
- **Phase 3**: 3-4 weeks
- **Phase 4**: 2-3 weeks
- **Total**: 9-13 weeks 