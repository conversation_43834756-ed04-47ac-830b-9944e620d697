/* eslint-disable @next/next/next-script-for-ga */
import type { Metadata } from "next";
import "./globals.css";
import { Lexend } from "next/font/google";
import { Clerk<PERSON><PERSON>ider } from "@clerk/nextjs";
import { Toaster } from "react-hot-toast";
import config from "@/config";
declare global {
  interface Window {
    dataLayer: any[];
  }
}

const lexend = Lexend({ subsets: ["latin"] });

const baseUrl =
  process.env.NODE_ENV === "development"
    ? "http://localhost:3000"
    : `https://${config.domainName}`;

export const metadata: Metadata = {
  title: "Scale your business with data-backed decisions",
  description:
    "The ultimate web analytics tool for SaaS and online businesses. Track visitors, analyze behavior, and make data-driven decisions.",
  manifest: "/manifest.json",
  icons: [
    { rel: "apple-touch-icon", url: "/icon.png" },
    { rel: "icon", url: "/favicon.ico" },
  ],
  openGraph: {
    type: "website",
    url: "https://versatailor.com",
    title: "Versatailor - Web Analytics Tool",
    description:
      "The ultimate web analytics tool for SaaS and online businesses. Track visitors, analyze behavior, and make data-driven decisions.",
    images: [
      {
        url: "/og_image.png",
        width: 1200,
        height: 630,
        alt: "Versatailor Preview",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: "Versatailor - Web Analytics Tool",
    description:
      "The ultimate web analytics tool for SaaS and online businesses. Track visitors, analyze behavior, and make data-driven decisions.",
    creator: "@ziadbeshippin",
    images: {
      url: `${baseUrl}/og_image.png`,
      alt: "Versatailor Preview",
    },
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <ClerkProvider>
      <html lang="en" className="overflow-x-hidden">
        <head>
          <script
            defer
            src="https://versatailor.com/script.js"
            data-website-id="bf9e213b-fabc-4d11-a1f0-a4b15ec72f5f"
            data-domain="versatailor.com"
          ></script>
          <script
            defer
            src="https://cloud.umami.is/script.js"
            data-website-id="c2aad286-69a1-48b9-9ec7-5a901255b447"
          ></script>
          <script
            defer
            data-website-id="68326d9dfef557450c9ae4c5"
            data-domain="versatailor.com"
            src="https://datafa.st/js/script.js"
          ></script>
          <link
            rel="manifest"
            href="/manifest.json"
            crossOrigin="use-credentials"
          />
        </head>
        <body
          className={`${lexend.className} overflow-x-hidden bg-customGray text-gray-100`}
          style={{ overscrollBehaviorX: "none" }}
        >
          {children}
          <Toaster />
        </body>
      </html>
    </ClerkProvider>
  );
}
