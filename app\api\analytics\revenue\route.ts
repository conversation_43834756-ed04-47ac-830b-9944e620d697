import { NextRequest, NextResponse } from "next/server";
import { auth } from "@clerk/nextjs/server";
import connect from "@/util/db";
import Website from "@/models/website";
import Event from "@/models/event";
import { getDateRangeFromPeriod, getPreviousPeriodRange } from "@/lib/periodHelpers";

export async function GET(request: NextRequest) {
  try {
    await connect();

    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const websiteId = searchParams.get("websiteId");
    const startDate = searchParams.get("startDate");
    const endDate = searchParams.get("endDate");
    const referrer = searchParams.get("referrer");
    const location = searchParams.get("location");
    const period = searchParams.get("period") || "7d";

    // Revenue-specific filters
    const minAmount = searchParams.get("minAmount");
    const maxAmount = searchParams.get("maxAmount");
    const customerType = searchParams.get("customerType");
    const paymentMethod = searchParams.get("paymentMethod");
    const paymentType = searchParams.get("paymentType");
    const currency = searchParams.get("currency");
    const revenueStartDate = searchParams.get("revenueStartDate");
    const revenueEndDate = searchParams.get("revenueEndDate");

    // System filters
    const systemFilter = searchParams.get("system");
    const systemType = searchParams.get("systemType"); // "os", "browser", or "device"

    if (!websiteId) {
      return NextResponse.json(
        { error: "Website ID is required" },
        { status: 400 }
      );
    }

    // Verify website ownership and revenue attribution
    // Conditionally include userId and revenueAttributionEnabled based on environment
    const query = process.env.NODE_ENV === "production"
      ? { id: websiteId, userId, revenueAttributionEnabled: true }
      : { id: websiteId };

    const website = await Website.findOne(query);

    if (!website) {
      return NextResponse.json(
        { error: "Website not found or revenue attribution not enabled" },
        { status: 404 }
      );
    }

    // Calculate date range using the same helpers as visitor analytics
    let currentStart: Date, currentEnd: Date, previousStart: Date, previousEnd: Date;

    if (startDate && endDate) {
      currentStart = new Date(startDate);
      currentEnd = new Date(endDate);
    } else {
      // Use the same date range logic as visitor analytics
      const customPeriod = startDate && endDate ? {
        startDate: new Date(startDate),
        endDate: new Date(endDate)
      } : undefined;
      
      const dateRange = getDateRangeFromPeriod(period as any, customPeriod);
      currentStart = dateRange.from;
      currentEnd = dateRange.to;
    }

    // Calculate previous period using the same helper as visitor analytics
    const previousPeriod = getPreviousPeriodRange(period as any, currentStart, currentEnd);
    previousStart = previousPeriod.from;
    previousEnd = previousPeriod.to;

    // Build query filters
    const baseQuery: any = {
      websiteId,
      type: "payment",
      revenueData: { $exists: true }
    };

    if (referrer) {
      baseQuery.referrer = { $regex: referrer, $options: "i" };
    }

    if (location) {
      baseQuery["location.country"] = { $regex: location, $options: "i" };
    }

    // Apply system filters
    if (systemFilter && systemType) {
      switch (systemType) {
        case "os":
          baseQuery["userAgent.os.name"] = systemFilter;
          break;
        case "browser":
          baseQuery["userAgent.browser.name"] = systemFilter;
          break;
        case "device":
          baseQuery["userAgent.device.type"] = systemFilter;
          break;
      }
    }

    // Apply revenue-specific filters
    if (minAmount || maxAmount) {
      baseQuery["revenueData.amount"] = {};
      if (minAmount) {
        baseQuery["revenueData.amount"].$gte = parseFloat(minAmount) * 100; // Convert to cents
      }
      if (maxAmount) {
        baseQuery["revenueData.amount"].$lte = parseFloat(maxAmount) * 100; // Convert to cents
      }
    }

    if (customerType && customerType !== "all") {
      if (customerType === "new") {
        baseQuery["revenueData.isNewCustomer"] = true;
      } else if (customerType === "returning") {
        baseQuery["revenueData.isNewCustomer"] = { $ne: true };
      }
    }

    if (paymentMethod && paymentMethod !== "all") {
      baseQuery["revenueData.paymentMethod"] = paymentMethod;
    }

    if (paymentType && paymentType !== "all") {
      if (paymentType === "subscription") {
        baseQuery["revenueData.paymentType"] = { $in: ["subscription", "renewal"] };
      } else {
        baseQuery["revenueData.paymentType"] = paymentType;
      }
    }

    if (currency && currency !== "all") {
      baseQuery["revenueData.currency"] = currency;
    }

    // Use revenue date filter if provided, otherwise use general date filter
    let dateFilter;
    if (revenueStartDate && revenueEndDate) {
      dateFilter = {
        $gte: new Date(revenueStartDate),
        $lte: new Date(revenueEndDate)
      };
    } else {
      dateFilter = { $gte: currentStart, $lte: currentEnd };
    }

    // Current period query
    const currentQuery = {
      ...baseQuery,
      timestamp: dateFilter
    };

    // Previous period query (for comparison)
    const previousQuery = {
      ...baseQuery,
      timestamp: { $gte: previousStart, $lte: previousEnd }
    };

    // Only remove revenue date filter from previous query if it exists
    if (revenueStartDate && revenueEndDate) {
      const { timestamp, ...queryWithoutTimestamp } = previousQuery;
      previousQuery.timestamp = { $gte: previousStart, $lte: previousEnd };
    }

    // Execute queries
    const [currentEvents, previousEvents, totalVisitors] = await Promise.all([
      Event.find(currentQuery),
      Event.find(previousQuery),
      Event.countDocuments({
        websiteId,
        type: { $ne: "payment" },
        timestamp: { $gte: currentStart, $lte: currentEnd }
      })
    ]);

    // Calculate current period metrics
    const currentRevenue = currentEvents.reduce((sum, event) => 
      sum + (event.revenueData?.amount || 0), 0) / 100; // Convert from cents
    
    const currentOrderCount = currentEvents.length;
    const currentCustomerCount = new Set(currentEvents.map(event => 
      event.revenueData?.stripeCustomerId).filter(Boolean)).size;
    
    const currentAOV = currentOrderCount > 0 ? currentRevenue / currentOrderCount : 0;
    const currentConversionRate = totalVisitors > 0 ? (currentOrderCount / totalVisitors) * 100 : 0;
    const currentRevenuePerVisitor = totalVisitors > 0 ? currentRevenue / totalVisitors : 0;

    // Calculate previous period metrics
    const previousRevenue = previousEvents.reduce((sum, event) => 
      sum + (event.revenueData?.amount || 0), 0) / 100;
    
    const previousOrderCount = previousEvents.length;
    const previousCustomerCount = new Set(previousEvents.map(event => 
      event.revenueData?.stripeCustomerId).filter(Boolean)).size;
    
    const previousAOV = previousOrderCount > 0 ? previousRevenue / previousOrderCount : 0;

    // Calculate deltas
    const revenueDelta = previousRevenue > 0 ? 
      ((currentRevenue - previousRevenue) / previousRevenue) * 100 : 0;
    
    const aovDelta = previousAOV > 0 ? 
      ((currentAOV - previousAOV) / previousAOV) * 100 : 0;

    const orderCountDelta = previousOrderCount > 0 ? 
      ((currentOrderCount - previousOrderCount) / previousOrderCount) * 100 : 0;

    // New vs returning customers
    const newCustomers = currentEvents.filter(event => 
      event.revenueData?.isNewCustomer).length;
    const returningCustomers = currentOrderCount - newCustomers;

    // Revenue by payment type
    const oneTimeRevenue = currentEvents
      .filter(event => event.revenueData?.paymentType === "one_time")
      .reduce((sum, event) => sum + (event.revenueData?.amount || 0), 0) / 100;
    
    const subscriptionRevenue = currentEvents
      .filter(event => ["subscription", "renewal"].includes(event.revenueData?.paymentType || ""))
      .reduce((sum, event) => sum + (event.revenueData?.amount || 0), 0) / 100;

    return NextResponse.json({
      totalRevenue: {
        current: currentRevenue,
        previous: previousRevenue,
        delta: revenueDelta,
        currency: currentEvents[0]?.revenueData?.currency || "usd"
      },
      averageOrderValue: {
        current: currentAOV,
        previous: previousAOV,
        delta: aovDelta,
        currency: currentEvents[0]?.revenueData?.currency || "usd"
      },
      conversionRate: {
        current: currentConversionRate,
        totalVisitors,
        totalOrders: currentOrderCount
      },
      revenuePerVisitor: {
        current: currentRevenuePerVisitor,
        currency: currentEvents[0]?.revenueData?.currency || "usd"
      },
      orderCount: {
        current: currentOrderCount,
        previous: previousOrderCount,
        delta: orderCountDelta
      },
      customerMetrics: {
        totalCustomers: currentCustomerCount,
        newCustomers,
        returningCustomers,
        newCustomerRate: currentOrderCount > 0 ? (newCustomers / currentOrderCount) * 100 : 0
      },
      revenueByType: {
        oneTime: oneTimeRevenue,
        subscription: subscriptionRevenue,
        breakdown: [
          { type: "One-time", amount: oneTimeRevenue },
          { type: "Subscription", amount: subscriptionRevenue }
        ]
      },
      period: {
        start: currentStart,
        end: currentEnd,
        label: period
      }
    });

  } catch (error: any) {
    console.error("Revenue analytics error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
} 