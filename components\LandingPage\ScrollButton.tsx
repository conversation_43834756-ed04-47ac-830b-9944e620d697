"use client";
import React from "react";
import { useAuth, useUser } from "@clerk/nextjs";
import Image from "next/image";
import { useRouter } from "next/navigation";

const ScrollButton = ({
  title,
  type,
  className,
  eventName,
  eventDesc,
}: {
  title: string;
  type: string;
  className?: string;
  eventName: string;
  eventDesc: string;
}) => {
  const { userId } = useAuth();
  const { user, isLoaded } = useUser();
  const router = useRouter();
  function handleClick() {
    router.push("/dashboard");
    window?.datafast(eventName, {
      description: eventDesc,
    });
    window?.versatailor(eventName, {
      description: eventDesc,
    });
  }

  return (
    <button
      className={`btn btn-primary  ${
        type === "header" ? "" : "btn-wide"
      } ${className}`}
      onClick={handleClick}
    >
      {userId && !isLoaded && <p>Loading... </p>}

      {userId && isLoaded && user?.imageUrl && (
        <Image
          src={user?.imageUrl!}
          alt="User Avatar"
          width={20}
          height={20}
          className="rounded-full"
        />
      )}
      {userId ? "Proceed to Dashboard" : title}
    </button>
  );
};

export default ScrollButton;
