import { auth } from "@clerk/nextjs/server";
import { NextRequest, NextResponse } from "next/server";
import connect from "@/util/db";
import User from "@/models/user";
import Website from "@/models/website";
import Event from "@/models/event";

export async function GET(request: NextRequest) {
    try {
        const { userId } = await auth();
        
        if (!userId) {
            return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
        }

        await connect();
        
        // Get the user to check their pricing tier
        const user = await User.findOne({ clerkId: userId });
        if (!user) {
            return new NextResponse("User not found", { status: 404 });
        }

        // Get all websites belonging to the user
        const websites = await Website.find({ userId: user.clerkId });
        
        // Calculate the date for the start of the current billing cycle (30 days ago)
        const startDate = new Date();
        startDate.setDate(startDate.getDate() - 30);
        
        // Define the query for events
        const query = {
            websiteId: { $in: websites.map(website => website.id) },
            timestamp: { $gte: startDate }
        };
        
        // Count all events for the user's websites in the current billing cycle
        const totalCount = await Event.countDocuments(query);
        
        // Define the limits for different pricing tiers
        const pricingTiers: Record<string, string> = {
            "price_1QxSnGKHseo214m6T8QOwLqW": "10k", // 10k pageviews
            "price_1QxSoOKHseo214m6Pn2ATdGd": "100k", // 100k pageviews
            // Add other pricing tiers as needed
        };
        
        // Get the appropriate limit based on the price ID
        const limit = user.priceId 
            ? (pricingTiers[user.priceId] || "10k") // Default to 10k if price ID doesn't match
            : "10k"; // Default to 10k for free tier
        
        // Convert limit string to number
        const limitNumber = (() => {
            if (limit.endsWith('k')) {
                return parseFloat(limit.slice(0, -1)) * 1000;
            } else if (limit.endsWith('M')) {
                return parseFloat(limit.slice(0, -1)) * 1000000;
            }
            return parseInt(limit);
        })();
        
        // Calculate percentage of limit used
        const percentage = Math.min((totalCount / limitNumber) * 100, 100);
        
        return NextResponse.json({
            totalCount,
            limit,
            percentage,
            websites: websites.length
        });
    } catch (error) {
        console.error("Error fetching pageview counts:", error);
        return new NextResponse("Internal Error", { status: 500 });
    }
} 