import {
    <PERSON>,
    But<PERSON>,
    Container,
    Head,
    <PERSON><PERSON>,
    Hr,
    Html,
    Img,
    Preview,
    Section,
    Text,
  } from "@react-email/components";
  import * as React from "react";
  
  export default function Welcome({ firstName }: { firstName: string }) {
    return (
      <Html>
        <Head />
        <Preview>
          Welcome aboard! Let&apos;s unlock powerful insights with Versatailor 🚀
        </Preview>
        <Body style={main}>
          <Container style={container}>
            <Img
              src={`https://www.versatailor.com/_next/image?url=%2Ficon.png&w=256&q=75`}
              width="120"
              height="120"
              alt="Versatailor Logo"
              style={logo}
            />
            <Heading style={heading}>Welcome aboard, {firstName}! 🎉</Heading>
            <Text style={paragraph}>
              You&apos;ve just taken the first step towards deeper insights and smarter decisions for your online business. Versatailor is here to help you understand your visitors like never before.
            </Text>
            <Text style={paragraph}>Here&apos;s what you can do right away:</Text>
            <Text style={listItem}>
              • Analyze your web traffic hour-by-hour to spot spikes and trends 📈
            </Text>
            <Text style={listItem}>
              • Exclude authenticated visits to measure your true landing page conversion rates 🎯
            </Text>
            <Text style={listItem}>
              • Gain actionable insights to optimize your website performance 🚀
            </Text>
            <Section style={btnContainer}>
              <Button style={button} href="https://versatailor.com/dashboard">
              Begin Your Data Journey →
              </Button>
            </Section>
            <Text style={paragraph}>
              <strong>Pro tip:</strong> Add <strong><EMAIL></strong>{" "}
              and <strong><EMAIL></strong> to your contacts to
              ensure you don&apos;t miss important updates. We&apos;ll only send essential notifications and feature updates (max twice a month).
            </Text>
            <Text style={paragraph}>
              Questions or feedback? Just hit reply—I personally read and respond to every email! Your success is our priority. 💪
            </Text>
            <Text style={signature}>
              Rooting for your growth,
              <br />
              Ziad
              <br />
              <span style={founderTitle}>Founder, Versatailor</span>
            </Text>
            <Hr style={hr} />
            <Text style={footer}>
              © {new Date().getFullYear()} Versatailor. All rights reserved.
              <br />
              You&apos;re receiving this because you signed up for Versatailor.
              <br />
              <a
                href="https://www.versatailor.com/privacy-policy"
                style={footerLink}
              >
                Privacy Policy
              </a>
              <br />
              <a href="https://www.versatailor.com/tos" style={footerLink}>
                Terms of Service
              </a>
            </Text>
          </Container>
        </Body>
      </Html>
    );
  }
  
  const main = {
    backgroundColor: "#1a1a1a",
    fontFamily:
      '-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,Oxygen-Sans,Ubuntu,Cantarell,"Helvetica Neue",sans-serif',
    padding: "30px 0",
  };
  
  const container = {
    margin: "0 auto",
    padding: "32px 20px",
    maxWidth: "600px",
    borderRadius: "8px",
  };
  
  const logo = {
    margin: "0 auto",
    borderRadius: "46px",
    display: "block",
  };
  
  const heading = {
    color: "#ffffff",
    fontSize: "28px",
    fontWeight: "bold",
    textAlign: "center" as const,
    margin: "30px 0",
  };
  
  const paragraph = {
    fontSize: "16px",
    lineHeight: "1.6",
    color: "#e6e6e6",
    margin: "16px 0",
  };
  
  const btnContainer = {
    textAlign: "center" as const,
    margin: "32px 0",
  };
  
  const button = {
    backgroundColor: "#5F51E8",
    borderRadius: "6px",
    color: "#fff",
    fontSize: "16px",
    fontWeight: "bold",
    textDecoration: "none",
    textAlign: "center" as const,
    display: "inline-block",
    padding: "14px 32px",
    margin: "0 auto",
    boxShadow: "0 2px 4px rgba(0, 0, 0, 0.2)",
  };
  
  const hr = {
    borderColor: "#404040",
    margin: "32px 0",
  };
  
  const signature = {
    fontSize: "16px",
    lineHeight: "1.6",
    color: "#e6e6e6",
    margin: "24px 0",
  };
  
  const footer = {
    color: "#808080",
    fontSize: "12px",
    textAlign: "center" as const,
    margin: "16px 0 0",
    lineHeight: "1.5",
  };
  
  const listItem = {
    fontSize: "16px",
    lineHeight: "1.6",
    color: "#e6e6e6",
    margin: "8px 0 8px 0",
  };
  
  const founderTitle = {
    color: "#808080",
    fontSize: "14px",
  };
  
  const footerLink = {
    color: "#808080",
    textDecoration: "underline",
  };
  