import { useState, useEffect } from 'react';

export function useAnalyticsRealtime(websiteId: string) {
  const [realtimeVisitors, setRealtimeVisitors] = useState(0);

  useEffect(() => {
    if (!websiteId) return;

    const controller = new AbortController();
    let isMounted = true;

    const fetchCurrentVisitors = async () => {
      if (!isMounted) return;
      try {
        const response = await fetch(
          `/api/current-visitors?websiteId=${websiteId}`,
          { signal: controller.signal }
        );
        if (!response.ok) {
          console.error(`Failed to fetch current visitors: ${response.status}`);
          return;
        }
        const data = await response.json();
        if (isMounted) {
          setRealtimeVisitors(data.count || 0);
        }
      } catch (error) {
        if (!controller.signal.aborted) {
          console.error("Error fetching current visitors count:", error);
        }
      }
    };

    fetchCurrentVisitors();

    const POLLING_INTERVAL = process.env.NODE_ENV === "production" ? 20000 : 200000;
    const intervalId = setInterval(fetchCurrentVisitors, POLLING_INTERVAL);

    return () => {
      isMounted = false;
      clearInterval(intervalId);
      controller.abort();
    };
  }, [websiteId]);

  return realtimeVisitors;
} 