"use client";

import React, { useEffect, useState } from "react";
import { useAuth } from "@clerk/nextjs";
import axios from "axios";
import { Loader2, HelpCircle, Info } from "lucide-react";
import Link from "next/link";
import CustomerPortalButton from "@/components/CustomerPortalButton";
import PricingComponent from "@/components/LandingPage/PricingSection"; // Import the PricingComponent
import FAQ from "@/components/LandingPage/FAQ";

// Define the structure for billing data
interface BillingData {
  accessUntil: Date;
  priceId: string | null;
}

// Define the structure for pageview counts
interface PageviewCounts {
  totalCount: number;
  limit: string;
  percentage: number;
  websites: number;
}

const BillingPage = () => {
  const { isLoaded, userId } = useAuth();
  const [loading, setLoading] = useState(true);
  const [billingData, setBillingData] = useState<BillingData | null>(null);
  const [pageviewCounts, setPageviewCounts] = useState<PageviewCounts | null>(
    null
  );
  const [error, setError] = useState<string | null>(null);
  const [showPageViewInfo, setShowPageViewInfo] = useState(false);

  // Calculate billing cycle start date (30 days before access until date)
  const getBillingCycleStart = (): string => {
    if (!billingData?.accessUntil) return "N/A";

    const accessUntil = new Date(billingData.accessUntil);
    const billingStart = new Date(accessUntil);
    billingStart.setDate(billingStart.getDate() - 30);

    return billingStart.toLocaleDateString();
  };

  useEffect(() => {
    const fetchData = async () => {
      if (!isLoaded || !userId) return;

      try {
        setLoading(true);

        // Fetch user's billing data
        const billingResponse = await axios.get("/api/user");
        const billing = billingResponse.data;

        // Check if user has an active subscription (priceId exists)
        if (!billing || !billing.priceId) {
          // If no priceId, likely free trial or no subscription yet.
          // Set error state to trigger showing the pricing component.
          // We might not need to fetch pageviews if there's no subscription.
          setError("No active subscription found. Choose a plan below.");
          setBillingData(null); // Ensure billing data is null
          setPageviewCounts(null); // Ensure pageview counts are null
        } else {
          // If priceId exists, proceed to fetch pageviews
          setBillingData(billing);
          const pageviewsResponse = await axios.get("/api/user/pageviews");
          setPageviewCounts(pageviewsResponse.data);
          setError(null); // Clear any previous error
        }
      } catch (err) {
        console.error("Error fetching billing data:", err);
        // Keep a generic error for actual fetch failures
        setError("Failed to load billing information. Please try again later.");
        setBillingData(null);
        setPageviewCounts(null);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [isLoaded, userId]);

  // if (!isLoaded || loading) {
  //     // Keep the loading skeleton as is
  //     return (
  //         <div className="min-h-screen bg-customGray text-white">
  //             <div className="container mx-auto px-4 py-8">
  //                 {/* Skeleton content remains the same */}
  //                 <div className="flex items-center gap-2 mb-6">
  //                     <div className="h-5 w-5 bg-gray-800 rounded-md animate-pulse"></div>
  //                     <div className="h-8 w-36 bg-gray-800 rounded-md animate-pulse"></div>
  //                 </div>
  //                 <div className="bg-gray-800/50 backdrop-blur-sm rounded-xl p-6 border border-gray-700 shadow-xl mb-8">
  //                     {/* Skeleton content */}
  //                 </div>
  //                 <div className="bg-gray-800/50 backdrop-blur-sm rounded-xl p-6 border border-gray-700 shadow-xl mb-8">
  //                     {/* Skeleton content */}
  //                 </div>
  //             </div>
  //         </div>
  //     );
  // }

  // If there's an error OR if there's no billing data (implying free trial/no subscription)
  if (error || !billingData) {
    return (
      <div className="min-h-screen bg-customGray text-white">
        <div className="container mx-auto px-4 py-8">
          <div className="flex items-center gap-2 mb-6">
            <Link
              href="/dashboard"
              className="text-white hover:text-indigo-300 flex items-center gap-2"
            >
              ←
            </Link>
            <h1 className="text-2xl font-bold">Billing</h1>
          </div>
          {/* Display error message if one exists */}
          {/* {error && (
                        <div className="bg-gray-800 p-4 rounded-lg text-red-400 border border-red-500/50 mb-8">
                            {error}
                        </div>
                    )} */}
          {/* Render the Pricing Component */}
          <PricingComponent />
          <FAQ />
        </div>
      </div>
    );
  }

  // Original view for users with active billing data
  return (
    <div className="min-h-screen bg-customGray text-white">
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center gap-2 mb-6">
          <Link
            href="/dashboard"
            className="text-white hover:text-indigo-300 flex items-center gap-2"
          >
            ←
          </Link>
          <h1 className="text-2xl font-bold">Billing</h1>
        </div>
        {/* Subscription Information */}
        <div className="bg-gray-800/50 backdrop-blur-sm rounded-xl p-6 border border-gray-700 shadow-xl mb-8">
          <h2 className="text-xl font-semibold mb-6">Subscription Details</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="bg-gray-800 rounded-lg p-5">
              <div className="text-sm text-gray-400">Plan</div>
              <div className="text-xl font-bold mt-1">
                {billingData?.priceId
                  ? `Premium (${pageviewCounts?.limit} pageviews)`
                  : "Free Trial"}{" "}
                {/* Should ideally not show Free Trial here anymore */}
              </div>
            </div>
            <div className="bg-gray-800 rounded-lg p-5">
              <div className="text-sm text-gray-400">Billing Cycle Started</div>
              <div className="text-xl font-bold mt-1">
                {getBillingCycleStart()}
              </div>
            </div>
            <div className="bg-gray-800 rounded-lg p-5">
              <div className="text-sm text-gray-400">Access Until</div>
              <div className="text-xl font-bold mt-1">
                {billingData?.accessUntil
                  ? new Date(billingData.accessUntil).toLocaleDateString()
                  : "N/A"}
              </div>
            </div>
          </div>
        </div>

        {/* Usage Information */}
        <div className="bg-gray-800/50 backdrop-blur-sm rounded-xl p-6 border border-gray-700 shadow-xl">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-semibold">Current Usage</h2>
            <button
              onClick={() => setShowPageViewInfo(!showPageViewInfo)}
              className="text-gray-400 hover:text-white transition-colors"
              aria-label="Show pageview information"
            >
              <HelpCircle size={20} />
            </button>
          </div>

          {showPageViewInfo && (
            <div className="bg-gray-800 rounded-lg p-5 mb-6 text-sm">
              <div className="flex items-start">
                <Info className="h-5 w-5 text-blue-400 mr-3 mt-0.5 flex-shrink-0" />
                <div>
                  <p className="mb-2 font-semibold">What are pageviews?</p>
                  <p className="mb-2 text-gray-300">
                    Each time a visitor loads or reloads a page on any of your
                    websites, it counts as one pageview. Custom events also
                    count toward your total usage.
                  </p>
                  <p className="text-gray-300">
                    Your billing cycle started on{" "}
                    <span className="font-semibold">
                      {getBillingCycleStart()}
                    </span>{" "}
                    and resets every 30 days from this date. Events are counted
                    across all of your websites.
                  </p>
                </div>
              </div>
            </div>
          )}

          <div className="bg-gray-800 rounded-lg p-5 mb-6">
            <div className="text-sm text-gray-400 mb-2">
              Pageviews this billing cycle
            </div>
            <div className="flex justify-between mb-2">
              <div className="text-xl font-bold">
                {pageviewCounts?.totalCount.toLocaleString()}
              </div>
              <div className="text-xl font-bold text-gray-400">
                {pageviewCounts?.limit}
              </div>
            </div>
            <div className="w-full bg-gray-700 rounded-full h-1">
              <div
                className={`h-1 rounded-full ${
                  pageviewCounts && pageviewCounts.percentage > 90
                    ? "bg-red-500"
                    : "bg-blue-500"
                }`}
                style={{ width: `${pageviewCounts?.percentage || 0}%` }}
              ></div>
            </div>
            <div className="mt-2 text-xs text-right text-gray-400">
              Billing cycle: {getBillingCycleStart()} -{" "}
              {billingData?.accessUntil
                ? new Date(billingData.accessUntil).toLocaleDateString()
                : "N/A"}
            </div>
          </div>

          {pageviewCounts && pageviewCounts.percentage > 90 && (
            <div className="bg-gray-800 rounded-lg p-5 mb-6 border-l-4 border-red-500">
              <p className="font-semibold mb-1 text-red-400">
                You are approaching your pageview limit!
              </p>
              <p className="text-gray-300">
                Consider upgrading your plan to avoid service interruption. Once
                you reach your limit, new pageviews may not be recorded.
              </p>
            </div>
          )}

          <div className="bg-gray-800 rounded-lg p-5">
            <h3 className="text-lg font-semibold mb-3">Websites Breakdown</h3>
            <p className="text-gray-300 mb-6">
              You have {pageviewCounts?.websites || 0} website
              {pageviewCounts?.websites !== 1 ? "s" : ""} tracking events. Check
              your dashboard for detailed analytics.
            </p>
            <div className="flex flex-col sm:flex-row gap-4">
              <CustomerPortalButton />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BillingPage;
