"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import React from "react";

const docsNavigation = [
  {
    title: "Get Started",
    items: [
      { name: "Set up your account", href: "/docs/set-up-your-account" },
      { name: "Import your data", href: "/docs/import-your-data" },
      { name: "Find your tracking script", href: "/docs/find-tracking-script" },
    ],
  },
  {
    title: "Website Settings",
    items: [
      { name: "Exclude authenticated visits", href: "/docs/exclude-auth-visits" },
      { name: "Exclue your own visits", href: "/docs/exclude-your-own-visits" },
    ],
  },
  {
    title: "Events",
    items: [
      {
        name: "Track your events",
        href: "/docs/track-events",
      },
    ],
  },
];

// Helper function to convert path segments to title case
const toTitleCase = (str: string) => {
  return str
    .replace(/-/g, " ") // Replace hyphens with spaces
    .replace(
      /\w\S*/g,
      (txt) => txt.charAt(0).toUpperCase() + txt.substring(1).toLowerCase()
    );
};

// Helper function to find parent section title
const findParentSection = (currentPath: string) => {
  for (const section of docsNavigation) {
    if (section.items.some(item => item.href === currentPath)) {
      return section.title;
    }
  }
  return null;
};

export default function DocsBreadcrumb() {
  const pathname = usePathname();

  // Don't render breadcrumbs on the base /docs page itself
  if (pathname === "/docs") {
    return null;
  }

  // Build the breadcrumb items
  const breadcrumbItems = [];

  // Always add Docs as the first item
  breadcrumbItems.push(
    <Link href="/docs" key="docs" className="text-gray-400 hover:text-white transition-colors">
      Docs
    </Link>
  );

  // Find the current page and its section
  const currentPage = docsNavigation
    .flatMap(section => section.items)
    .find(item => item.href === pathname);

  const currentSection = docsNavigation.find(section =>
    section.items.some(item => item.href === pathname)
  );

  // Add section if found
  if (currentSection) {
    breadcrumbItems.push(
      <span key="separator-1" className="mx-2 text-gray-600">/</span>,
      <span key="section" className="text-gray-400">{currentSection.title}</span>
    );
  }

  // Add current page if found
  if (currentPage) {
    breadcrumbItems.push(
      <span key="separator-2" className="mx-2 text-gray-600">/</span>,
      <span key="current" className="text-white font-medium">{currentPage.name}</span>
    );
  }

  return (
    <div className="px-4 md:px-6 max-w-screen-2xl mx-auto border-b border-gray-800/30">
      <div className="flex items-center text-sm py-2 overflow-x-auto no-scrollbar">
        {breadcrumbItems}
      </div>
    </div>
  );
}
