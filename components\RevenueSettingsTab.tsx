"use client";
import { Website } from "@/lib/types";
import React, { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "./ui/alert-dialog";
import toast from "react-hot-toast";

interface RevenueEvent {
  id: string;
  timestamp: Date;
  amount: number;
  currency: string;
  customerEmail?: string;
  stripeChargeId?: string;
  referrer?: string;
  location?: {
    country?: string;
    city?: string;
  };
}

type Props = {
  website: Website;
  onUpdate: (updatedWebsite: Website) => void;
};

function RevenueSettingsTab({ website, onUpdate }: Props) {
  const [isLoading, setIsLoading] = useState(false);
  const [stripeApiKey, setStripeApiKey] = useState("");
  const [webhookStatus, setWebhookStatus] = useState<{
    isActive: boolean;
    lastReceived?: Date;
    error?: string;
  } | null>(null);
  const [recentRevenueEvents, setRecentRevenueEvents] = useState<RevenueEvent[]>([]);
  const [testingWebhook, setTestingWebhook] = useState(false);
  const [currencyDisplay, setCurrencyDisplay] = useState("usd");

  useEffect(() => {
    if (website.revenueAttributionEnabled) {
      fetchWebhookStatus();
      fetchRecentRevenueEvents();
    }
  }, [website.id, website.revenueAttributionEnabled]);

  const fetchWebhookStatus = async () => {
    try {
      const response = await fetch(`/api/stripe/webhook-status/${website.id}`);
      if (response.ok) {
        const data = await response.json();
        setWebhookStatus(data);
      }
    } catch (error) {
      console.error("Failed to fetch webhook status:", error);
    }
  };

  const fetchRecentRevenueEvents = async () => {
    try {
      const response = await fetch(`/api/analytics/revenue-events/${website.id}?limit=10`);
      if (response.ok) {
        const data = await response.json();
        setRecentRevenueEvents(data.events || []);
      }
    } catch (error) {
      console.error("Failed to fetch recent revenue events:", error);
    }
  };

  const handleToggleRevenueAttribution = async (enabled: boolean) => {
    if (!enabled) {
      // Disable revenue attribution and disconnect Stripe
      await handleDisconnectStripe();
      return;
    }

    if (!stripeApiKey.trim()) {
      toast.error("Please enter a Stripe API key before enabling revenue attribution");
      return;
    }

    setIsLoading(true);
    try {
      const response = await fetch("/api/stripe/webhook-setup", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          websiteId: website.id,
          stripeApiKey: stripeApiKey.trim(),
        }),
      });

      if (response.ok) {
        const data = await response.json();
        await updateRevenueSettings({
          revenueAttributionEnabled: true,
          stripeWebhookId: data.webhookId,
        });
        toast.success("Revenue attribution enabled successfully!");
        fetchWebhookStatus();
        setStripeApiKey(""); // Clear the input after successful setup
      } else {
        const error = await response.json();
        toast.error(error.message || "Failed to setup Stripe webhook");
      }
    } catch (error) {
      toast.error("An error occurred while setting up revenue attribution");
      console.error(error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleDisconnectStripe = async () => {
    setIsLoading(true);
    try {
      // Use the webhook setup API to properly disable and cleanup
      const response = await fetch("/api/stripe/webhook-setup", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          websiteId: website.id,
          action: "disable",
        }),
      });

      if (response.ok) {
        // Update the website state to reflect the changes
        await updateRevenueSettings({
          revenueAttributionEnabled: false,
          stripeWebhookId: undefined,
        });
        setWebhookStatus(null);
        setRecentRevenueEvents([]);
        toast.success("Stripe disconnected successfully!");
      } else {
        const error = await response.json();
        toast.error(error.message || "Failed to disconnect Stripe");
      }
    } catch (error) {
      toast.error("Failed to disconnect Stripe");
      console.error(error);
    } finally {
      setIsLoading(false);
    }
  };

  const updateRevenueSettings = async (updates: Partial<Website>) => {
    try {
      const response = await fetch(`/api/websites/${website.id}`, {
        method: "PATCH",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(updates),
      });

      if (response.ok) {
        const updatedWebsite = await response.json();
        onUpdate(updatedWebsite);
      } else {
        throw new Error("Failed to update website settings");
      }
    } catch (error) {
      toast.error("Failed to update settings");
      throw error;
    }
  };

  const testWebhookConnection = async () => {
    setTestingWebhook(true);
    try {
      const response = await fetch(`/api/stripe/test-webhook/${website.id}`, {
        method: "POST",
      });

      if (response.ok) {
        toast.success("Webhook connection test successful!");
        fetchWebhookStatus();
      } else {
        const error = await response.json();
        toast.error(error.message || "Webhook test failed");
      }
    } catch (error) {
      toast.error("Failed to test webhook connection");
    } finally {
      setTestingWebhook(false);
    }
  };

  const formatCurrency = (amount: number, currency: string = "usd") => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: currency.toUpperCase(),
    }).format(amount / 100);
  };

  return (
    <div className="p-6 space-y-8">
      {/* Revenue Attribution Toggle */}
      <div className="space-y-4">
        <h2 className="text-xl font-semibold flex items-center gap-2">
          <svg
            className="w-5 h-5 text-green-500"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"
            />
          </svg>
          Revenue Attribution
        </h2>
        
        <div className="flex items-center justify-between p-4 bg-gray-800/50 rounded-lg border border-gray-700/50">
          <div className="space-y-1">
            <p className="font-medium">Enable Revenue Tracking</p>
            <p className="text-sm text-gray-400">
              Track and attribute revenue to visitor sources and journeys
            </p>
          </div>
          <Switch
            checked={website.revenueAttributionEnabled}
            onCheckedChange={handleToggleRevenueAttribution}
            disabled={isLoading}
          />
        </div>
      </div>

      {/* Stripe Configuration */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium">Stripe Configuration</h3>
        
        {!website.revenueAttributionEnabled ? (
          // Show API key input when not connected
          <div className="space-y-3">
            <div>
              <Label htmlFor="stripe-api-key">Stripe Restricted API Key</Label>
              <div className="mt-1 space-y-2">
                <Input
                  id="stripe-api-key"
                  type="password"
                  placeholder="rk_test_... or rk_live_..."
                  value={stripeApiKey}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) => setStripeApiKey(e.target.value)}
                  className="bg-gray-800 border-gray-600"
                />
                <p className="text-xs text-gray-400">
                  Create a restricted API key with webhook and charge read permissions. Test keys (rk_test_) and live keys (rk_live_) are both supported.{" "}
                  <a
                    href="https://dashboard.stripe.com/apikeys/create?name=VersaTailor&permissions%5B%5D=rak_charge_read&permissions%5B%5D=rak_webhook_write"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-blue-400 hover:text-blue-300"
                  >
                    Create API Key →
                  </a>
                </p>
              </div>
            </div>
          </div>
        ) : (
          // Show connected status when already configured
          <div className="space-y-3">
            <div className="p-4 bg-gray-800/50 rounded-lg border border-gray-700/50">
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center gap-3">
                  <div className="w-2 h-2 rounded-full bg-green-500" />
                  <span className="font-medium">Stripe Connected</span>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleDisconnectStripe}
                  disabled={isLoading}
                  className="border-gray-600 !text-gray-300 hover:!border-red-500 hover:!text-red-400 bg-transparent"
                >
                  {isLoading ? "Disconnecting..." : "Disconnect"}
                </Button>
              </div>
              <p className="text-sm text-gray-400">
                Revenue attribution is active and tracking payments.
              </p>
            </div>

            {/* Webhook Status */}
            <div className="p-4 bg-gray-800/50 rounded-lg border border-gray-700/50">
              <div className="flex items-center justify-between mb-3">
                <h4 className="font-medium">Webhook Status</h4>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={testWebhookConnection}
                  disabled={testingWebhook}
                  className="border-gray-600 !text-gray-300 hover:!text-gray-900 hover:!bg-gray-100 bg-transparent"
                >
                  {testingWebhook ? "Testing..." : "Test Connection"}
                </Button>
              </div>
              
              {webhookStatus ? (
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <div
                      className={`w-2 h-2 rounded-full ${
                        webhookStatus.isActive ? "bg-green-500" : "bg-red-500"
                      }`}
                    />
                    <span className="text-sm">
                      {webhookStatus.isActive ? "Active" : "Inactive"}
                    </span>
                  </div>
                  {webhookStatus.lastReceived && (
                    <p className="text-xs text-gray-400">
                      Last event: {new Date(webhookStatus.lastReceived).toLocaleString()}
                    </p>
                  )}
                  {webhookStatus.error && (
                    <p className="text-xs text-red-400">{webhookStatus.error}</p>
                  )}
                </div>
              ) : (
                <p className="text-sm text-gray-400">Loading webhook status...</p>
              )}
            </div>
          </div>
        )}
      </div>

      {/* Revenue Configuration */}
      {website.revenueAttributionEnabled && (
        <div className="space-y-4">
          <h3 className="text-lg font-medium">Revenue Configuration</h3>
          
          <div className="space-y-3">
            <div className="space-y-2">
              <Label htmlFor="currency-display">Default Currency Display</Label>
              <select
                id="currency-display"
                value={currencyDisplay}
                onChange={(e: React.ChangeEvent<HTMLSelectElement>) => setCurrencyDisplay(e.target.value)}
                className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-md text-sm max-w-xs"
              >
                <option value="usd">USD ($)</option>
                <option value="eur">EUR (€)</option>
                <option value="gbp">GBP (£)</option>
                <option value="cad">CAD (C$)</option>
              </select>
              <p className="text-xs text-gray-400">
                Choose how revenue amounts are displayed in analytics
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Recent Revenue Events */}
      {website.revenueAttributionEnabled && recentRevenueEvents.length > 0 && (
        <div className="space-y-4">
          <h3 className="text-lg font-medium">Recent Revenue Events</h3>
          
          <div className="bg-gray-800/50 rounded-lg border border-gray-700/50 overflow-hidden">
            <div className="overflow-x-auto">
              <table className="w-full text-sm">
                <thead className="bg-gray-700/50">
                  <tr>
                    <th className="px-4 py-3 text-left">Date</th>
                    <th className="px-4 py-3 text-left">Amount</th>
                    <th className="px-4 py-3 text-left">Customer</th>
                    <th className="px-4 py-3 text-left">Source</th>
                    <th className="px-4 py-3 text-left">Location</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-700/50">
                  {recentRevenueEvents.map((event) => (
                    <tr key={event.id} className="hover:bg-gray-700/30">
                      <td className="px-4 py-3">
                        {new Date(event.timestamp).toLocaleDateString()}
                      </td>
                      <td className="px-4 py-3 font-medium text-green-400">
                        {formatCurrency(event.amount, event.currency)}
                      </td>
                      <td className="px-4 py-3">
                        {event.customerEmail || "Unknown"}
                      </td>
                      <td className="px-4 py-3">
                        {event.referrer || "Direct"}
                      </td>
                      <td className="px-4 py-3">
                        {event.location?.city && event.location?.country
                          ? `${event.location.city}, ${event.location.country}`
                          : "Unknown"}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
            
            <div className="p-4 border-t border-gray-700/50">
              <Button
                variant="outline"
                size="sm"
                className="w-full border-gray-600"
                onClick={() => window.open(`/dashboard/${website.id}?tab=revenue`, '_blank')}
              >
                View Full Revenue Analytics →
              </Button>
            </div>
          </div>
        </div>
      )}

    </div>
  );
}

export default RevenueSettingsTab; 