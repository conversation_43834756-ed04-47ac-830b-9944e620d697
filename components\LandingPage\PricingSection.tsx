"use client";

import { useState } from "react";
import { Check } from "lucide-react";
import { useAuth, useUser } from "@clerk/nextjs";
import toast from "react-hot-toast";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { sliderValues } from "@/lib/utils";
import config from "@/config";

export default function PricingComponent() {
  const [sliderValue, setSliderValue] = useState(0);
  const [billingCycle, setBillingCycle] = useState("monthly");
  const { isSignedIn, user } = useUser();

  const email = user?.primaryEmailAddress?.emailAddress;

  function getCurrentValue() {
    const closest = sliderValues.reduce((prev, curr) => {
      return Math.abs(curr.value - sliderValue) <
        Math.abs(prev.value - sliderValue)
        ? curr
        : prev;
    });
    return closest.label;
  }

  function getCurrentPrice() {
    const closest = sliderValues.reduce((prev, curr) => {
      return Math.abs(curr.value - sliderValue) <
        Math.abs(prev.value - sliderValue)
        ? curr
        : prev;
    });

    // Find matching price from config based on events count and billing cycle
    const matchingPrice = config.stripe.plans.find(
      (plan) =>
        plan.features[0].name
          .toLowerCase()
          .includes(closest.label.toLowerCase()) &&
        plan.frequency.toLowerCase() === billingCycle.toLowerCase()
    );

    return {
      ...closest,
      price:
        matchingPrice && matchingPrice.price !== undefined
          ? matchingPrice.price.toString()
          : "0",
      testPriceId: matchingPrice?.priceId || "",
    };
  }

  function getYearlyPrice() {
    // Get the current tier based on slider
    const closest = sliderValues.reduce((prev, curr) => {
      return Math.abs(curr.value - sliderValue) <
        Math.abs(prev.value - sliderValue)
        ? curr
        : prev;
    });

    // Find both monthly and yearly prices for this tier
    const monthlyPrice = config.stripe.plans.find(
      (plan) =>
        plan.features[0].name
          .toLowerCase()
          .includes(closest.label.toLowerCase()) && plan.frequency === "monthly"
    );

    const yearlyPrice = config.stripe.plans.find(
      (plan) =>
        plan.features[0].name
          .toLowerCase()
          .includes(closest.label.toLowerCase()) && plan.frequency === "annual"
    );

    return {
      original: monthlyPrice?.price ? monthlyPrice.price * 12 : 0,
      discounted: yearlyPrice?.price || 0,
    };
  }

  function handleSliderChange(value: number) {
    const snapValue = sliderValues.reduce((prev, curr) => {
      return Math.abs(curr.value - value) < Math.abs(prev.value - value)
        ? curr
        : prev;
    });
    setSliderValue(snapValue.value);
  }
  async function handleBuyNow(priceId: string) {
    if (!isSignedIn) {
      window.location.href = "/dashboard";
      return;
    }

    try {
      const response = await fetch("/api/stripe/create-checkout", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          priceId,
          email,
        }),
      });

      if (!response.ok) {
        throw new Error("Error creating checkout session");
      }

      const { url } = await response.json();

      if (url) {
        window.location.href = url;
      }
    } catch (error) {
      toast.error("Error creating checkout session");
    }

    window?.versatailor(
      `attempted_purchase_${priceId}`,
      `User attempted to purchase ${priceId}`
    );
  }
  return (
    <div className="flex items-center justify-center w-full py-8" id="pricing">
      <div className="w-full max-w-2xl px-4">
        <div className="text-center mb-8">
          <span className="text-lg font-medium tracking-wider text-zinc-400 uppercase">
            Pricing
          </span>
        </div>
        {/* Title section */}
        <div className="text-center mb-8">
          <h2 className="text-3xl font-bold tracking-tighter sm:text-5xl xl:text-6xl/none mb-4 text-zinc-100">
            Simple, <span className="text-[#204ec8]">transparent</span> pricing
          </h2>
          <p className="text-zinc-300 md:text-xl mx-auto max-w-2xl">
            Start free for 14 days, then pay only for what you use. No hidden
            fees, no surprises.
          </p>
        </div>

        {/* Slider section */}
        <div className="text-center mb-4">
          <span className="inline-block px-3 py-1 bg-gradient-to-b from-zinc-800/30 to-zinc-900/30 backdrop-blur-sm border border-zinc-800/50 rounded-full text-sm text-zinc-300">
            Up to {getCurrentValue()} events
          </span>
        </div>

        <div className="flex items-center gap-2 mb-6">
          <span className="text-xs text-zinc-400">10k</span>
          <div className="relative flex-1 h-3 bg-zinc-800/30 rounded-full ml-2 border border-zinc-800/50">
            <input
              type="range"
              min="0"
              max="100"
              step="0.1"
              value={sliderValue}
              onChange={(e) => handleSliderChange(parseFloat(e.target.value))}
              className="absolute w-[calc(100%+20px)] -left-[10px] h-12 -top-4 opacity-0 cursor-grab active:cursor-grabbing z-20"
            />
            <div
              className="absolute h-full rounded-full bg-[#204ec8] pointer-events-none"
              style={{ width: `${sliderValue}%` }}
            />
            <div
              className="absolute top-1/2 -translate-y-1/2 -ml-[10px] w-5 h-5 rounded-full bg-white border-2 border-[#204ec8] pointer-events-none z-10 hover:scale-110 transition-transform"
              style={{ left: `${sliderValue}%` }}
            />
          </div>
          <span className="text-xs text-zinc-400">10M+</span>
        </div>

        {/* Card */}
        <div className="bg-gradient-to-b from-zinc-800/30 to-zinc-900/30 backdrop-blur-sm border border-zinc-800/50 rounded-xl p-4">
          {/* Toggle */}
          <div className="flex p-1 bg-zinc-900/30 border border-zinc-800/50 rounded-lg mb-4">
            <button
              className={`flex-1 py-2 px-4 rounded-lg text-sm font-medium transition ${
                billingCycle === "monthly"
                  ? "bg-zinc-800/30 text-white"
                  : "text-zinc-400"
              }`}
              onClick={() => setBillingCycle("monthly")}
            >
              Monthly
            </button>
            <button
              className={`flex-1 py-2 px-4 rounded-lg text-sm font-medium transition relative ${
                billingCycle === "yearly"
                  ? "bg-zinc-800/30 text-white"
                  : "text-zinc-400"
              }`}
              onClick={() => setBillingCycle("yearly")}
            >
              Yearly
              {billingCycle === "yearly" && (
                <span className="absolute -top-1 -right-1 bg-[#204ec8] text-white text-[10px] px-1.5 py-0.5 rounded">
                  2 months free
                </span>
              )}
              {billingCycle !== "yearly" && (
                <span className="absolute -top-1 -right-1 bg-[#204ec8] text-white text-[10px] px-1.5 py-0.5 rounded opacity-70">
                  2 months free
                </span>
              )}
            </button>
          </div>

          {/* Pricing */}
          <div className="mb-4">
            <div className="flex items-baseline flex-wrap gap-2">
              {billingCycle === "monthly" ? (
                <>
                  <span className="text-4xl font-bold text-white">
                    ${getCurrentPrice().price}
                  </span>
                  <span className="text-zinc-400">/month</span>
                </>
              ) : (
                <>
                  <span className="text-zinc-400 line-through text-2xl">
                    ${getYearlyPrice().original}
                  </span>
                  <span className="text-4xl font-bold text-white">
                    ${getYearlyPrice().discounted}
                  </span>
                  <span className="text-zinc-400">/year</span>
                </>
              )}
            </div>
          </div>

          {/* Features */}
          <div className="space-y-2 mb-4">
            <div className="flex items-start">
              <Check className="h-5 w-5 text-[#204ec8] mr-2 mt-0.5 flex-shrink-0" />
              <div>
                <span className="text-white">{getCurrentValue()}</span>
                <span className="text-zinc-400"> monthly </span>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger>
                      <span className="text-white underline decoration-dotted cursor-help">
                        events
                      </span>
                    </TooltipTrigger>
                    <TooltipContent className="bg-zinc-800 text-zinc-100 border border-zinc-700 text-sm max-w-[250px]">
                      Events are user interactions like page views, clicks, or
                      custom actions tracked on your website
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
            </div>
            <div className="flex items-start">
              <Check className="h-5 w-5 text-[#204ec8] mr-2 mt-0.5 flex-shrink-0" />
              <span className="text-white">Import your data</span>
            </div>
            <div className="flex items-start">
              <Check className="h-5 w-5 text-[#204ec8] mr-2 mt-0.5 flex-shrink-0" />
              <span className="text-white">Unlimited websites</span>
            </div>
            <div className="flex items-start">
              <Check className="h-5 w-5 text-[#204ec8] mr-2 mt-0.5 flex-shrink-0" />
              <span className="text-white">Web analytics</span>
            </div>
            <div className="flex items-start">
              <Check className="h-5 w-5 text-[#204ec8] mr-2 mt-0.5 flex-shrink-0" />
              <span className="text-white">
                Detailed hour-by-hour breakdown
              </span>
            </div>
            <div className="flex items-start">
              <Check className="h-5 w-5 text-[#204ec8] mr-2 mt-0.5 flex-shrink-0" />
              <span className="text-white">
                Exclude authenticated users from your data
              </span>
            </div>
          </div>

          {/* CTA Button */}
          <button
            onClick={() => {
              const closest = sliderValues.reduce((prev, curr) => {
                return Math.abs(curr.value - sliderValue) <
                  Math.abs(prev.value - sliderValue)
                  ? curr
                  : prev;
              });

              const matchingPrice = config.stripe.plans.find(
                (plan) =>
                  plan.features[0].name
                    .toLowerCase()
                    .includes(closest.label.toLowerCase()) &&
                  plan.frequency.toLowerCase() ===
                    (billingCycle === "yearly" ? "annual" : "monthly")
              );

              handleBuyNow(matchingPrice?.priceId || "");
            }}
            className="w-full py-2.5 px-4 bg-[#204ec8] hover:bg-[#204ec8]/90 text-white font-medium rounded-lg transition mb-2"
          >
            Secure early bird discount
          </button>

          {/* Footer text */}
          <p className="text-xs text-center text-zinc-400">
            You won&apos;t be charged until your free trials ends in 14 days
          </p>
        </div>
      </div>
    </div>
  );
}
