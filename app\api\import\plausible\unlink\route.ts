import { NextRequest, NextResponse } from "next/server";
import { auth } from "@clerk/nextjs/server";
import connect from "@/util/db";
import Event from "@/models/event";

export async function POST(request: NextRequest) {
  try {
    // Verify user is authenticated
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Connect to database
    await connect();

    // Parse the request body
    const body = await request.json();
    const { websiteId } = body;

    // Validate websiteId
    if (!websiteId) {
      return NextResponse.json(
        { error: "Missing required field: websiteId" },
        { status: 400 }
      );
    }

    // First, count how many records will be deleted
    const countToDelete = await Event.countDocuments({
      websiteId,
      visitorId: { $regex: "^pl_" }, // Match events where visitorId starts with "pl_"
    });

    if (countToDelete === 0) {
      return NextResponse.json({
        success: true,
        message: "No Plausible data found to remove",
        count: 0,
      });
    }

    // Delete all events that have a visitor ID starting with "pl_"
    const deleteResult = await Event.deleteMany({
      websiteId,
      visitorId: { $regex: "^pl_" },
    });

    return NextResponse.json({
      success: true,
      message: `Successfully removed ${deleteResult.deletedCount} Plausible events`,
      count: deleteResult.deletedCount,
    });
  } catch (error) {
    console.error("Error unlinking Plausible data:", error);
    return NextResponse.json(
      {
        error: "Failed to unlink Plausible data",
        details: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
} 