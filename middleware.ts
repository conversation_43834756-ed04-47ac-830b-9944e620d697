import { clerkMiddleware, createRouteMatcher } from "@clerk/nextjs/server";

const isPublicRoute = createRouteMatcher([
  "/",
  "/sign-in(.*)",
  "/sign-up(.*)",
  "/api/events(.*)",
  "/api/websites(.*)",
  "/api/webhooks/(.*)",
  "/api/stripe/(.*)",
  "/tos(.*)",
  "/changelog/(.*)",
  "/docs(.*)",
  "/privacy-policy(.*)",
  "/api/clean(.*)",
  // "/api/stripe/create-checkout"
]);

export default clerkMiddleware(async (auth, request) => {
  // Always skip OPTIONS requests to allow CORS preflight
  if (request.method === "OPTIONS") {
    return;
  }

  if (!isPublicRoute(request)) {
    await auth.protect();
  }
});

export const config = {
  matcher: [
    // Skip Next.js internals and all static files
    "/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*)",
    // Run for API routes
    "/(api|trpc)(.*)",
  ],
};
