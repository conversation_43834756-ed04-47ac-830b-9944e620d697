import mongoose from "mongoose";

const analyticsSchema = new mongoose.Schema({
  path: { type: String, required: true },
  referrer: { type: String },
  referrerUrl: { type: String },
  userAgent: { type: String },
  timestamp: { type: Date, default: Date.now },
  sessionId: { type: String, required: true },
  ip: { type: String, default: "Unknown" },
  city: { type: String, default: "Unknown" },
  country: { type: String, default: "Unknown" },
  countryCode: { type: String, default: "Unknown" },
  os: { type: String, default: "Unknown" },
});

export interface Analytics extends mongoose.Document {
  path: string;
  referrer: string;
  referrerUrl: string;
  userAgent: string;
  timestamp: Date;
  sessionId: string;
  ip: string;
  city: string;
  country: string;
  countryCode: string;
  os: string;
}

export default mongoose.models.Analytics ||
  mongoose.model<Analytics>("Analytics", analyticsSchema);
