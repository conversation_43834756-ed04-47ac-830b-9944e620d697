import React from 'react';

function Skeleton({ className = '' }: { className?: string }) {
  return (
    <div className={`animate-pulse bg-[#363636] rounded ${className}`} />
  );
}

export function WebsiteCardSkeleton() {
  return (
    <div className="bg-[#2A2A2A] p-6 rounded-lg space-y-4 border border-gray-700/50">
      <div className="flex items-center gap-2 mb-5">
        <Skeleton className="w-5 h-5 rounded" />
        <Skeleton className="h-6 w-32" />
      </div>
      <Skeleton className="h-[40px] w-full" />
      <div className="flex items-center gap-2 mt-2">
        <Skeleton className="w-4 h-4 rounded-full" />
        <Skeleton className="h-4 w-24" />
      </div>
    </div>
  );
}

export function DashboardSkeleton() {
  return (
    <div className="min-h-screen bg-[#1C1C1C] text-gray-100 overflow-x-hidden">
      <div className="container mx-auto px-4 py-8 max-w-full sm:max-w-6xl">
        <div className="flex items-center justify-between mb-8">
          <Skeleton className="h-8 w-32" />
          <Skeleton className="h-10 w-28" />
        </div>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
          {[...Array(6)].map((_, i) => (
            <WebsiteCardSkeleton key={i} />
          ))}
        </div>
      </div>
    </div>
  );
}

export function WebsiteDashboardSkeleton() {
  return (
    <div className="min-h-screen bg-[#1C1C1C] text-gray-100 overflow-x-hidden">
      <div className="container mx-auto px-4 py-4 sm:py-8 max-w-full sm:max-w-6xl">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center gap-4 mb-6">
            <Skeleton className="h-8 w-32" />
          </div>
          <div className="flex items-center gap-3">
            <Skeleton className="h-8 w-8 rounded-lg" />
            <Skeleton className="h-8 w-40" />
            <Skeleton className="h-8 w-32" />
          </div>
        </div>

        {/* Stats Card */}
        <div className="bg-[#2A2A2A] p-4 sm:p-6 rounded-lg mb-4 sm:mb-8">
          <div className="grid grid-cols-2 sm:grid-cols-4 gap-4 sm:gap-6 mb-6">
            {[...Array(4)].map((_, i) => (
              <div key={i}>
                <Skeleton className="h-4 w-20 mb-2" />
                <Skeleton className="h-8 w-16" />
              </div>
            ))}
          </div>
          <Skeleton className="h-[250px] sm:h-[300px] w-full" />
        </div>

        {/* Top Referrers & Pages */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-8 mb-4 sm:mb-8">
          {[...Array(2)].map((_, i) => (
            <div key={i} className="bg-[#2A2A2A] p-4 sm:p-6 rounded-lg">
              <Skeleton className="h-6 w-32 mb-4" />
              <div className="space-y-2">
                {[...Array(5)].map((_, j) => (
                  <Skeleton key={j} className="h-12 w-full" />
                ))}
              </div>
            </div>
          ))}
        </div>

        {/* Locations */}
        <div className="bg-[#2A2A2A] p-4 sm:p-6 rounded-lg mb-4 sm:mb-8">
          <Skeleton className="h-6 w-32 mb-4" />
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
            {[...Array(6)].map((_, i) => (
              <Skeleton key={i} className="h-12 w-full" />
            ))}
          </div>
        </div>

        {/* Operating Systems */}
        <div className="bg-[#2A2A2A] p-4 sm:p-6 rounded-lg">
          <Skeleton className="h-6 w-32 mb-4" />
          <div className="space-y-2">
            {[...Array(4)].map((_, i) => (
              <Skeleton key={i} className="h-12 w-full" />
            ))}
          </div>
        </div>
      </div>
    </div>
  );
} 