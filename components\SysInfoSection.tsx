import { useFilterState } from "@/hooks/useFilterState";
import { useOSIcon } from "@/hooks/useOSIcon";
import { useBrowserIcon } from "@/hooks/useBrowserIcon";
import { useDeviceIcon } from "@/hooks/useDeviceIcon";
import React, { Dispatch, JSX, SetStateAction } from "react";

type SysInfoSectionProps = {
  operatingSystems: Array<{ name: string; visitors: number }>;
  browsers: Array<{ name: string; visitors: number }>;
  devices: Array<{ name: string; visitors: number }>;
  maxOSVisitors: number;
  maxBrowserVisitors: number;
  maxDeviceVisitors: number;
  setShowSystemModal: Dispatch<SetStateAction<boolean>>;
  systemView: "browser" | "os" | "device";
  setSystemView: Dispatch<SetStateAction<"browser" | "os" | "device">>;
};

function SysInfoSection({
  operatingSystems,
  browsers,
  devices,
  maxOSVisitors,
  maxBrowserVisitors,
  maxDeviceVisitors,
  setShowSystemModal,
  systemView,
  setSystemView,
}: SysInfoSectionProps) {
  const { handleFilterClick, activeFilters } = useFilterState();
  const { getOSIcon } = useOSIcon();
  const { getBrowserIcon } = useBrowserIcon();
  const { getDeviceIcon } = useDeviceIcon();

  return (
    <div className="bg-[#2A2A2A] p-4 sm:p-6 rounded-lg">
      <div className="flex items-center justify-between mb-3">
        <h2 className="text-base sm:text-lg font-semibold text-white">
          System Info
        </h2>
        <div className="flex rounded-lg p-1">
          <button
            onClick={() => setSystemView("browser")}
            className={`px-2 py-1 rounded-md text-xs sm:text-sm transition-colors ${systemView === "browser"
              ? "bg-blue-500 text-white"
              : "text-gray-400 hover:text-white"
              }`}
          >
            Browser
          </button>
          <button
            onClick={() => setSystemView("os")}
            className={`px-2 py-1 rounded-md text-xs sm:text-sm transition-colors ${systemView === "os"
              ? "bg-blue-500 text-white"
              : "text-gray-400 hover:text-white"
              }`}
          >
            OS
          </button>
          <button
            onClick={() => setSystemView("device")}
            className={`px-2 py-1 rounded-md text-xs sm:text-sm transition-colors ${systemView === "device"
              ? "bg-blue-500 text-white"
              : "text-gray-400 hover:text-white"
              }`}
          >
            Device
          </button>
        </div>
      </div>
      <div className="space-y-1 h-[250px] overflow-hidden relative">
        {systemView === "os" &&
          operatingSystems.map((os, i) => (
            <div
              key={i}
              className={`flex items-center justify-between p-1 sm:p-2 rounded transition-colors text-xs sm:text-sm relative overflow-hidden group cursor-pointer ${activeFilters.some(
                (filter) =>
                  filter.type === "system" && filter.value === os.name
              )
                ? "bg-blue-500/20"
                : ""
                }`}
              onClick={() =>
                handleFilterClick("system", os.name, `OS: ${os.name}`)
              }
            >
              <div
                className="absolute inset-0 bg-blue-500/40 transition-transform origin-left group-hover:bg-blue-500/20"
                style={{
                  transform: `scaleX(${os.visitors / maxOSVisitors})`,
                }}
              />
              <span className="text-white flex items-center gap-2 relative">
                {getOSIcon(os.name)}
                {os.name}
              </span>
              <span className="text-white relative">{os.visitors}</span>
            </div>
          ))}
        {systemView === "browser" &&
          browsers.map((browser, i) => (
            <div
              key={i}
              className={`flex items-center justify-between p-1 sm:p-2 rounded transition-colors text-xs sm:text-sm relative overflow-hidden group cursor-pointer ${activeFilters.some(
                (filter) =>
                  filter.type === "system" && filter.value === browser.name
              )
                ? "bg-blue-500/20"
                : ""
                }`}
              onClick={() =>
                handleFilterClick(
                  "system",
                  browser.name,
                  `Browser: ${browser.name}`
                )
              }
            >
              <div
                className="absolute inset-0 bg-blue-500/40 transition-transform origin-left group-hover:bg-blue-500/20"
                style={{
                  transform: `scaleX(${browser.visitors / maxBrowserVisitors})`,
                }}
              />
              <span className="text-white flex items-center gap-2 relative">
                {getBrowserIcon(browser.name)}
                {browser.name}
              </span>
              <span className="text-white relative">{browser.visitors}</span>
            </div>
          ))}
        {systemView === "device" &&
          devices.map((device, i) => (
            <div
              key={i}
              className={`flex items-center justify-between p-1 sm:p-2 rounded transition-colors text-xs sm:text-sm relative overflow-hidden group cursor-pointer ${activeFilters.some(
                (filter) =>
                  filter.type === "system" && filter.value === device.name
              )
                ? "bg-blue-500/20"
                : ""
                }`}
              onClick={() =>
                handleFilterClick(
                  "system",
                  device.name,
                  `Device: ${device.name}`
                )
              }
            >
              <div
                className="absolute inset-0 bg-blue-500/40 transition-transform origin-left group-hover:bg-blue-500/20"
                style={{
                  transform: `scaleX(${device.visitors / maxDeviceVisitors})`,
                }}
              />
              <span className="text-white flex items-center gap-2 relative">
                {getDeviceIcon(device.name)}
                {device.name}
              </span>
              <span className="text-white relative">{device.visitors}</span>
            </div>
          ))}
        {((systemView === "os" && operatingSystems.length > 5) ||
          (systemView === "browser" && browsers.length > 5) ||
          (systemView === "device" && devices.length > 5)) && (
            <button
              onClick={() => setShowSystemModal(true)}
              className="absolute bottom-0 left-0 right-0 text-center py-2 text-blue-500 hover:text-blue-400 bg-gradient-to-t from-[#2A2A2A] via-[#2A2A2A] to-transparent"
            >
              Show more
            </button>
          )}
      </div>
    </div>
  );
}
export default SysInfoSection;
