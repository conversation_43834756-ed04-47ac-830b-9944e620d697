"use client";
import React, {
  Dispatch,
  SetStateAction,
  useEffect,
  useRef,
  useState,
} from "react";
import PlausibleImportModal from "./PlausibleImportModal";
import { Website } from "@/lib/types";
import {
  FaUnlink,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>aC<PERSON><PERSON>,
  FaTimes,
} from "react-icons/fa";
import Image from "next/image";
function ImportSettingsTab({ website }: { website: Website }) {
  const [showImportModal, setShowImportModal] = useState(false);
  const importModalRef = useRef<HTMLDivElement | null>(null);
  const [isUnlinking, setIsUnlinking] = useState(false);
  const [unlinkError, setUnlinkError] = useState<string | null>(null);
  const [unlinkSuccess, setUnlinkSuccess] = useState<string | null>(null);
  const [showUnlinkConfirm, setShowUnlinkConfirm] = useState(false);

  const handleUnlink = async () => {
    try {
      setIsUnlinking(true);
      setUnlinkError(null);
      setUnlinkSuccess(null);

      const response = await fetch("/api/import/plausible/unlink", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          websiteId: website.id,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Failed to unlink Plausible data");
      }

      setUnlinkSuccess(data.message || "Successfully unlinked Plausible data");
      setShowUnlinkConfirm(false);
    } catch (err) {
      setUnlinkError(
        err instanceof Error
          ? err.message
          : "An unknown error occurred while unlinking data"
      );
    } finally {
      setIsUnlinking(false);
    }
  };

  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (
        importModalRef.current &&
        !importModalRef.current.contains(event.target as Node)
      ) {
        setShowImportModal(false);
      }
    }

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);
  return (
    <div className="p-6 space-y-6">
      {showImportModal && (
        <PlausibleImportModal
          websiteId={website.id}
          domain={website.domain}
          onClose={() => setShowImportModal(false)}
          modalRef={importModalRef}
        />
      )}
      <div className="space-y-4">
        <h2 className="text-xl font-semibold flex items-center gap-2">
          <svg
            className="w-5 h-5 text-blue-500"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12"
            />
          </svg>
          Import Analytics Data
        </h2>
        <p className="text-sm text-gray-400">
          Import analytics data from other platforms to visualize your data
          alongside new analytics.
        </p>

        <div className="bg-[#363636] p-5 rounded-lg border border-gray-700/50 hover:border-blue-500/30 transition-colors">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 flex items-center justify-center bg-blue-500/10 rounded-lg">
              <Image
                src="/assets/plausible.png"
                alt="Plausible Analytics Icon"
                width={40}
                height={40}
              />
            </div>
            <div className="flex-1">
              <h3 className="font-medium text-lg">Plausible Analytics</h3>
              <p className="text-sm text-gray-400">
                Import your data from Plausible Analytics
              </p>
            </div>
            <button
              onClick={() => setShowImportModal(true)}
              className="bg-blue-500 text-white px-3 py-1.5 rounded hover:bg-blue-600 transition-colors"
            >
              Import
            </button>
          </div>
        </div>

        <div className="mt-4 text-sm text-gray-400">
          <p>
            More import options coming soon. Contact us if you need to import
            from a specific platform.
          </p>
        </div>

        <div className="border-t border-gray-700 pt-6 mt-6">
          <h3 className="text-gray-300 font-medium mb-3 flex items-center gap-2">
            <FaUnlink className="text-red-400" /> Remove Imported Data
          </h3>
          <p className="text-gray-400 text-sm mb-4">
            You can remove all previously imported data from Plausible. This
            action cannot be undone.
          </p>
          {unlinkError && (
            <div className="bg-red-900/30 border border-red-900 text-red-300 p-3 rounded mb-4 flex items-center gap-2">
              <FaTimes className="text-red-400 flex-shrink-0" />
              <p>{unlinkError}</p>
            </div>
          )}

          {unlinkSuccess && (
            <div className="bg-green-900/30 border border-green-900 text-green-300 p-3 rounded mb-4 flex items-center gap-2">
              <FaCheck className="text-green-400 flex-shrink-0" />
              <p>{unlinkSuccess}</p>
            </div>
          )}
          {showUnlinkConfirm ? (
            <div className="bg-red-900/20 border border-red-800 rounded-lg p-4 mb-4">
              <div className="flex items-start gap-3">
                <FaExclamationTriangle className="text-red-500 text-xl flex-shrink-0 mt-0.5" />
                <div>
                  <h4 className="text-red-300 font-medium mb-2">
                    Are you sure?
                  </h4>
                  <p className="text-gray-300 text-sm mb-3">
                    This will permanently delete all imported Plausible
                    analytics data for this website.
                  </p>
                  <div className="flex gap-3">
                    <button
                      onClick={() => setShowUnlinkConfirm(false)}
                      className="px-3 py-1.5 bg-gray-700 text-gray-200 rounded text-sm hover:bg-gray-600"
                    >
                      Cancel
                    </button>
                    <button
                      onClick={handleUnlink}
                      disabled={isUnlinking}
                      className="px-3 py-1.5 bg-red-700 text-white rounded text-sm hover:bg-red-600 flex items-center gap-1.5"
                    >
                      {isUnlinking ? (
                        <>
                          <FaSpinner className="animate-spin" /> Removing...
                        </>
                      ) : (
                        <>
                          <FaUnlink /> Confirm Removal
                        </>
                      )}
                    </button>
                  </div>
                </div>
              </div>
            </div>
          ) : (
            <button
              onClick={() => setShowUnlinkConfirm(true)}
              className="px-4 py-2 border border-red-700 text-red-400 rounded-md inline-flex items-center gap-2 hover:bg-red-900/20 transition-colors"
            >
              <FaUnlink /> Remove Plausible Data
            </button>
          )}
        </div>
      </div>
    </div>
  );
}
export default ImportSettingsTab;
