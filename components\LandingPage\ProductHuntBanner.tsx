"use client";

import { useEffect, useState } from "react";
import { X } from "lucide-react";

export default function ProductHuntBanner() {
  const [isVisible, setIsVisible] = useState(true);
  const [shouldDisplay, setShouldDisplay] = useState(false);

  useEffect(() => {
    const today = new Date();
    const isLaunchDay = today.getDate() === 12 && today.getMonth() === 4;
    setShouldDisplay(isLaunchDay);
  }, []);

  if (!shouldDisplay || !isVisible) return null;

  return (
    <div className="relative bg-[#EA532A] text-white py-3">
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-center gap-4">
          <img
            src="/producthunt-cat.svg"
            alt="Product Hunt Cat"
            className="w-8 h-8"
          />
          <p className="text-sm sm:text-base font-medium">
            🎉 We&apos;re live on Product Hunt today!{" "}
            <a
              href="https://www.producthunt.com/posts/versatailor"
              target="_blank"
              rel="noopener noreferrer"
              className="underline font-bold hover:no-underline"
            >
              Support us with your upvote →
            </a>
          </p>
          <button
            onClick={() => setIsVisible(false)}
            className="absolute right-4 top-1/2 -translate-y-1/2 p-1 hover:bg-white/10 rounded-full transition-colors"
            aria-label="Close banner"
          >
            <X size={20} />
          </button>
        </div>
      </div>
    </div>
  );
}
