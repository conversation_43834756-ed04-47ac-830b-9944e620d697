import { useMemo, useCallback, useEffect } from 'react';
import { SessionCache, CacheEntry, Event, ChartDataPoint } from './types';
import { areCacheKeysEquivalent } from './utils';

const API_CACHE_TIMEOUT = 24 * 60 * 60 * 1000; // 24 hours
const LOCAL_STORAGE_CACHE_LIMIT = 50;

export function useAnalyticsCache() {
  const sessionCache = useMemo<SessionCache>(
    () => ({
      events: new Map(),
      filteredResults: new Map(),
      chartData: new Map(),
      cacheVersion: "v1.4",
    }),
    []
  );

  const findBestCacheMatch = useCallback(
    (targetKey: string, periodType: string): CacheEntry<Event[]> | undefined => {
      const targetParts = targetKey.split("_");
      if (targetParts.length < 2 || targetParts[0] !== "website") return undefined;
      const targetWebsiteId = targetParts[1];

      for (const [key, value] of sessionCache.events.entries()) {
        const keyParts = key.split("_");
        if (
          keyParts.length < 2 ||
          keyParts[0] !== "website" ||
          keyParts[1] !== targetWebsiteId
        ) {
          continue;
        }

        if (key.includes(periodType) && areCacheKeysEquivalent(key, targetKey)) {
          return value;
        }
      }
      return undefined;
    },
    [sessionCache]
  );

  const cleanupOldCacheEntries = useCallback(() => {
    try {
      const storedCacheKeys = localStorage.getItem("analyticsCacheKeys");
      if (storedCacheKeys) {
        const cacheKeys = JSON.parse(storedCacheKeys) as string[];
        let hasRemovedOldEntries = false;

        const validKeys = cacheKeys.filter((key) => {
          const isValidFormat = key.startsWith("website_");

          if (!isValidFormat) {
            localStorage.removeItem(`analyticsCache_${key}`);
            hasRemovedOldEntries = true;
          }

          return isValidFormat;
        });

        if (hasRemovedOldEntries) {
          localStorage.setItem("analyticsCacheKeys", JSON.stringify(validKeys));
        }
      }

      for (const key of sessionCache.events.keys()) {
        if (!key.startsWith("website_")) {
          sessionCache.events.delete(key);
        }
      }
    } catch (error) {
      console.error("Error cleaning up old cache entries:", error);
    }
  }, [sessionCache]);

  const shouldInvalidateCache = useCallback(
    (selectedPeriod: string) => {
      const storedVersion = localStorage.getItem("chartDataCacheVersion");
      const currentVersion = sessionCache.cacheVersion;

      if (storedVersion !== currentVersion) {
        sessionCache.events.clear();
        sessionCache.filteredResults.clear();
        sessionCache.chartData.clear();
        localStorage.setItem("chartDataCacheVersion", currentVersion);
        cleanupOldCacheEntries();
        return true;
      }

      if (selectedPeriod === "all" || selectedPeriod === "ytd") {
        return true;
      }

      return false;
    },
    [sessionCache, cleanupOldCacheEntries]
  );

  const setCacheEntry = useCallback(
    (key: string, data: Event[] | ChartDataPoint[], type: 'events' | 'chartData') => {
      const now = Date.now();
      const entry: CacheEntry<any> = { data, timestamp: now };

      if (type === 'events') {
        sessionCache.events.set(key, entry);
      } else {
        sessionCache.chartData.set(key, entry);
      }

      try {
        const storedCacheKeys = localStorage.getItem("analyticsCacheKeys");
        let cacheKeys = storedCacheKeys ? JSON.parse(storedCacheKeys) : [];

        if (!cacheKeys.includes(key)) {
          if (cacheKeys.length >= LOCAL_STORAGE_CACHE_LIMIT) {
            const oldestKey = cacheKeys.shift();
            localStorage.removeItem(`analyticsCache_${oldestKey}`);
          }

          cacheKeys.push(key);
          localStorage.setItem("analyticsCacheKeys", JSON.stringify(cacheKeys));
        }

        localStorage.setItem(
          `analyticsCache_${key}`,
          JSON.stringify(entry)
        );
      } catch (error) {
        console.error("Error storing cache in localStorage:", error);
      }
    },
    [sessionCache]
  );

  // Initialize cache from localStorage
  useEffect(() => {
    try {
      const storedCacheKeys = localStorage.getItem("analyticsCacheKeys");
      if (storedCacheKeys) {
        const cacheKeys = JSON.parse(storedCacheKeys) as string[];
        cacheKeys.forEach((key) => {
          const storedCache = localStorage.getItem(`analyticsCache_${key}`);
          if (storedCache) {
            const { data, timestamp } = JSON.parse(storedCache);
            sessionCache.events.set(key, { data, timestamp });
          }
        });
      }
    } catch (error) {
      console.error("Error loading cache from localStorage:", error);
      localStorage.removeItem("analyticsCacheKeys");
    }
  }, [sessionCache]);

  return {
    sessionCache,
    findBestCacheMatch,
    shouldInvalidateCache,
    setCacheEntry,
    cleanupOldCacheEntries,
    API_CACHE_TIMEOUT,
  };
} 