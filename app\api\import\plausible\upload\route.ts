import { NextRequest, NextResponse } from "next/server";
import { auth } from "@clerk/nextjs/server";
import connect from "@/util/db";
import Event from "@/models/event";
import { parse } from "papaparse";

// Remove JSZip type import - we'll use dynamic import
// import type J<PERSON><PERSON><PERSON> from 'jszip';

interface ZipFile {
  name: string;
  dir: boolean;
  async: (format: string) => Promise<string>;
}

interface ZipContents {
  files: Record<string, ZipFile>;
}

interface VisitorInfo {
  visitorId: string;
  sessionId: string;
  count: number;
  date: Date;
}

interface PageInfo {
  visitors: number;
  visits: number;
  bounceRate: number;
  duration: number;
}

interface BrowserInfo {
  browser: string;
  visitors: number;
}

interface OSInfo {
  os: string;
  visitors: number;
}

interface DeviceInfo {
  device: string;
  visitors: number;
}

interface LocationInfo {
  country: string;
  region: string | null;
  city: string | null;
  visitors: number;
}

interface SourceInfo {
  source: string;
  visitors: number;
}

interface PageEntry {
  page: string;
  visitors: number;
}

interface CustomEventInfo {
  eventName: string;
  visitors: number;
  events: number;
}

// Helper to extract files from FormData with proper typing
async function extractFormData(request: NextRequest) {
  const formData = await request.formData();
  const file = formData.get("file") as File | null;
  const websiteId = formData.get("websiteId") as string;
  const domain = formData.get("domain") as string;

  return { file, websiteId, domain };
}

export async function POST(request: NextRequest) {
  try {
    // Verify user is authenticated
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Connect to database
    await connect();

    // Extract form data
    const { file, websiteId, domain } = await extractFormData(request);

    // Validate required data
    if (!file || !websiteId || !domain) {
      return NextResponse.json(
        { error: "Missing required fields: file, websiteId, domain" },
        { status: 400 }
      );
    }

    // Verify file is a ZIP
    if (!file.name.endsWith(".zip")) {
      return NextResponse.json(
        { error: "Please upload a ZIP file" },
        { status: 400 }
      );
    }

    // Process the zip file
    try {
      // Convert file to ArrayBuffer for processing
      const buffer = await file.arrayBuffer();

      // Use JSZip to extract files from the zip - fix the import approach
      const JSZip = (await import("jszip")).default;
      const zip = new JSZip();
      
      // Load the zip file
      const zipContents = await zip.loadAsync(buffer) as unknown as ZipContents;
      
      // Track processed data and stats
      const importStats = {
        total: 0,
        successful: 0,
        failed: 0,
        types: {
          browsers: 0,
          devices: 0,
          locations: 0,
          pages: 0,
          sources: 0,
          visitors: 0,
          operatingSystems: 0,
          entryPages: 0,
          exitPages: 0,
          customEvents: 0,
        },
      };

      // Process each file in the zip
      const events: any[] = [];
      const visitorMap = new Map<string, VisitorInfo>(); // Maps visitorId to info
      const sessionMap = new Map<string, string>(); // Maps sessionId to info
      const pageMap = new Map<string, PageInfo>(); // Maps page paths to info

      // Process visitors file first to get visitor IDs
      const visitorsFile = findFileByPattern(zipContents, "imported_visitors");
      
      if (visitorsFile) {
        const visitorsContent = await visitorsFile.async("text");
        const visitorsData = parse(visitorsContent, { header: true }).data as Record<string, string>[];
        
        for (const row of visitorsData) {
          if (isValidRow(row)) {
            // Generate a consistent visitor ID for each visitor
            const visitorId = `pl_${crypto.randomUUID()}`;
            const sessionId = `pl_s_${crypto.randomUUID()}`;
            
            const date = getDateFromFilename(visitorsFile.name) || new Date();
            
            visitorMap.set(row.visitors, {
              visitorId,
              sessionId,
              count: parseInt(row.visitors, 10) || 0,
              date,
            });
            
            importStats.types.visitors++;
          }
        }
      }

      // Process pages data
      const pagesFile = findFileByPattern(zipContents, "imported_pages");
      
      if (pagesFile) {
        const pagesContent = await pagesFile.async("text");
        const pagesData = parse(pagesContent, { header: true }).data as Record<string, string>[];
        
        for (const row of pagesData) {
          if (isValidRow(row) && row.page) {
            pageMap.set(row.page, {
              visitors: parseInt(row.visitors, 10) || 0,
              visits: parseInt(row.visits, 10) || 0,
              bounceRate: parseFloat(row.bounce_rate) || 0,
              duration: parseFloat(row.visit_duration) || 0,
            });
            
            importStats.types.pages++;
          }
        }
      }

      // Process browser data
      const browsersFile = findFileByPattern(zipContents, "imported_browsers");
      let browsers: BrowserInfo[] = [];
      
      if (browsersFile) {
        const browsersContent = await browsersFile.async("text");
        const browsersData = parse(browsersContent, { header: true }).data as Record<string, string>[];
        
        browsers = browsersData
          .filter(row => isValidRow(row) && row.browser)
          .map(row => ({
            browser: row.browser,
            visitors: parseInt(row.visitors, 10) || 0,
          }));
          
        importStats.types.browsers = browsers.length;
      }

      // Process operating systems data
      const osFile = findFileByPattern(zipContents, "imported_operating_systems");
      let operatingSystems: OSInfo[] = [];
      
      if (osFile) {
        const osContent = await osFile.async("text");
        const osData = parse(osContent, { header: true }).data as Record<string, string>[];
        
        operatingSystems = osData
          .filter(row => isValidRow(row) && row.operating_system)
          .map(row => ({
            os: row.operating_system,
            visitors: parseInt(row.visitors, 10) || 0,
          }));
          
        importStats.types.operatingSystems = operatingSystems.length;
      }

      // Process device data
      const devicesFile = findFileByPattern(zipContents, "imported_devices");
      let devices: DeviceInfo[] = [];
      
      if (devicesFile) {
        const devicesContent = await devicesFile.async("text");
        const devicesData = parse(devicesContent, { header: true }).data as Record<string, string>[];
        
        devices = devicesData
          .filter(row => isValidRow(row) && row.device)
          .map(row => ({
            device: row.device,
            visitors: parseInt(row.visitors, 10) || 0,
          }));
          
        importStats.types.devices = devices.length;
      }

      // Process location data
      const locationsFile = findFileByPattern(zipContents, "imported_locations");
      let locations: LocationInfo[] = [];
      
      if (locationsFile) {
        const locationsContent = await locationsFile.async("text");
        const locationsData = parse(locationsContent, { header: true }).data as Record<string, string>[];
        
        locations = locationsData
          .filter(row => isValidRow(row))
          .map(row => {
            // Extract country and region from location data
            let country = row.country;
            let region = null;
            let city = null;
            
            // Check if the country field contains region information (like "US-MI")
            if (country && country.includes('-')) {
              const parts = country.split('-');
              country = parts[0]; // The first part is the country code
              region = parts[1];  // The second part is the region code
              city = row.city || region; // Use city if available, otherwise use region
            } else {
              city = row.city || null;
            }
            
            return {
              country,
              region,
              city,
              visitors: parseInt(row.visitors, 10) || 0,
            };
          });
          
        importStats.types.locations = locations.length;
      }

      // Process referrer/source data
      const sourcesFile = findFileByPattern(zipContents, "imported_sources");
      let sources: SourceInfo[] = [];
      
      if (sourcesFile) {
        const sourcesContent = await sourcesFile.async("text");
        const sourcesData = parse(sourcesContent, { header: true }).data as Record<string, string>[];
        
        sources = sourcesData
          .filter(row => isValidRow(row) && row.source)
          .map(row => ({
            source: row.source,
            visitors: parseInt(row.visitors, 10) || 0,
          }));
          
        importStats.types.sources = sources.length;
      }

      // Process entry pages
      const entryPagesFile = findFileByPattern(zipContents, "imported_entry_pages");
      let entryPages: PageEntry[] = [];
      
      if (entryPagesFile) {
        const entryPagesContent = await entryPagesFile.async("text");
        const entryPagesData = parse(entryPagesContent, { header: true }).data as Record<string, string>[];
        
        entryPages = entryPagesData
          .filter(row => isValidRow(row) && row.entry_page)
          .map(row => ({
            page: row.entry_page,
            visitors: parseInt(row.visitors, 10) || 0,
          }));
          
        importStats.types.entryPages = entryPages.length;
      }

      // Process custom events if available
      const customEventsFile = findFileByPattern(zipContents, "imported_custom_events");
      let customEvents: CustomEventInfo[] = [];
      
      if (customEventsFile) {
        const customEventsContent = await customEventsFile.async("text");
        const customEventsData = parse(customEventsContent, { header: true }).data as Record<string, string>[];
        
        customEvents = customEventsData
          .filter(row => isValidRow(row) && row.event_name)
          .map(row => ({
            eventName: row.event_name,
            visitors: parseInt(row.visitors, 10) || 0,
            events: parseInt(row.events, 10) || 0,
          }));
          
        importStats.types.customEvents = customEvents.length;
      }

      // Create events from the processed data
      // First determine how many events to create based on visitor count
      const totalVisitors = Array.from(visitorMap.values()).reduce(
        (sum, info) => sum + info.count, 
        0
      );
      
      if (totalVisitors === 0) {
        throw new Error("No visitor data found in the uploaded files");
      }

      // Create synthetic events based on the aggregate data
      for (const [key, visitorInfo] of visitorMap.entries()) {
        const { visitorId, sessionId, date } = visitorInfo;
        
        // Create events for this visitor based on the distribution of pages
        const pageEntries = Array.from(pageMap.entries());
        
        if (pageEntries.length > 0) {
          // Choose a random page for this visitor
          const pageIdx = Math.floor(Math.random() * pageEntries.length);
          const [pagePath, pageInfo] = pageEntries[pageIdx];
          
          // Create a timestamp with realistic distribution instead of completely random times
          const timestamp = new Date(date);
          
          // Use a more realistic hourly distribution that favors business hours
          // This creates a bell curve centered around noon/early afternoon
          const hourDistribution = [
            0.5, 0.3, 0.2, 0.1, 0.2, 0.5,       // 12am-5am (low traffic)
            1.0, 2.0, 4.0, 5.0, 6.0, 7.0,       // 6am-11am (rising traffic)
            8.0, 7.5, 7.0, 6.5, 6.0, 5.0,       // 12pm-5pm (peak traffic)
            4.0, 3.0, 2.0, 1.5, 1.0, 0.7        // 6pm-11pm (declining traffic)
          ];
          
          // Get a random hour based on the weighted distribution
          const totalWeight = hourDistribution.reduce((sum, weight) => sum + weight, 0);
          let random = Math.random() * totalWeight;
          let hour = 0;
          
          for (let i = 0; i < hourDistribution.length; i++) {
            random -= hourDistribution[i];
            if (random <= 0) {
              hour = i;
              break;
            }
          }
          
          timestamp.setHours(
            hour,
            Math.floor(Math.random() * 60),
            Math.floor(Math.random() * 60)
          );
          
          // Randomly select browser, OS, device, location, and referrer
          const browser = getRandomItem(browsers)?.browser || 'Chrome';
          const os = getRandomItem(operatingSystems)?.os || 'Windows';
          const device = getRandomItem(devices)?.device || 'Desktop';
          
          // Improved location handling
          let country = null;
          let region = null;
          let city = null;
          if (locations.length > 0) {
            // Weight countries by visitor count
            const locationTotalVisitors = locations.reduce((sum, loc) => sum + loc.visitors, 0);
            let randomValue = Math.random() * locationTotalVisitors;
            let selectedLocation = null;
            
            for (const location of locations) {
              randomValue -= location.visitors;
              if (randomValue <= 0) {
                selectedLocation = location;
                break;
              }
            }
            
            // If weighted selection fails, fall back to random selection
            if (!selectedLocation) {
              selectedLocation = getRandomItem(locations);
            }
            
            if (selectedLocation) {
              country = selectedLocation.country;
              region = selectedLocation.region;
              city = selectedLocation.city || selectedLocation.region;
              
              // Convert country name to ISO code if needed
              if (country) {
                country = getCountryCode(country);
              }
            }
          }
          
          const referrer = getRandomItem(sources)?.source || null;
          
          // Create the event
          const event = {
            id: crypto.randomUUID(),
            type: 'pageview',
            websiteId,
            domain,
            href: `https://${domain}${pagePath}`,
            referrer,
            timestamp: timestamp.toISOString(),
            viewport: {
              width: [320, 375, 414, 768, 1024, 1280, 1366, 1440, 1920][Math.floor(Math.random() * 9)],
              height: [568, 667, 736, 1024, 768, 800, 900, 1080][Math.floor(Math.random() * 8)]
            },
            visitorId,
            sessionId,
            location: {
              country,
              city,
              region
            },
            clientInfo: {
              browser,
              os,
              device
            },
            isBot: false
          };
          
          events.push(event);
          importStats.total++;
          importStats.successful++;
        }
      }

      // Add custom events if available
      for (const customEvent of customEvents) {
        // Create events proportional to the number reported
        const eventCount = Math.min(customEvent.events, 100); // Cap to prevent too many events
        
        for (let i = 0; i < eventCount; i++) {
          // Randomly select a visitor
          const visitorInfo = getRandomItem(Array.from(visitorMap.values()));
          
          if (visitorInfo) {
            const { visitorId, sessionId, date } = visitorInfo;
            
            // Create a timestamp with the same realistic distribution
            const timestamp = new Date(date);
            
            // Use the same hourly distribution as for pageviews
            const hourDistribution = [
              0.5, 0.3, 0.2, 0.1, 0.2, 0.5,       // 12am-5am (low traffic)
              1.0, 2.0, 4.0, 5.0, 6.0, 7.0,       // 6am-11am (rising traffic)
              8.0, 7.5, 7.0, 6.5, 6.0, 5.0,       // 12pm-5pm (peak traffic)
              4.0, 3.0, 2.0, 1.5, 1.0, 0.7        // 6pm-11pm (declining traffic)
            ];
            
            // Get a random hour based on the weighted distribution
            const totalWeight = hourDistribution.reduce((sum, weight) => sum + weight, 0);
            let random = Math.random() * totalWeight;
            let hour = 0;
            
            for (let i = 0; i < hourDistribution.length; i++) {
              random -= hourDistribution[i];
              if (random <= 0) {
                hour = i;
                break;
              }
            }
            
            timestamp.setHours(
              hour,
              Math.floor(Math.random() * 60),
              Math.floor(Math.random() * 60)
            );
            
            // Also add location data to custom events
            let country = null;
            let region = null;
            
            if (locations.length > 0) {
              const selectedLocation = getRandomItem(locations);
              if (selectedLocation) {
                country = selectedLocation.country;
                region = selectedLocation.region;
                
                // Convert country name to ISO code if needed
                if (country) {
                  country = getCountryCode(country);
                }
              }
            }
            
            // Create the custom event
            const event = {
              id: crypto.randomUUID(),
              type: 'custom',
              websiteId,
              domain,
              href: `https://${domain}${getRandomItem(Array.from(pageMap.keys())) || '/'}`,
              timestamp: timestamp.toISOString(),
              visitorId,
              sessionId,
              location: {
                country,
                city: null,
                region
              },
              extraData: {
                eventName: customEvent.eventName
              },
              isBot: false
            };
            
            events.push(event);
            importStats.total++;
            importStats.successful++;
          }
        }
      }

      // Batch insert events
      const BATCH_SIZE = 100;
      for (let i = 0; i < events.length; i += BATCH_SIZE) {
        const batch = events.slice(i, i + BATCH_SIZE);
        await Event.insertMany(batch);
      }

      return NextResponse.json({
        success: true,
        message: `Successfully imported ${importStats.successful} events from Plausible data`,
        stats: importStats
      });
    } catch (error) {
      console.error("Error processing ZIP file:", error);
      return NextResponse.json({ 
        error: "Failed to process the ZIP file",
        details: error instanceof Error ? error.message : 'Unknown error'
      }, { status: 500 });
    }
  } catch (error) {
    console.error("Error in Plausible import upload:", error);
    return NextResponse.json({ 
      error: "Failed to import data from Plausible",
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

// Helper functions

// Find a file in the zip by matching name pattern
function findFileByPattern(zipContents: ZipContents, pattern: string): ZipFile | null {
  for (const [path, file] of Object.entries(zipContents.files)) {
    if (path.includes(pattern) && !file.dir) {
      return file;
    }
  }
  return null;
}

// Validate a CSV row has content
function isValidRow(row: Record<string, any>): boolean {
  return row && Object.keys(row).length > 0 && !row.__parsed_extra;
}

// Extract date from filename like imported_xxx_20220101_20220131.csv
function getDateFromFilename(filename: string): Date | null {
  const match = filename.match(/_(\d{8})_/);
  if (match && match[1]) {
    const dateStr = match[1];
    const year = parseInt(dateStr.substring(0, 4), 10);
    const month = parseInt(dateStr.substring(4, 6), 10) - 1; // 0-indexed months
    const day = parseInt(dateStr.substring(6, 8), 10);
    return new Date(year, month, day);
  }
  return null;
}

// Get a random item from an array
function getRandomItem<T>(array: T[]): T | null {
  if (!array || array.length === 0) return null;
  return array[Math.floor(Math.random() * array.length)];
}

// Add a function to convert country names to ISO codes
function getCountryCode(countryName: string): string | null {
  // Map of common country names to their ISO codes
  const countryMap: Record<string, string> = {
    "united states": "US",
    "us": "US",
    "usa": "US",
    "united kingdom": "GB",
    "uk": "GB",
    "great britain": "GB",
    "canada": "CA",
    "australia": "AU",
    "germany": "DE",
    "france": "FR",
    "italy": "IT",
    "spain": "ES",
    "netherlands": "NL",
    "sweden": "SE",
    "norway": "NO",
    "denmark": "DK",
    "finland": "FI",
    "russia": "RU",
    "japan": "JP",
    "china": "CN",
    "india": "IN",
    "brazil": "BR",
    "mexico": "MX",
    "south africa": "ZA",
    "nigeria": "NG",
    "egypt": "EG",
    "saudi arabia": "SA",
    "united arab emirates": "AE",
    "poland": "PL",
    "ukraine": "UA",
    "turkey": "TR",
    "ireland": "IE",
    "belgium": "BE",
    "switzerland": "CH",
    "austria": "AT",
    "portugal": "PT",
    "greece": "GR",
    "israel": "IL",
    "singapore": "SG",
    "hong kong": "HK",
    "taiwan": "TW",
    "south korea": "KR",
    "thailand": "TH",
    "vietnam": "VN",
    "indonesia": "ID",
    "malaysia": "MY",
    "philippines": "PH",
    "new zealand": "NZ",
    "argentina": "AR",
    "chile": "CL",
    "colombia": "CO",
    "peru": "PE",
    "venezuela": "VE"
  };

  if (!countryName) return null;
  
  // Try direct match first
  const normalizedName = countryName.toLowerCase().trim();
  if (countryMap[normalizedName]) {
    return countryMap[normalizedName];
  }
  
  // Check if it's already a 2-letter code
  if (normalizedName.length === 2 && /^[A-Za-z]{2}$/.test(normalizedName)) {
    return normalizedName.toUpperCase();
  }
  
  // Try partial match
  for (const [key, value] of Object.entries(countryMap)) {
    if (normalizedName.includes(key) || key.includes(normalizedName)) {
      return value;
    }
  }
  
  // If still no match but it looks like a valid country name (at least 4 chars)
  // take the first two letters as a fallback
  if (normalizedName.length >= 4) {
    return normalizedName.substring(0, 2).toUpperCase();
  }
  
  return null;
} 