import React, { useState, useEffect } from "react";
import AnimatedCounter from "./AnimatedCounter";
import type { RevenueFilters, RevenueMetrics } from "@/hooks/useRevenueAnalytics";
import { Period, CustomPeriod } from "@/lib/types";

const POLLING_INTERVAL = process.env.NODE_ENV === "development" ? 120 : 20;

type HeadersListProps = {
  uniqueVisitors: number;
  isAnalyticsLoading: boolean;
  visitorsDeltaPercentage: number | null;
  bounceRate: number;
  bounceRateDelta: number | null;
  avgVisitTime: number;
  avgVisitTimeDeltaPercentage: number | null;
  currentVisitors: number;
  websiteId: string;
  selectedPeriod: Period;
  customPeriod?: CustomPeriod;
  revenueFilters?: RevenueFilters;
  revenueData?: RevenueMetrics | null;
  isRevenueLoading?: boolean;
  isRevenueEnabled?: boolean;
  formatCurrency?: (amount: number, currency?: string) => string;
  formatPercentage?: (value: number, showSign?: boolean) => string;
};

function HeaderList({
  uniqueVisitors,
  isAnalyticsLoading,
  visitorsDeltaPercentage,
  bounceRate,
  bounceRateDelta,
  avgVisitTime,
  avgVisitTimeDeltaPercentage,
  currentVisitors,
  websiteId,
  selectedPeriod,
  customPeriod,
  revenueFilters,
  revenueData,
  isRevenueLoading = false,
  isRevenueEnabled = false,
  formatCurrency,
  formatPercentage
}: HeadersListProps) {
  const [nextRefresh, setNextRefresh] = useState(POLLING_INTERVAL);

  // Default formatting functions if none are provided
  const formatCurrencyWithFallback = (amount: number, currency = 'USD') => {
    if (formatCurrency) return formatCurrency(amount, currency);
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(amount);
  };

  const formatPercentageWithFallback = (value: number, showSign = true) => {
    if (formatPercentage) return formatPercentage(value, showSign);
    const formatted = Math.abs(value).toFixed(1);
    const sign = showSign && value !== 0 ? (value > 0 ? '+' : '-') : '';
    return `${sign}${formatted}%`;
  };

  // Reset polling timer when website changes
  useEffect(() => {
    setNextRefresh(POLLING_INTERVAL);
  }, [websiteId]);

  useEffect(() => {
    const timer = setInterval(() => {
      setNextRefresh((prev) => (prev > 0 ? prev - 1 : POLLING_INTERVAL));
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  function formatSessionTime(seconds: number): string {
    if (seconds < 60) {
      return `${seconds}s`;
    }

    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;

    if (remainingSeconds === 0) {
      return `${minutes}m`;
    }

    return `${minutes}m ${remainingSeconds}s`;
  }

  const hasNoData =
    uniqueVisitors === 0 &&
    bounceRate === 0 &&
    avgVisitTime === 0 &&
    currentVisitors === 0;

  const hasNoRevenueData = !revenueData || (
    revenueData.totalRevenue.current === 0 &&
    revenueData.orderCount.current === 0
  );

  // Improved grid layout for better responsiveness
  const gridCols = "grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-7";

  // Common delta arrow component
  const DeltaArrow = ({ isPositive }: { isPositive: boolean }) => (
    <svg
      className="w-3 h-3 flex-shrink-0"
      fill="none"
      stroke="currentColor"
      viewBox="0 0 24 24"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d={isPositive ? "M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" : "M13 17h8m0 0V9m0 8l-8-8-4 4-6-6"}
      />
    </svg>
  );

  // Tooltip component for disabled revenue metrics
  const RevenueTooltip = ({ children, label }: { children: React.ReactNode; label: string }) => (
    <div className="group relative">
      {children}
      {!isRevenueEnabled && (
        <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-gray-800 text-white text-xs rounded-lg shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-10">
          Enable revenue attribution in Settings → Revenue
          <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-800"></div>
        </div>
      )}
    </div>
  );

  return (
    <div className="mb-6" key={`header-list-${websiteId}`}>
      <div className={`grid ${gridCols} gap-2 sm:gap-3 md:gap-4 lg:gap-6 rounded-xl p-2 sm:p-3 md:p-4 lg:p-6`}>
        {/* Unique Visitors */}
        <div className="flex flex-col">
          <div className="text-xs text-gray-400 mb-1 flex items-center gap-1">
            Visitors
          </div>
          <div className="text-sm sm:text-base lg:text-lg xl:text-xl font-semibold text-white">
            {hasNoData && !isAnalyticsLoading ? (
              <span className="text-gray-500">0</span>
            ) : (
              <span className="break-all">{uniqueVisitors.toLocaleString()}</span>
            )}
          </div>
          {!isAnalyticsLoading &&
            !hasNoData &&
            visitorsDeltaPercentage !== null && (
              <div
                className={`text-xs font-normal flex items-center gap-1 mt-1 ${
                  visitorsDeltaPercentage > 0
                    ? "text-green-400"
                    : visitorsDeltaPercentage < 0
                    ? "text-red-400"
                    : "text-gray-400"
                }`}
              >
                {visitorsDeltaPercentage !== 0 && (
                  <DeltaArrow isPositive={visitorsDeltaPercentage > 0} />
                )}
                {visitorsDeltaPercentage > 0 ? `+${visitorsDeltaPercentage}` : visitorsDeltaPercentage}%
              </div>
            )}
        </div>

        {/* Total Revenue - Always show but grey out if disabled */}
        <RevenueTooltip label="Revenue">
          <div className={`flex flex-col ${!isRevenueEnabled ? 'opacity-50 cursor-help' : ''}`}>
            <div className="text-xs text-gray-400 mb-1">
              Revenue
            </div>
            <div className="text-sm sm:text-base lg:text-lg xl:text-xl font-semibold text-white">
              {!isRevenueEnabled ? (
                <span className="text-gray-500">$0.00</span>
              ) : hasNoRevenueData && !isRevenueLoading ? (
                <span className="text-gray-500">
                  {formatCurrencyWithFallback(0)}
                </span>
              ) : (
                <span className="break-all">
                  {formatCurrencyWithFallback(revenueData?.totalRevenue.current || 0)}
                </span>
              )}
            </div>
            {isRevenueEnabled &&
              !isRevenueLoading &&
              !hasNoRevenueData &&
              revenueData?.totalRevenue.delta !== undefined &&
              revenueData.totalRevenue.delta !== 0 && (
                <div
                  className={`text-xs font-normal flex items-center gap-1 mt-1 ${
                    revenueData.totalRevenue.delta > 0
                      ? "text-green-400"
                      : "text-red-400"
                  }`}
                >
                  <DeltaArrow isPositive={revenueData.totalRevenue.delta > 0} />
                  {formatPercentageWithFallback(revenueData.totalRevenue.delta)}
                </div>
              )}
          </div>
        </RevenueTooltip>

        {/* Revenue Per Visitor - Always show but grey out if disabled */}
        <RevenueTooltip label="Revenue per visitor">
          <div className={`flex flex-col ${!isRevenueEnabled ? 'opacity-50 cursor-help' : ''}`}>
            <div className="text-xs text-gray-400 mb-1">
              Revenue/visitor
            </div>
            <div className="text-sm sm:text-base lg:text-lg xl:text-xl font-semibold text-white">
              {!isRevenueEnabled ? (
                <span className="text-gray-500">$0.00</span>
              ) : hasNoRevenueData && !isRevenueLoading ? (
                <span className="text-gray-500">
                  {formatCurrencyWithFallback(0)}
                </span>
              ) : (
                <span className="break-all">
                  {formatCurrencyWithFallback(revenueData?.revenuePerVisitor.current || 0)}
                </span>
              )}
            </div>
          </div>
        </RevenueTooltip>

        {/* Conversion Rate - Always show but grey out if disabled */}
        <RevenueTooltip label="Conversion rate">
          <div className={`flex flex-col ${!isRevenueEnabled ? 'opacity-50 cursor-help' : ''}`}>
            <div className="text-xs text-gray-400 mb-1">
              Conversion rate
            </div>
            <div className="text-sm sm:text-base lg:text-lg xl:text-xl font-semibold text-white">
              {!isRevenueEnabled ? (
                <span className="text-gray-500">0%</span>
              ) : hasNoRevenueData && !isRevenueLoading ? (
                <span className="text-gray-500">0%</span>
              ) : (
                `${(revenueData?.conversionRate.current || 0).toFixed(1)}%`
              )}
            </div>
          </div>
        </RevenueTooltip>

        {/* Bounce Rate */}
        <div className="flex flex-col">
          <div className="text-xs text-gray-400 mb-1">
            Bounce rate
          </div>
          <div className="text-sm sm:text-base lg:text-lg xl:text-xl font-semibold text-white">
            {hasNoData && !isAnalyticsLoading ? (
              <span className="text-gray-500">–</span>
            ) : (
              <span>{bounceRate}%</span>
            )}
          </div>
          {!isAnalyticsLoading &&
            !hasNoData &&
            bounceRateDelta !== null &&
            bounceRateDelta !== 0 &&
            Math.abs(bounceRateDelta) !== 100 && (
              <div
                className={`text-xs font-normal flex items-center gap-1 mt-1 ${
                  bounceRateDelta < 0 ? "text-green-400" : "text-red-400"
                }`}
              >
                <DeltaArrow isPositive={bounceRateDelta > 0} />
                {bounceRateDelta > 0 ? `+${bounceRateDelta}` : bounceRateDelta}%
              </div>
            )}
        </div>

        {/* Session Time */}
        <div className="flex flex-col">
          <div className="text-xs text-gray-400 mb-1">
            Session time
          </div>
          <div className="text-sm sm:text-base lg:text-lg xl:text-xl font-semibold text-white">
            {hasNoData && !isAnalyticsLoading ? (
              <span className="text-gray-500">–</span>
            ) : (
              <span>{formatSessionTime(avgVisitTime)}</span>
            )}
          </div>
          {!isAnalyticsLoading &&
            !hasNoData &&
            avgVisitTimeDeltaPercentage !== null &&
            avgVisitTimeDeltaPercentage !== 0 &&
            Math.abs(avgVisitTimeDeltaPercentage) !== 100 && (
              <div
                className={`text-xs font-normal flex items-center gap-1 mt-1 ${
                  avgVisitTimeDeltaPercentage > 0
                    ? "text-green-400"
                    : "text-red-400"
                }`}
              >
                <DeltaArrow isPositive={avgVisitTimeDeltaPercentage > 0} />
                {avgVisitTimeDeltaPercentage > 0 ? `+${avgVisitTimeDeltaPercentage}` : avgVisitTimeDeltaPercentage}%
              </div>
            )}
        </div>

        {/* Visitors Now */}
        <div className="flex flex-col">
          <div className="text-xs text-gray-400 mb-1 flex items-center gap-1">
            Visitors now
            <div className="relative flex h-2 w-2 flex-shrink-0">
              <div className="animate-ping absolute inline-flex h-full w-full rounded-full bg-blue-400 opacity-75"></div>
              <div className="relative inline-flex rounded-full h-2 w-2 bg-blue-500"></div>
            </div>
          </div>
          <div className="text-sm sm:text-base lg:text-lg xl:text-xl font-semibold text-white">
            {hasNoData && !isAnalyticsLoading ? (
              <span className="text-gray-500">0</span>
            ) : (
              <AnimatedCounter value={currentVisitors} />
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

export default HeaderList;
