import mongoose from "mongoose";
import { v4 as uuidv4 } from "uuid";

const userSchema = new mongoose.Schema({
  id: {
    type: String,
    required: true,
    unique: true,
    default: () => uuidv4(),
  },
  clerkId: {
    type: String,
    required: true,
    unique: true,
  },
  email: {
    type: String,
    required: true,
    unique: true,
  },
  name: {
    type: String,
    required: true,
  },
  websites: {
    type: [String],
    default: [],
  },
  preferences: {
    defaultWebsite: {
      type: String,
      required: false,
    },
    theme: {
      type: String,
      enum: ["light", "dark", "system"],
      default: "system",
    },
    emailNotifications: {
      type: Boolean,
      default: true,
    },
  },
  createdAt: {
    type: Date,
    default: Date.now,
  },
  customerId: { // Added Stripe Customer ID
    type: String,
    required: false, // Optional as existing users won't have it initially
  },
  priceId: {
    type: String,
    default: null,
  },
  accessUntil: {
    type: Date,
    default: () => new Date(Date.now() + 14 * 24 * 60 * 60 * 1000),
  },
  lastLoginAt: {
    type: Date,
    default: Date.now,
  },
});

export interface User extends mongoose.Document {
  id: string;
  clerkId: string;
  email: string;
  name: string;
  websites: string[];
  preferences: {
    defaultWebsite?: string;
    theme: "light" | "dark" | "system";
    emailNotifications: boolean;
  };
  createdAt: Date;
  lastLoginAt: Date;
  accessUntil: Date;
  customerId?: string; // Added Stripe Customer ID
}

const User = mongoose.models.User || mongoose.model<User>("User", userSchema);
export default User;
