export interface Website {
  id: string;
  name: string;
  domain: string;
  excludeAuthVisits?: boolean; // Add optional boolean property
  privateRoutes?: string[]; // Add optional string array property
  hasReceivedEvents?: boolean; // Flag indicating if any events have ever been received
  createdAt: Date;
  // Revenue Attribution Fields
  stripeApiKey?: string;
  stripeWebhookId?: string;
  stripeWebhookSecret?: string;
  revenueAttributionEnabled: boolean;
}

export type Period =
  | "today"
  | "yesterday"
  | "last24h"
  | "last7d"
  | "last30d"
  | "wtd" // week to date
  | "mtd" // month to date
  | "ytd" // year to date
  | "all"
  | "custom";

export interface CustomPeriod {
  startDate: Date;
  endDate: Date;
}

export interface ChartDataPoint {
  time: string;
  visitors: number | null;
  pulse: number | null;
  revenue?: number;
  orders?: number;
  _dateKey?: string; // Optional internal key for date matching
}

export type LocationFilter = {
  type: "location";
  value:
    | string
    | { city: string; country: string }
    | { region: string; country: string };
  label: string;
};

export type OtherFilter = {
  type: "referrer" | "campaign" | "utm" | "page" | "system" | "hour" | "entry-page";
  value: string;
  label: string;
};

export type Filter = LocationFilter | OtherFilter;

export interface TopPage {
  path: string;
  visitors: number;
}

export interface TopReferrer {
  url: string | null;
  visitors: number;
  normalizedDomain?: string; // Added for URL normalization and grouping
  referralParams?: {
    campaign?: string;
    utm_source?: string;
    utm_medium?: string;
    utm_campaign?: string;
    utm_term?: string;
    utm_content?: string;
    ref?: string;
    source?: string;
    referral?: string;
    affiliate?: string;
    fbclid?: string;
    gclid?: string;
    msclkid?: string;
    dclid?: string;
  };
}

export interface Location {
  city: string | null;
  country: string;
  region: string | null;
  visitors: number;
}

export interface Referrer {
  url: string | null;
  visitors: number;
  referralParams?: {
    campaign?: string;
    utm_source?: string;
    utm_medium?: string;
    utm_campaign?: string;
    utm_term?: string;
    utm_content?: string;
    ref?: string;
    source?: string;
    referral?: string;
    affiliate?: string;
    fbclid?: string;
    gclid?: string;
    msclkid?: string;
    dclid?: string;
  };
}

export interface Event {
  id: string;
  type: string;
  websiteId: string;
  domain: string;
  href: string;
  referrer: string | null;
  referralParams?: {
    campaign?: string;
    utm_source?: string;
    utm_medium?: string;
    utm_campaign?: string;
    utm_term?: string;
    utm_content?: string;
    ref?: string;
    source?: string;
    referral?: string;
    affiliate?: string;
    fbclid?: string;
    gclid?: string;
    msclkid?: string;
    dclid?: string;
  };
  timestamp: string;
  visitorId: string;
  sessionId: string;
  userAgent?: string;
  extraData?: any;
  location?: {
    city: string | null;
    country: string;
    region: string | null;
  };
  isAuthRedirect?: boolean;
  isBot: boolean;
  // Revenue Attribution Data (for payment events)
  revenueData?: {
    amount?: number; // Amount in cents
    currency?: string;
    stripeChargeId?: string;
    stripeCustomerId?: string;
    stripeSessionId?: string;
    customerEmail?: string;
    isNewCustomer?: boolean;
    isRecurring?: boolean;
    subscriptionId?: string;
    productNames?: string[];
    paymentType?: string; // 'one_time', 'subscription', 'renewal'
  };
}

export interface ChartDataItem {
  time: string;
  visitors: number | null;
  pulse: number | null;
}

export interface PageView {
  path: string;
  visitors: number;
}

export interface ExitLink {
  url: string;
  visitors: number;
}

export interface OperatingSystem {
  name: string;
  visitors: number;
}

export interface AnalyticsData {
  isLoading: boolean;
  chartData: ChartDataPoint[];
  uniqueVisitors: number;
  currentVisitors: number;
  bounceRate: number;
  avgVisitTime: number;
  topPages: any[];
  topReferrers: any[];
  operatingSystems: any[];
  browsers: any[];
  devices: any[];
  topLocations: any[];
  visitorsDeltaPercentage: number | null;
  visitorsDelta: number | null;
  bounceRateDelta: number | null;
  avgVisitTimeDeltaPercentage: number | null;
  avgVisitTimeDelta: number | null;
  entryPages: any[];
  exitPages: any[];
  exitLinks: ExitLink[];
  events: Event[];
  allEvents: Event[];
  totalPageviews: number;
  pageviewsDelta: number | null;
  visitorsSeries: ChartDataPoint[];
}
