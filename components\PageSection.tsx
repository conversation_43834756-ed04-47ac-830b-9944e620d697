import { useFilterState } from "@/hooks/useFilterState";
import { PageView, TopPage, ExitLink } from "@/lib/types";
import React, { Dispatch, SetStateAction, useState } from "react";

type PageSectionProps = {
  topPages: TopPage[];
  entryPages: PageView[];
  exitPages: PageView[];
  exitLinks: ExitLink[];
  maxPageVisitors: number;
  setShowPageModal: Dispatch<SetStateAction<boolean>>;
  setCurrentPageView: Dispatch<SetStateAction<"all" | "entry" | "exit" | "exit-links">>;
};

function PageSection({
  topPages,
  entryPages,
  exitPages,
  exitLinks,
  maxPageVisitors,
  setShowPageModal,
  setCurrentPageView,
}: PageSectionProps) {
  const { handleFilterClick, activeFilters } = useFilterState();
  const [pageView, setPageView] = useState<"all" | "entry" | "exit-links">("all");

  // Determine which pages to show based on the selected view (full list)
  const pagesToShow = React.useMemo(() => {
    switch (pageView) {
      case "entry":
        return entryPages;
      case "exit-links":
        return exitLinks.map(link => ({ path: link.url, visitors: link.visitors }));
      default:
        return topPages;
    }
  }, [pageView, topPages, entryPages, exitLinks]);

  // Only show the top 5 pages in the section
  const visiblePages = React.useMemo(() => {
    return pagesToShow.slice(0, 5);
  }, [pagesToShow]);

  // Update parent component's state when page view changes
  React.useEffect(() => {
    // Map the new page view types to the old ones for backward compatibility
    const mappedPageView = pageView === "exit-links" ? "exit-links" : 
                          pageView === "entry" ? "entry" : "all";
    setCurrentPageView(mappedPageView);
  }, [pageView, setCurrentPageView]);

  const handleShowMore = () => {
    setShowPageModal(true);
  };

  // Handle filter click for different view types
  const handleItemClick = (item: { path: string, visitors: number }) => {
    if (pageView === "exit-links") {
      // For exit links, filter by the URL in the extraData of external_link events
      const domain = getFaviconDomain(item.path);
      handleFilterClick("page", `external_link:${item.path}`, `Clicks to ${domain}`);
    } else {
      // For regular pages and entry pages, use the appropriate filter type
      const filterType = pageView === "entry" ? "entry-page" : "page";
      handleFilterClick(filterType, item.path, item.path);
    }
  };

  // Get domain for favicon
  const getFaviconDomain = (url: string) => {
    try {
      // Make sure URL has a protocol
      const fullUrl = url.startsWith('http') ? url : `https://${url}`;
      // Extract just the hostname (domain) part
      const hostname = new URL(fullUrl).hostname;
      // Remove www. prefix if present
      return hostname.replace(/^www\./, '');
    } catch (e) {
      // If URL parsing fails, try to extract domain manually
      const domainMatch = url.match(/^(?:https?:\/\/)?(?:www\.)?([^\/\?]+)/i);
      return domainMatch ? domainMatch[1] : url;
    }
  };

  // Get section titles with visitor counts
  const getSectionTitle = () => {
    switch (pageView) {
      case "entry":
        return `Entry Pages (${entryPages.length})`;
      case "exit-links":
        return `External Clicks (${exitLinks.length})`;
      default:
        return `Top Pages (${topPages.length})`;
    }
  };

  return (
    <div className="bg-[#2A2A2A] p-4 sm:p-6 rounded-lg">
      <div className="flex items-center justify-between mb-3">
        <h2 className="text-base sm:text-lg font-semibold text-white">
          {getSectionTitle()}
        </h2>
        <div className="flex rounded-lg p-1">
          <button
            onClick={() => setPageView("all")}
            className={`px-1.5 py-1 rounded-md text-xs sm:text-sm transition-colors whitespace-nowrap ${
              pageView === "all"
                ? "bg-blue-500 text-white"
                : "text-gray-400 hover:text-white"
            }`}
          >
            Pages
          </button>
          <button
            onClick={() => setPageView("entry")}
            className={`px-1.5 py-1 rounded-md text-xs sm:text-sm transition-colors whitespace-nowrap ${
              pageView === "entry"
                ? "bg-blue-500 text-white"
                : "text-gray-400 hover:text-white"
            }`}
          >
            Entries
          </button>
          <button
            onClick={() => setPageView("exit-links")}
            className={`px-1.5 py-1 rounded-md text-xs sm:text-sm transition-colors whitespace-nowrap ${
              pageView === "exit-links"
                ? "bg-blue-500 text-white"
                : "text-gray-400 hover:text-white"
            }`}
          >
            External
          </button>
        </div>
      </div>
      <div className="space-y-1 h-[250px] overflow-hidden relative">
        {visiblePages.map((page, i) => (
          <div
            key={i}
            className={`flex items-center justify-between p-1 sm:p-2 rounded text-xs sm:text-sm relative overflow-hidden group cursor-pointer ${
              pageView !== "exit-links" && activeFilters.some(
                (filter) => 
                  (pageView === "entry" ? 
                    filter.type === "entry-page" : 
                    filter.type === "page") && 
                  filter.value === page.path
              )
                ? "bg-blue-500/20"
                : ""
              }`}
            onClick={() => handleItemClick(page)}
          >
            <div
              className="absolute inset-0 bg-blue-500/40 transition-transform origin-left group-hover:bg-blue-500/20"
              style={{
                transform: `scaleX(${page.visitors / maxPageVisitors})`,
              }}
            />
            <span className="text-white truncate max-w-[70%] relative flex items-center gap-2">
              {pageView === "exit-links" ? (
                <>
                  <img
                    src={`https://www.google.com/s2/favicons?domain=${getFaviconDomain(page.path)}&sz=32`}
                    alt=""
                    className="w-4 h-4 flex-shrink-0"
                    onError={(e) => {
                      const target = e.target as HTMLImageElement;
                      target.onerror = null; // Prevent infinite loop
                      
                      // Try DuckDuckGo as first fallback
                      const domain = getFaviconDomain(page.path);
                      target.src = `https://icons.duckduckgo.com/ip3/${domain}.ico`;
                      
                      // Add another fallback in case DuckDuckGo fails
                      target.onerror = () => {
                        target.onerror = null;
                        target.src = "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='16' height='16'%3E%3Cpath fill='none' stroke='%234B5563' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14'%3E%3C/path%3E%3C/svg%3E";
                      };
                    }}
                  />
                  <span className="truncate">{page.path}</span>
                </>
              ) : (
                page.path
              )}
            </span>
            <span className="text-white ml-2 flex-shrink-0 relative flex items-center gap-2">
              {page.visitors}
              {pageView === "exit-links" && (
                <button
                  onClick={(e) => {
                    e.stopPropagation(); // Prevent triggering the filter
                    window.open(page.path, "_blank");
                  }}
                  className="ml-2 p-1 hover:bg-blue-500/20 rounded"
                  title="Open link"
                >
                  <svg
                    className="w-4 h-4"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"
                    />
                  </svg>
                </button>
              )}
            </span>
          </div>
        ))}
        {pagesToShow.length > 5 && (
          <button
            onClick={handleShowMore}
            className="absolute bottom-0 left-0 right-0 text-center py-2 text-blue-500 hover:text-blue-400 bg-gradient-to-t from-[#2A2A2A] via-[#2A2A2A] to-transparent"
          >
            Show more
          </button>
        )}
      </div>
    </div>
  );
}
export default PageSection;

