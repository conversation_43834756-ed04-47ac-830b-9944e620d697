import connect from "@/util/db";
import Event from "@/models/event";
import { NextResponse } from "next/server";

export async function POST() {
  try {
    await connect();
    // Delete events with null location fields
    const result = await Event.deleteMany({
      "location.city": null,
      "location.country": null,
      "location.region": null,
    });

    const deleteLocal = await Event.deleteMany({
      $or: [
        { "location.city": "Farmington Hills" },
        { "location.city": "Croydon" },
      ],
    });
    console.log(
      `${result.deletedCount} events with null location fields deleted successfully
      ${deleteLocal.deletedCount} deleted locally
      ${result.deletedCount + deleteLocal.deletedCount} total events deleted
      `
    );
    return NextResponse.json({
      message: "Events with null location fields deleted successfully",
      deletedCount: result.deletedCount,
      deletedLocally: deleteLocal.deletedCount,
      totalDeleted: result.deletedCount + deleteLocal.deletedCount,
    });
  } catch (error) {
    console.error("Cleaning error:", error);
    return NextResponse.json({ error: "Failed to clean" }, { status: 500 });
  }
}
