"use client";
import { Website } from "@/lib/types";
import React, { useState } from "react";
import { useRouter } from "next/navigation";

import { Button } from "@/components/ui/button";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "./ui/alert-dialog";
import toast from "react-hot-toast";

type Props = {
  website: Website;
};

function GeneralSettingsTab({ website }: Props) {
  const router = useRouter();
  const [validationStatus, setValidationStatus] = useState<{
    isLoading: boolean;
    result: null | {
      isValid: boolean;
      message: string;
    };
  }>({ isLoading: false, result: null });
  const [copied, setCopied] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [deleteError, setDeleteError] = useState<string | null>(null);

  function handleCopy() {
    if (website) {
      navigator.clipboard.writeText(getScriptCode(website));
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    }
  }

  const getScriptCode = (website: Website) => {
    return `<script
defer
src="https://versatailor.com/script.js"
data-website-id="${website.id}"
data-domain="${website.domain}"
></script>`;
  };

  async function handleValidateScript() {
    // ... (validation logic remains the same)
    if (!website) return;

    try {
      setValidationStatus({ isLoading: true, result: null });

      const response = await fetch("/api/validate/script", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          url: website.domain,
          websiteId: website.id,
        }),
      });

      const data = await response.json();

      if (response.ok) {
        if (data.isValid) {
          setValidationStatus({
            isLoading: false,
            result: {
              isValid: true,
              message: "The script is correctly installed on your website! 🎉",
            },
          });
        } else if (data.isInstalled && !data.isInHead) {
          setValidationStatus({
            isLoading: false,
            result: {
              isValid: false,
              message:
                "Script was found but not in the <head> section. Please move it there for optimal tracking.",
            },
          });
        } else {
          setValidationStatus({
            isLoading: false,
            result: {
              isValid: false,
              message:
                "Could not find the tracking script on your website. Please verify it's installed correctly.",
            },
          });
        }
      } else {
        setValidationStatus({
          isLoading: false,
          result: {
            isValid: false,
            message:
              data.error || "Failed to validate script. Please try again.",
          },
        });
      }
    } catch (error) {
      setValidationStatus({
        isLoading: false,
        result: {
          isValid: false,
          message: "An error occurred during validation. Please try again.",
        },
      });
    }
  }

  // --- Function to perform the actual deletion after confirmation ---
  async function confirmAndProceedWithDelete() {
    if (!website) return;

    setIsDeleting(true);
    setDeleteError(null); // Clear previous errors
    try {
      const response = await fetch(`/api/websites/${website.id}`, {
        method: "DELETE",
      });

      if (response.ok) {
        // Successfully disassociated user
        // Using alert temporarily, consider a toast notification library later
        toast.success("Website removed successfully!");
        router.push("/dashboard"); // Redirect to dashboard
        router.refresh(); // Refresh data on the dashboard
      } else {
        // Handle error response
        const errorData = await response.json();
        setDeleteError(errorData.error || "Failed to remove website.");
        console.error("Failed to remove website:", errorData);
        // Keep the modal open or display error elsewhere if needed
      }
    } catch (error) {
      setDeleteError("An unexpected error occurred. Please try again.");
      console.error("Error removing website:", error);
      // Keep the modal open or display error elsewhere if needed
    } finally {
      // Only set isDeleting to false if staying in the modal on error,
      // otherwise the component might unmount before state update finishes.
      // If redirecting on success, this might not be necessary or could cause issues.
      // For simplicity now, we'll set it regardless. Consider refining if needed.
      setIsDeleting(false);
    }
  }
  // --- End confirmAndProceedWithDelete function ---

  return (
    <div className="p-6 space-y-8">
      {/* --- Tracking Code Section --- */}
      <div className="space-y-4">
        <h2 className="text-xl font-semibold flex items-center gap-2">
          <svg
            className="w-5 h-5 text-blue-500"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
            />
          </svg>
          Tracking Code
        </h2>
        <div className="space-y-2">
          <p className="text-sm text-gray-400">
            Add this code to your website&apos;s{" "}
            <code className="text-blue-400">&lt;head&gt;</code> tag to start
            tracking.
          </p>
          <div className="bg-[#1C1C1C] rounded-lg border border-gray-700/50 overflow-hidden">
            <div className="flex items-center justify-end p-2 bg-[#363636] border-b border-gray-700/50">
              <Button
                variant="ghost"
                size="sm"
                onClick={handleCopy}
                className="text-blue-400 hover:text-blue-300"
              >
                {copied ? (
                  <>
                    <svg
                      className="w-4 h-4 mr-2"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M5 13l4 4L19 7"
                      />
                    </svg>
                    Copied!
                  </>
                ) : (
                  <>
                    <svg
                      className="w-4 h-4 mr-2"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"
                      />
                    </svg>
                    Copy Code
                  </>
                )}
              </Button>
            </div>
            <pre className="p-4 text-sm overflow-x-auto whitespace-pre-wrap break-all text-xs sm:text-sm">
              <code className="font-mono text-gray-300">
                {getScriptCode(website)}
              </code>
            </pre>
          </div>
        </div>

        <div className="mt-6 pt-4">
          <Button
            onClick={handleValidateScript}
            disabled={validationStatus.isLoading}
            variant="default" // Assuming default blue style
          >
            {validationStatus.isLoading ? (
              <>
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                <span>Validating...</span>
              </>
            ) : (
              <>
                <svg
                  className="w-4 h-4 mr-2"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
                Verify Installation
              </>
            )}
          </Button>

          {validationStatus.result && (
            <div
              className={`mt-4 p-3 rounded ${
                validationStatus.result.isValid
                  ? "bg-green-500/10 text-green-400 border border-green-500/20"
                  : "bg-red-500/10 text-red-400 border border-red-500/20"
              }`}
            >
              <div className="flex items-start gap-2">
                {/* ... validation result icons ... */}
                {validationStatus.result.isValid ? (
                  <svg
                    className="w-5 h-5 mt-0.5 flex-shrink-0"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                    />
                  </svg>
                ) : (
                  <svg
                    className="w-5 h-5 mt-0.5 flex-shrink-0"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                    />
                  </svg>
                )}
                {/* Wrap text content in a div */}
                <div>
                  <p>{validationStatus.result.message}</p>
                  {!validationStatus.result.isValid && (
                    <p className="mt-1">
                      {" "}
                      {/* Add margin-top */}
                      Having trouble?{" "}
                      <a
                        href="mailto:<EMAIL>?subject=Having trouble setting up Versatailor script"
                        className="text-blue-400"
                      >
                        Email Support
                      </a>
                    </p>
                  )}
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
      {/* --- End Tracking Code Section --- */}

      {/* --- Delete Website Section --- */}
      <div className="border-t border-gray-700/50 pt-6 space-y-4">
        <div className="flex justify-end">
          {/* --- AlertDialog Implementation --- */}
          <AlertDialog>
            <AlertDialogTrigger asChild>
              <Button variant="destructive" disabled={isDeleting}>
                {isDeleting ? (
                  <>
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                    <span>Removing...</span>
                  </>
                ) : (
                  "Remove Website"
                )}
              </Button>
            </AlertDialogTrigger>
            <AlertDialogContent className="bg-[#1C1C1C] border border-gray-700/50">
              <AlertDialogHeader>
                <AlertDialogTitle className="text-gray-100">
                  Are you absolutely sure?
                </AlertDialogTitle>
                <AlertDialogDescription className="text-gray-400">
                  This action cannot be undone. This will permanently delete the
                  website&apos;s analytics.
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel
                  className="bg-gray-800 text-gray-100 hover:bg-gray-700 border-gray-700"
                  disabled={isDeleting}
                >
                  Cancel
                </AlertDialogCancel>
                <AlertDialogAction
                  onClick={confirmAndProceedWithDelete}
                  disabled={isDeleting}
                  className="bg-red-600 hover:bg-red-700 text-gray-100"
                >
                  {isDeleting ? "Removing..." : "Yes, Remove Website"}
                </AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>
          {/* --- End AlertDialog Implementation --- */}

          {/* Display error message below the button/dialog trigger */}
          {deleteError && (
            <p className="mt-2 text-sm text-red-400">{deleteError}</p>
          )}
        </div>
      </div>
      {/* --- End Delete Website Section --- */}
    </div>
  );
}
export default GeneralSettingsTab;
