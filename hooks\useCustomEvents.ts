import { useState, useEffect, useCallback, useMemo } from "react";
import { Period, Filter, CustomPeriod, Event } from "@/lib/types";

// Add interface for custom events
export interface CustomEvent {
  name: string;
  count: number;
  uniqueUsers: number;
  visitorId: string;
  timestamp: string;
}

export function useCustomEvents(
  websiteId: string,
  selectedPeriod: Period,
  activeFilters: Filter[],
  customPeriod?: CustomPeriod
) {
  const [customEvents, setCustomEvents] = useState<CustomEvent[]>([]);
  const [isLoadingEvents, setIsLoadingEvents] = useState<boolean>(false);
  const [selectedEventJourneys, setSelectedEventJourneys] = useState<{
    eventName: string;
    journeys: Array<{
      visitorId: string;
      timestamp: string;
      location?: { city: string; country: string };
    }>;
  } | null>(null);

  // Session-level cache for custom events data
  const customEventsCache = useMemo(
    () => ({
      events: new Map<string, { data: any[]; timestamp: number }>(),
      processedEvents: new Map<
        string,
        { data: CustomEvent[]; timestamp: number }
      >(),
      cacheVersion: "v1.1", // Increment this when the custom events format changes
    }),
    []
  );

  // Cache timeout - 24 hours
  const CACHE_TIMEOUT = 24 * 60 * 60 * 1000;

  // Normalize dates for consistent cache keys
  const normalizeDate = useCallback(
    (date: Date) => {
      // For periods other than today/yesterday/last24h, normalize to start of day
      if (!["today", "yesterday", "last24h"].includes(selectedPeriod)) {
        return new Date(
          date.getFullYear(),
          date.getMonth(),
          date.getDate()
        ).toISOString();
      }
      return date.toISOString();
    },
    [selectedPeriod]
  );

  // Generate a normalized cache key
  const generateCacheKey = useCallback(
    (
      websiteId: string,
      period: Period,
      startDate: Date,
      endDate: Date,
      customPeriod?: CustomPeriod
    ) => {
      const normalizedStart = normalizeDate(startDate);
      const normalizedEnd = normalizeDate(endDate);

      // Create a cache key that's agnostic to the parameter names
      // Always put websiteId first to prevent mixing data between websites
      return `customEvents_website_${websiteId}_${normalizedStart}_${normalizedEnd}_${period}${
        customPeriod
          ? `_custom_${normalizeDate(
              new Date(customPeriod.startDate)
            )}_${normalizeDate(new Date(customPeriod.endDate))}`
          : ""
      }`;
    },
    [normalizeDate]
  );

  // Function to check if the cache key belongs to the current website
  const isCurrentWebsiteCacheKey = useCallback((cacheKey: string, currentWebsiteId: string): boolean => {
    const parts = cacheKey.split('_');
    // Make sure the custom events cache key format matches: customEvents_website_[websiteId]_...
    return parts.length >= 3 && parts[0] === 'customEvents' && parts[1] === 'website' && parts[2] === currentWebsiteId;
  }, []);

  // Process event data into custom events format
  const processEventsIntoCustomEvents = useCallback(
    (events: any[]): CustomEvent[] => {
      try {
        if (!events || events.length === 0) {
          return [];
        }

        // Group events by event name
        const eventsByName: Record<string, any[]> = {};
        events.forEach((event) => {
          try {
            const eventName = event.extraData?.eventName;
            if (!eventName) return;

            if (!eventsByName[eventName]) {
              eventsByName[eventName] = [];
            }
            eventsByName[eventName].push(event);
          } catch (error) {
            console.error("Error processing event:", error, event);
          }
        });

        // Count events and unique users
        const customEvents = Object.entries(eventsByName).map(
          ([name, events]) => {
            // Get most recent event for each event name
            const mostRecentEvent = events.reduce((latest, current) => {
              return new Date(current.timestamp) > new Date(latest.timestamp)
                ? current
                : latest;
            }, events[0]);

            // Count unique visitors
            const uniqueVisitors = new Set(events.map((e) => e.visitorId)).size;

            return {
              name,
              count: events.length,
              uniqueUsers: uniqueVisitors,
              visitorId: mostRecentEvent.visitorId,
              timestamp: mostRecentEvent.timestamp,
            };
          }
        );

        // Sort by count (descending)
        return customEvents.sort((a, b) => b.count - a.count);
      } catch (error) {
        console.error("Error processing custom events:", error);
        return [];
      }
    },
    []
  );

  // Add this filterEventsByHour function
  const filterEventsByHour = (events: any[], hourFilter: string): any[] => {
    if (!hourFilter) return events;

    // Parse the hour filter value
    let hourToFilter = 0;
    // Normalize filter value by removing spaces and converting to uppercase
    const normalizedFilter = hourFilter.toUpperCase().replace(/\s+/g, "");

    if (normalizedFilter.includes("AM")) {
      // Handle 12 AM as 0
      if (normalizedFilter === "12AM") {
        hourToFilter = 0;
      } else {
        hourToFilter = parseInt(normalizedFilter.replace("AM", ""));
      }
    } else if (normalizedFilter.includes("PM")) {
      // Handle 12 PM as 12, others add 12
      if (normalizedFilter === "12PM") {
        hourToFilter = 12;
      } else {
        hourToFilter = parseInt(normalizedFilter.replace("PM", "")) + 12;
      }
    } else {
      // If no AM/PM specified, assume 24-hour format
      hourToFilter = parseInt(hourFilter);
    }

    // Filter events by the hour
    return events.filter((event) => {
      if (!event.timestamp) return false;
      const eventDate = new Date(event.timestamp);
      return eventDate.getHours() === hourToFilter;
    });
  };

  // Add filterEvents function from useAnalyticsData
  const filterEvents = (events: any[]): any[] => {
    if (!activeFilters.length) return events;

    if (events.length === 0) {
      return [];
    }

    return events.filter((event) => {
      // Event must match ALL filters (AND condition)
      return activeFilters.every((activeFilter) => {
        switch (activeFilter.type) {
          case "referrer":
            // Handle the "direct" case (no referrer)
            if (activeFilter.value === "direct") {
              return !event.referrer;
            }
            // For all other referrers, perform an exact string match
            else {
              // Ensure both values are treated as strings for comparison
              // Use nullish coalescing to handle potential null/undefined referrers gracefully
              return (event.referrer ?? null) === (activeFilter.value ?? null);
            }

          case "campaign":
            // Check campaign data in multiple places
            const campaignFilter = activeFilter.value.toLowerCase();

            // Check in referralParams
            const refParam = event.referralParams?.ref?.toLowerCase();
            const campaignParam = event.referralParams?.campaign?.toLowerCase();
            const utmCampaignParam =
              event.referralParams?.utm_campaign?.toLowerCase();

            // Check in URL parameters
            let urlRef, urlCampaign, urlUtmCampaign;
            try {
              const url = new URL(event.href);
              urlRef = url.searchParams.get("ref")?.toLowerCase();
              urlCampaign = url.searchParams.get("campaign")?.toLowerCase();
              urlUtmCampaign = url.searchParams
                .get("utm_campaign")
                ?.toLowerCase();
            } catch (e) {
              // Invalid URL, ignore
            }

            // Check in event properties directly
            const eventRef = (event as any).ref?.toLowerCase();
            const eventCampaign = (event as any).campaign?.toLowerCase();
            const eventUtmCampaign = (event as any).utm_campaign?.toLowerCase();

            // Check for exact matches first
            if (
              campaignFilter === refParam ||
              campaignFilter === campaignParam ||
              campaignFilter === utmCampaignParam ||
              campaignFilter === urlRef ||
              campaignFilter === urlCampaign ||
              campaignFilter === urlUtmCampaign ||
              campaignFilter === eventRef ||
              campaignFilter === eventCampaign ||
              campaignFilter === eventUtmCampaign
            ) {
              return true;
            }

            return false;

          case "page":
            // Safely extract pathname from href
            let pathname: string;
            try {
              // Try to create URL object
              const url = new URL(event.href);
              pathname = url.pathname;
            } catch (error) {
              // If href is not a valid URL (e.g., relative path), use it as-is
              // Remove query params and hash if present
              pathname = event.href.split('?')[0].split('#')[0];
            }
            return pathname === activeFilter.value;

          case "location":
            if (!event.location) return false;
            const value = activeFilter.value;
            if (typeof value === "string") {
              return event.location.country === value;
            } else if ("city" in value) {
              return (
                event.location.city === value.city &&
                event.location.country === value.country
              );
            } else if ("region" in value) {
              return (
                event.location.region === value.region &&
                event.location.country === value.country
              );
            }
            return false;

          case "system":
            const userAgent = event.userAgent || "";
            // OS check - simplified to match generic OS names
            if (activeFilter.value === "Windows") {
              return /Windows/.test(userAgent);
            } else if (activeFilter.value === "Android") {
              return /Android/.test(userAgent);
            } else if (activeFilter.value === "iOS") {
              return /(iPhone|iPad|iPod)/.test(userAgent);
            } else if (activeFilter.value === "macOS") {
              // More specific macOS check that excludes iOS devices
              return /Mac OS X/.test(userAgent) && !/(iPhone|iPad|iPod)/.test(userAgent);
            } else if (activeFilter.value === "Linux") {
              return /Linux/.test(userAgent);
            } else if (activeFilter.value === "Chrome OS") {
              return /CrOS/.test(userAgent);
            } else if (activeFilter.value === "Windows Phone") {
              return /Windows Phone/.test(userAgent);
            } else if (activeFilter.value === "Unknown") {
              // Return true if none of the known OS patterns match
              return !(
                /Windows/.test(userAgent) ||
                /Android/.test(userAgent) ||
                /(iPhone|iPad|iPod)/.test(userAgent) ||
                (/Mac OS X/.test(userAgent) && !/(iPhone|iPad|iPod)/.test(userAgent)) ||
                /Linux/.test(userAgent) ||
                /CrOS/.test(userAgent) ||
                /Windows Phone/.test(userAgent)
              );
            }
            // Browser check
            else if (["Chrome", "Firefox", "Safari", "Edge", "Opera"].includes(activeFilter.value)) {
              const browserMap: Record<string, RegExp> = {
                Chrome: /Chrome/,
                Firefox: /Firefox/,
                Safari: /Safari/,
                Edge: /Edge/,
                Opera: /Opera|OPR/,
              };
              return browserMap[activeFilter.value].test(userAgent);
            }
            // Device check
            else if (["Mobile", "Tablet", "Desktop"].includes(activeFilter.value)) {
              if (activeFilter.value === "Mobile") {
                return /Mobile|Android|iPhone|iPod/.test(userAgent) && !/iPad/.test(userAgent);
              } else if (activeFilter.value === "Tablet") {
                return /iPad/.test(userAgent);
              } else {
                return !/Mobile|Android|iPhone|iPad|iPod/.test(userAgent);
              }
            }
            return false;

          default:
            return true;
        }
      });
    });
  };

  // Function to clean up old format cache entries
  const cleanupOldCacheEntries = useCallback(() => {
    // Clear old format localStorage cache entries
    try {
      const storedCacheKeys = localStorage.getItem('customEventsCacheKeys');
      if (storedCacheKeys) {
        const cacheKeys = JSON.parse(storedCacheKeys) as string[];
        let hasRemovedOldEntries = false;
        
        // Filter out keys that don't match new format (customEvents_website_[websiteId])
        const validKeys = cacheKeys.filter(key => {
          const parts = key.split('_');
          const isValidFormat = parts.length >= 3 && parts[0] === 'customEvents' && parts[1] === 'website';
          
          if (!isValidFormat) {
            // Also remove the corresponding localStorage item
            try {
              localStorage.removeItem(`customEventsCache_${key}`);
            } catch (e) {
              console.warn('Error removing cache entry:', e);
            }
            hasRemovedOldEntries = true;
            console.log(`Removed old format custom events cache key: ${key}`);
          }
          
          return isValidFormat;
        });
        
        if (hasRemovedOldEntries) {
          try {
            // Update the stored keys
            localStorage.setItem('customEventsCacheKeys', JSON.stringify(validKeys));
            console.log('Cleaned up old format custom events cache entries');
          } catch (e) {
            // If we hit quota, start removing oldest entries
            while (validKeys.length > 0) {
              try {
                const reducedKeys = validKeys.slice(1); // Remove oldest key
                localStorage.setItem('customEventsCacheKeys', JSON.stringify(reducedKeys));
                break;
              } catch (e2) {
                validKeys.shift(); // Remove one more and try again
              }
            }
          }
        }
      }
      
      // Also clear old format session cache entries
      for (const key of customEventsCache.events.keys()) {
        const parts = key.split('_');
        if (!(parts.length >= 3 && parts[0] === 'customEvents' && parts[1] === 'website')) {
          customEventsCache.events.delete(key);
        }
      }
    } catch (error) {
      console.error('Error cleaning up old custom events cache entries:', error);
    }
  }, [customEventsCache]);
  
  // Initialize cleanup on mount
  useEffect(() => {
    // Clean up old format cache entries
    cleanupOldCacheEntries();
    
    // Check for version changes
    const storedVersion = localStorage.getItem('customEventsCacheVersion');
    if (storedVersion !== customEventsCache.cacheVersion) {
      // Clear all custom events caches
      customEventsCache.events.clear();
      customEventsCache.processedEvents.clear();
      
      // Update stored version
      localStorage.setItem('customEventsCacheVersion', customEventsCache.cacheVersion);
    }
  }, [customEventsCache, cleanupOldCacheEntries]);

  // Initialize cache from localStorage
  useEffect(() => {
    try {
      // Clean up old format cache entries first
      cleanupOldCacheEntries();
      
      // Load cache indicators from localStorage
      const storedCacheKeys = localStorage.getItem("customEventsCacheKeys");
      if (storedCacheKeys) {
        const cacheKeys = JSON.parse(storedCacheKeys) as string[];

        // Load each cache item individually
        cacheKeys.forEach((key) => {
          // Only load cache items for the current website
          if (isCurrentWebsiteCacheKey(key, websiteId)) {
            const storedCache = localStorage.getItem(`customEventsCache_${key}`);
            if (storedCache) {
              const { data, timestamp } = JSON.parse(storedCache);
              customEventsCache.events.set(key, { data, timestamp });
            }
          }
        });
      }
    } catch (error) {
      console.error(
        "Error loading custom events cache from localStorage:",
        error
      );
      localStorage.removeItem("customEventsCacheKeys");
    }
  }, [customEventsCache, websiteId, isCurrentWebsiteCacheKey, cleanupOldCacheEntries]);

  // Add page refresh handling to clear session cache
  useEffect(() => {
    // Function to handle page load/refresh
    const handlePageLoad = () => {
      console.log("Page refreshed, clearing custom events session cache");
      customEventsCache.events.clear();
      customEventsCache.processedEvents.clear();
    };

    // Register the event listener
    window.addEventListener('load', handlePageLoad);
    
    // Clean up the event listener on unmount
    return () => {
      window.removeEventListener('load', handlePageLoad);
    };
  }, [customEventsCache]);

  // Function to safely store in localStorage with fallback cleanup
  const safeLocalStorageSet = useCallback((key: string, value: string) => {
    try {
      localStorage.setItem(key, value);
    } catch (error: any) {
      if ((error.name === 'QuotaExceededError' || error.code === 22) && error instanceof Error) {
        // Start by removing the oldest entries until we can store the new one
        const storedCacheKeys = localStorage.getItem('customEventsCacheKeys');
        if (storedCacheKeys) {
          const cacheKeys = JSON.parse(storedCacheKeys) as string[];
          
          while (cacheKeys.length > 0) {
            try {
              // Remove oldest cache entry
              const oldestKey = cacheKeys.shift();
              if (oldestKey) {
                localStorage.removeItem(`customEventsCache_${oldestKey}`);
              }
              
              // Try to update the keys list and store the new value
              localStorage.setItem('customEventsCacheKeys', JSON.stringify(cacheKeys));
              localStorage.setItem(key, value);
              break;
            } catch (e) {
              // Continue removing items if still not enough space
              continue;
            }
          }
        } else {
          // If no cache keys exist, try clearing everything
          try {
            localStorage.clear();
            localStorage.setItem(key, value);
          } catch (e) {
            console.warn('Unable to store in localStorage even after clearing:', e);
          }
        }
      }
    }
  }, []);

  // Fetch custom events based on the selected period
  useEffect(() => {
    // Create abort controller for cleanup
    const controller = new AbortController();
    let isMounted = true;

    // Track in-flight requests to prevent duplicates
    const requestsInFlight = new Set<string>();

    const fetchCustomEvents = async () => {
      if (!websiteId) return;

      setIsLoadingEvents(true);

      try {
        // Get current date for reference and log timezone info
        const now = new Date();

        // Create fresh date objects for start and end
        let startDate, endDate;

        switch (selectedPeriod) {
          case "today":
            // Start of today in local time
            startDate = new Date();
            startDate.setHours(0, 0, 0, 0);
            endDate = new Date(); // current time
            break;

          case "yesterday":
            // Yesterday's full day in local time
            startDate = new Date();
            startDate.setDate(startDate.getDate() - 1);
            startDate.setHours(0, 0, 0, 0);
            endDate = new Date(startDate);
            endDate.setHours(23, 59, 59, 999);
            break;

          case "last24h":
            // Exactly 24 hours ago from current time
            endDate = new Date();
            startDate = new Date(endDate.getTime() - 24 * 60 * 60 * 1000);
            break;

          case "last7d":
            // Last 7 days including today in UTC
            endDate = new Date();
            startDate = new Date(
              Date.UTC(
                endDate.getUTCFullYear(),
                endDate.getUTCMonth(),
                endDate.getUTCDate() - 6,
                0,
                0,
                0,
                0
              )
            );
            break;

          case "last30d":
            // Last 30 days including today in UTC
            endDate = new Date();
            startDate = new Date(
              Date.UTC(
                endDate.getUTCFullYear(),
                endDate.getUTCMonth(),
                endDate.getUTCDate() - 29,
                0,
                0,
                0,
                0
              )
            );
            break;

          case "wtd":
            // Week to date (starting Monday) in local time
            const currentDate = new Date();
            // Get the current day in local time (0 = Sunday, 1 = Monday, ..., 6 = Saturday)
            const currentDay = currentDate.getDay();

            // Calculate days to subtract to get to the most recent Monday
            // If today is Sunday (0), go back 6 days to the previous Monday
            // Otherwise go back to the most recent Monday (current day - 1)
            const daysToSubtract = currentDay === 0 ? 6 : currentDay - 1;

            // Create start date at the beginning of the most recent Monday in LOCAL time
            startDate = new Date();
            startDate.setDate(startDate.getDate() - daysToSubtract);
            startDate.setHours(0, 0, 0, 0);

            // Set end date to current time in LOCAL time
            endDate = new Date();

            break;

          case "mtd":
            // Month to date in UTC
            startDate = new Date(
              Date.UTC(now.getUTCFullYear(), now.getUTCMonth(), 1, 0, 0, 0, 0)
            );
            endDate = new Date();
            break;

          case "ytd":
            // Year to date in UTC
            startDate = new Date(
              Date.UTC(now.getUTCFullYear(), 0, 1, 0, 0, 0, 0)
            );
            endDate = new Date();
            break;

          case "all":
            // All time - using a reasonable past date in UTC
            startDate = new Date(Date.UTC(2000, 0, 1, 0, 0, 0, 0));
            endDate = new Date();
            break;

          case "custom":
            if (customPeriod) {
              // Create dates in local time
              startDate = new Date(customPeriod.startDate);
              startDate.setHours(0, 0, 0, 0);

              // For the end date, set it to the end of the day (23:59:59.999) in local time
              endDate = new Date(customPeriod.endDate);
              // Ensure we're working with the correct day by resetting hours first
              endDate.setHours(0, 0, 0, 0);
              // Then set to end of day
              endDate.setHours(23, 59, 59, 999);
            } else {
              startDate = new Date();
              startDate.setHours(0, 0, 0, 0);
              endDate = new Date();
            }
            break;
        }

        // Log the local time ranges before converting

        // Convert to UTC for API
        const formattedStartDate = startDate.toISOString();
        const formattedEndDate = endDate.toISOString();

        // Generate cache key for this request
        const cacheKey = generateCacheKey(
          websiteId,
          selectedPeriod,
          startDate,
          endDate,
          customPeriod
        );

        // First check if we have cached results
        const cachedData = customEventsCache.events.get(cacheKey);
        const cacheTime = Date.now();

        // If we have valid cached data, use it
        if (cachedData && cacheTime - cachedData.timestamp < CACHE_TIMEOUT) {
          // Process cached events
          let eventsData = cachedData.data;

          // Filter for custom events first
          eventsData = eventsData.filter((event: any) => {
            try {
              // Include both custom_update events and signup events that have eventName
              const isCustomOrSignupType = event.type === "custom_update" || event.type === "signup";
              const hasEventName = event.extraData?.eventName;

              return isCustomOrSignupType && hasEventName;
            } catch (error) {
              console.error("Error filtering event:", error, event);
              return false;
            }
          });

          // Apply filters before processing
          eventsData = filterEvents(eventsData);

          // Apply hour filtering if present
          const hourFilter = activeFilters.find((f) => f.type === "hour");
          if (hourFilter) {
            eventsData = filterEventsByHour(
              eventsData,
              hourFilter.value as string
            );
          }

          // Process filtered events
          const result = processEventsIntoCustomEvents(eventsData);

          if (isMounted) {
            setCustomEvents(result);
            setIsLoadingEvents(false);
          }

          return;
        }

        // Fetch custom events with the updated date range
        // Use a more inclusive query that gets all events and filters client-side
        const url = `/api/events?websiteId=${websiteId}&startDate=${encodeURIComponent(
          formattedStartDate
        )}&endDate=${encodeURIComponent(formattedEndDate)}`;

        // Check if this request is already in flight
        if (requestsInFlight.has(url)) {
          return;
        }

        // Mark request as in flight
        requestsInFlight.add(url);

        const response = await fetch(url, {
          signal: controller.signal,
        }).finally(() => {
          requestsInFlight.delete(url);
        });

        if (!response.ok) {
          const errorText = await response.text();
          console.error("API Error Response:", {
            status: response.status,
            statusText: response.statusText,
            body: errorText,
          });
          throw new Error(`API error: ${response.statusText} - ${errorText}`);
        }

        let eventsData = await response.json();

        // Store the raw response in cache
        customEventsCache.events.set(cacheKey, {
          data: eventsData,
          timestamp: cacheTime,
        });

        // Filter for custom events first
        eventsData = eventsData.filter((event: any) => {
          try {
            // Include both custom_update events and signup events that have eventName
            const isCustomOrSignupType = event.type === "custom_update" || event.type === "signup";
            const hasEventName = event.extraData?.eventName;

            return isCustomOrSignupType && hasEventName;
          } catch (error) {
            console.error("Error filtering event:", error, event);
            return false;
          }
        });

        // Apply filters before processing
        eventsData = filterEvents(eventsData);

        // Apply hour filtering if present
        const hourFilter = activeFilters.find((f) => f.type === "hour");
        if (hourFilter) {
          eventsData = filterEventsByHour(
            eventsData,
            hourFilter.value as string
          );
        }

        // Process filtered events
        const result = processEventsIntoCustomEvents(eventsData);

        if (isMounted) {
          setCustomEvents(result);
          setIsLoadingEvents(false);
        }

        // Try to save to localStorage for persistence if needed
        try {
          // Get existing cache keys
          const storedCacheKeys = localStorage.getItem("customEventsCacheKeys");
          let cacheKeys = storedCacheKeys ? JSON.parse(storedCacheKeys) : [];

          // Add the new key if it doesn't exist
          if (!cacheKeys.includes(cacheKey)) {
            // If we're over a limit, remove the oldest key (up to 20 periods)
            if (cacheKeys.length >= 20) {
              const oldestKey = cacheKeys.shift();
              localStorage.removeItem(`customEventsCache_${oldestKey}`);
            }

            cacheKeys.push(cacheKey);
            safeLocalStorageSet("customEventsCacheKeys", JSON.stringify(cacheKeys));
          }

          // Store the data (just raw events to save space)
          const cacheData = JSON.stringify({
            data: eventsData.map((e: any) => ({
              id: e.id,
              type: e.type,
              visitorId: e.visitorId,
              timestamp: e.timestamp,
              extraData: e.extraData,
            })),
            timestamp: cacheTime,
          });
          
          safeLocalStorageSet(`customEventsCache_${cacheKey}`, cacheData);
        } catch (error) {
          console.error(
            "Error storing custom events cache in localStorage:",
            error
          );
        }
      } catch (err) {
        if (!controller.signal.aborted) {
          console.error("Error fetching custom events:", err);
        }
        if (isMounted) {
          setCustomEvents([]);
          setIsLoadingEvents(false);
        }
      }
    };

    fetchCustomEvents();

    return () => {
      isMounted = false;
      controller.abort();
    };
  }, [
    websiteId,
    selectedPeriod,
    activeFilters,
    customPeriod,
    generateCacheKey,
    customEventsCache,
    processEventsIntoCustomEvents,
    safeLocalStorageSet,
  ]);

  // Function to handle showing journey list
  const handleShowJourneyList = async (eventName: string) => {
    try {
      setIsLoadingEvents(true);

      // Get any hour filter that might be active
      const hourFilter = activeFilters.find((f) => f.type === "hour");

      // Use the same date range logic as the main event fetching
      const now = new Date();
      let startDate, endDate;

      switch (selectedPeriod) {
        case "today":
          startDate = new Date();
          startDate.setHours(0, 0, 0, 0);
          endDate = new Date(); // current time
          break;

        case "yesterday":
          startDate = new Date();
          startDate.setDate(startDate.getDate() - 1);
          startDate.setHours(0, 0, 0, 0);
          endDate = new Date(startDate);
          endDate.setHours(23, 59, 59, 999);
          break;

        case "last24h":
          endDate = new Date();
          startDate = new Date(endDate.getTime() - 24 * 60 * 60 * 1000);
          break;

        case "last7d":
          endDate = new Date();
          startDate = new Date(
            Date.UTC(
              endDate.getUTCFullYear(),
              endDate.getUTCMonth(),
              endDate.getUTCDate() - 6,
              0,
              0,
              0,
              0
            )
          );
          break;

        case "last30d":
          endDate = new Date();
          startDate = new Date(
            Date.UTC(
              endDate.getUTCFullYear(),
              endDate.getUTCMonth(),
              endDate.getUTCDate() - 29,
              0,
              0,
              0,
              0
            )
          );
          break;

        case "wtd":
          // Week to date (starting Monday) in local time
          const currentDate = new Date();
          // Get the current day in local time (0 = Sunday, 1 = Monday, ..., 6 = Saturday)
          const currentDay = currentDate.getDay();

          // Calculate days to subtract to get to the most recent Monday
          // If today is Sunday (0), go back 6 days to the previous Monday
          // Otherwise go back to the most recent Monday (current day - 1)
          const daysToSubtract = currentDay === 0 ? 6 : currentDay - 1;

          // Create start date at the beginning of the most recent Monday in LOCAL time
          startDate = new Date();
          startDate.setDate(startDate.getDate() - daysToSubtract);
          startDate.setHours(0, 0, 0, 0);

          // Set end date to current time in LOCAL time
          endDate = new Date();

          break;

        case "mtd":
          startDate = new Date(
            Date.UTC(now.getUTCFullYear(), now.getUTCMonth(), 1, 0, 0, 0, 0)
          );
          endDate = new Date();
          break;

        case "ytd":
          startDate = new Date(
            Date.UTC(now.getUTCFullYear(), 0, 1, 0, 0, 0, 0)
          );
          endDate = new Date();
          break;

        case "all":
          startDate = new Date(Date.UTC(2000, 0, 1, 0, 0, 0, 0));
          endDate = new Date();
          break;

        case "custom":
          if (customPeriod) {
            // Create dates in local time
            startDate = new Date(customPeriod.startDate);
            startDate.setHours(0, 0, 0, 0);

            // For the end date, set it to the end of the day (23:59:59.999) in local time
            endDate = new Date(customPeriod.endDate);
            // Ensure we're working with the correct day by resetting hours first
            endDate.setHours(0, 0, 0, 0);
            // Then set to end of day
            endDate.setHours(23, 59, 59, 999);
          } else {
            startDate = new Date();
            startDate.setHours(0, 0, 0, 0);
            endDate = new Date();
          }
          break;

        default:
          // Default to last 24 hours if period is unknown
          endDate = new Date();
          startDate = new Date(endDate.getTime() - 24 * 60 * 60 * 1000);
      }

      // Format dates for API
      const formattedStartDate = startDate.toISOString();
      const formattedEndDate = endDate.toISOString();

      // Fetch all events in the time range - we'll filter for custom events client-side
      const url = `/api/events?websiteId=${websiteId}&startDate=${encodeURIComponent(
        formattedStartDate
      )}&endDate=${encodeURIComponent(formattedEndDate)}`;

      const response = await fetch(url);
      if (!response.ok) {
        const errorText = await response.text();
        console.error("Journey API Error:", {
          status: response.status,
          statusText: response.statusText,
          body: errorText,
        });
        throw new Error("Failed to fetch journeys");
      }

      let events = await response.json();

      // Filter for custom events with matching name
      events = events.filter((event: any) => {
        try {
          // Ensure event is an object
          if (!event || typeof event !== "object") {
            return false;
          }

          // Check type with full null safety
          let isCustomType = false;
          try {
            const type = String(event.type || "");
            isCustomType = type === "custom_update" || type === "signup"; // Include both custom_update and signup types
          } catch (e) {
            console.warn("Error checking event type:", e);
            return false;
          }

          // Check event name with full null safety
          let matchesEventName = false;
          try {
            matchesEventName =
              event.extraData &&
              typeof event.extraData === "object" &&
              event.extraData.eventName === eventName;
          } catch (e) {
            console.warn("Error checking event name:", e);
            return false;
          }

          // Check timestamp with full null safety
          let isInTimeRange = false;
          try {
            const eventTime = event.timestamp
              ? new Date(event.timestamp)
              : null;
            isInTimeRange = Boolean(
              eventTime && eventTime >= startDate && eventTime <= endDate
            );
          } catch (e) {
            console.warn("Error checking timestamp:", e);
            return false;
          }

          return isCustomType && matchesEventName && isInTimeRange;
        } catch (e) {
          console.warn("Error filtering event:", e);
          return false;
        }
      });

      // Ensure events is always an array
      events = Array.isArray(events) ? events : [];

      // Apply all active filters to be consistent with the main event filtering
      events = filterEvents(events);

      // Apply hour filtering if present with null safety
      if (hourFilter && hourFilter.value) {
        events = events.filter((event: Event) => {
          try {
            if (!event.timestamp) return false;
            return (
              filterEventsByHour([event], hourFilter.value as string).length > 0
            );
          } catch (e) {
            console.warn("Error in hour filtering:", e);
            return false;
          }
        });
      }

      // Sort events by timestamp with null safety
      events.sort((a: any, b: any) => {
        try {
          const timeA = a?.timestamp ? new Date(a.timestamp).getTime() : 0;
          const timeB = b?.timestamp ? new Date(b.timestamp).getTime() : 0;
          return timeA - timeB;
        } catch (e) {
          console.warn("Error sorting events:", e);
          return 0;
        }
      });

      // Format each event as its own journey item - don't group by visitor ID
      const formattedJourneys = events.map((event: Event) => {
        try {
          return {
            visitorId: event?.visitorId || "unknown",
            timestamp: event?.timestamp || new Date().toISOString(),
            location: event?.location || undefined,
            referrer: event?.referrer || null,
            href: event?.href || "",
            eventName: event?.extraData?.eventName || eventName,
            eventData:
              event?.extraData && typeof event.extraData === "object"
                ? event.extraData
                : {},
          };
        } catch (e) {
          console.warn("Error formatting journey:", e);
          // Return a safe default object if formatting fails
          return {
            visitorId: "unknown",
            timestamp: new Date().toISOString(),
            location: undefined,
            referrer: null,
            href: "",
            eventName: eventName,
            eventData: {},
          };
        }
      });

      setSelectedEventJourneys({
        eventName,
        journeys: formattedJourneys,
      });
    } catch (error) {
      console.error("Error fetching journeys:", error);
      setSelectedEventJourneys(null);
    } finally {
      setIsLoadingEvents(false);
    }
  };

  return {
    customEvents,
    isLoadingEvents,
    selectedEventJourneys,
    setSelectedEventJourneys,
    handleShowJourneyList,
  };
}
