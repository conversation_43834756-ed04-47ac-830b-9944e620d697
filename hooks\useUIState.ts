import { useState, useEffect, useRef } from "react";
import { Period, CustomPeriod } from "@/lib/types";

export function useUIState() {
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [isPeriodDropdownOpen, setIsPeriodDropdownOpen] = useState(false);
  const [selectedPeriod, setSelectedPeriod] = useState<Period>("today");
  const [customPeriod, setCustomPeriod] = useState<CustomPeriod | undefined>();
  const [xAxisInterval, setXAxisInterval] = useState(3);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const periodDropdownRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const previousPeriod = localStorage.getItem("selected_period");
    setSelectedPeriod(previousPeriod as Period);
  }, []);
  // <PERSON>le clicks outside dropdowns
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsDropdownOpen(false);
      }
      if (
        periodDropdownRef.current &&
        !periodDropdownRef.current.contains(event.target as Node)
      ) {
        setIsPeriodDropdownOpen(false);
      }
    }
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  // Get X-axis interval based on period and screen size
  const getXAxisInterval = () => {
    if (window.innerWidth < 640) {
      switch (selectedPeriod) {
        case "today":
        case "yesterday":
        case "last24h":
          return 6;
        case "last7d":
        case "wtd":
          return 2;
        case "last30d":
          return 15;
        case "mtd":
          return 10;
        case "ytd":
          return 2;
        case "all":
          return 30;
        case "custom":
          return 2;
        default:
          return 3;
      }
    }

    switch (selectedPeriod) {
      case "today":
      case "yesterday":
      case "last24h":
        return 3;
      case "last7d":
        return 3;
      case "wtd":
      case "last30d":
      case "mtd":
      case "ytd":
      case "all":
        return 10;
      case "custom":
        return 1;
      default:
        return 1;
    }
  };

  // Update interval based on screen size and period
  useEffect(() => {
    function handleResize() {
      setXAxisInterval(getXAxisInterval());
    }

    handleResize(); // Set initial value
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, [selectedPeriod]);

  return {
    isDropdownOpen,
    setIsDropdownOpen,
    isPeriodDropdownOpen,
    setIsPeriodDropdownOpen,
    selectedPeriod,
    setSelectedPeriod,
    customPeriod,
    setCustomPeriod,
    xAxisInterval,
    dropdownRef,
    periodDropdownRef,
  };
}
