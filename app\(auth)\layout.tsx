import type { Metadata } from "next";
import LogoComponent from "@/components/LogoComponent";
import { <PERSON><PERSON><PERSON><PERSON> } from "@clerk/nextjs";

export const metadata: Metadata = {
    title: "Auth | VersaTailor",
    description: "VersaTailor",
};

export default function AuthLayout({
    children,
}: Readonly<{
    children: React.ReactNode;
}>) {
    return (
        <ClerkProvider>
            <div className="min-h-screen flex flex-col bg-gradient-to-br from-gray-900 via-black to-gray-900">
                {/* Header */}
                <header className="w-full py-6 px-4 sm:px-6 lg:px-8">
                    <div className="container mx-auto">
                        <LogoComponent />
                    </div>
                </header>

                {/* Main Content */}
                <main className="flex-1 flex items-center justify-center p-4 sm:p-6 lg:p-8">
                    <div className="w-full max-w-md">
                        <div className="bg-gray-900/50 backdrop-blur-sm rounded-2xl border border-gray-800 shadow-xl p-6 sm:p-8 animate-fade-in">
                            {children}
                        </div>
                        <p className="text-gray-500 text-sm mt-4">
                            By signing up, you agree to our{" "}
                            <a
                                href={`${process.env.NEXT_PUBLIC_APP_URL}/tos`}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="text-blue-500 hover:underline"
                            >
                                Terms of Service
                            </a>
                        </p>
                    </div>
                </main>

                {/* Footer */}
                <footer className="py-6 px-4 sm:px-6 lg:px-8">
                    <div className="container mx-auto text-center text-gray-500 text-sm">
                        &copy; {new Date().getFullYear()} VersaTailor. All rights reserved.
                    </div>
                </footer>
            </div>
        </ClerkProvider>
    );
}
