import React from 'react';
import { Filter } from '@/lib/types';

interface FilterIndicatorProps {
  activeFilters: Filter[];
  onRemoveFilter: (index: number) => void;
  onClearAll: () => void;
}

export default function FilterIndicator({ 
  activeFilters, 
  onRemoveFilter, 
  onClearAll 
}: FilterIndicatorProps) {
  if (activeFilters.length === 0) return null;

  return (
    <div className="flex flex-wrap gap-2 mb-4">
      {activeFilters.map((filter, index) => (
        <div
          key={index}
          className="inline-flex items-center gap-2 px-3 py-1.5 bg-blue-500/20 text-blue-400 rounded-lg"
        >
          <span>{filter.label}</span>
          <button
            onClick={() => onRemoveFilter(index)}
            className="hover:text-white"
          >
            <svg
              className="w-4 h-4"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </button>
        </div>
      ))}
      {activeFilters.length > 1 && (
        <button
          onClick={onClearAll}
          className="inline-flex items-center gap-2 px-3 py-1.5 text-blue-400 hover:text-blue-300 rounded-lg"
        >
          Clear all
        </button>
      )}
    </div>
  );
} 