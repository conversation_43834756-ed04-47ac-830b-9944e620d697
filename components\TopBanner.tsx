"use client";
import React from "react";
import { useUser } from "@clerk/nextjs";
import { useEffect, useState } from "react";
import { FiAlertCircle } from "react-icons/fi";
import { useRouter } from "next/navigation";

function TopBanner() {
  const { user } = useUser();
  const [daysLeft, setDaysLeft] = useState<number | null>(null);
  const [hasPlan, setHasPlan] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(true); // Add isLoading state

  useEffect(() => {
    async function fetchUserData() {
      try {
        const response = await fetch("/api/user");
        const data = await response.json();

        if (data.accessUntil) {
          const accessUntil = new Date(data.accessUntil);
          const now = new Date();
          const diffTime = accessUntil.getTime() - now.getTime();
          const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
          setDaysLeft(Math.max(0, diffDays));
        }

        // Set hasPlan based on whether priceId exists
        setHasPlan(!!data.priceId);
      } catch (error) {
        console.error("Error fetching user data:", error);
      } finally {
        setIsLoading(false); // Set loading to false after fetch attempt
      }
    }

    if (user) {
      fetchUserData();
    }
  }, [user]);
  const router = useRouter();

  // Redirect only if loading is complete and access is expired (daysLeft is 0)
  if (!isLoading && daysLeft === 0 ) {
    if (process.env.NODE_ENV !== "development") {
      router.push("/dashboard/frozen");
    }
    // Return null to prevent rendering the banner content while redirecting or if access is expired
    return null;
  }

  // Don't show banner if loading or access is expired
  if (isLoading || daysLeft === 0 || daysLeft! > 14 || process.env.NODE_ENV === "development") {
    return null;
  }

  // Show banner only if loading is complete and daysLeft > 0
  return (
    <div className="bg-[#363636] border-b border-gray-700/50 text-white py-2.5 px-6">
      <p className="text-sm md:text-base text-center flex flex-wrap items-center justify-center gap-1.5">
        <FiAlertCircle className="text-blue-400 w-4 h-4" /> 
        <span className="font-medium">{daysLeft} {daysLeft === 1 ? "day" : "days"}</span>
        <span className="text-gray-300">left in your trial period.</span>
        <a 
          href="/billing" 
          className="inline-flex items-center font-medium text-blue-400 hover:text-blue-300 transition-colors"
        >
          Secure the early bird price
        </a>
        <span className="text-gray-300">now for $0.</span>
      </p>
    </div>
  );
}

export default TopBanner;
