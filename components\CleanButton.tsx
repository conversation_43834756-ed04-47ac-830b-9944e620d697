"use client";
import React from "react";

function CleanButton() {
  if (process.env.NODE_ENV !== "development") {
    return null;
  }
  return (
    <button
      onClick={() => fetch("/api/clean", { method: "POST" })}
      
      className="px-4 py-2 bg-gradient-to-r bg-transparent rounded-lg text-white font-semibold hover:opacity-90 transition-opacity"
    >
      Clean
    </button>
  );
}

export default CleanButton;
