import { useUser } from "@clerk/nextjs";
import Link from "next/link";

const customerPortalLink =
    "https://billing.stripe.com/p/login/fZe4gxcjP40D6Mo000";

function ButtonCustomerPortal() {
    const { user } = useUser();

    const primaryEmail = user?.primaryEmailAddress?.emailAddress;

    if (user) {
        return (
            <Link
                href={customerPortalLink + "?prefilled_email=" + primaryEmail}
                target="_blank"
                className="px-6 py-3 bg-gradient-to-r from-blue-500 to-blue-700 rounded-lg text-white font-semibold text-center hover:opacity-90 transition-opacity"
            >
                Manage Subscription
            </Link>
        );
    }
}
export default ButtonCustomerPortal;
