import { Metadata } from 'next';
import { getMetadata } from '@/lib/docs-metadata';

export async function generateMetadata(): Promise<Metadata> {
  const metadata = getMetadata('/docs/filter-settings');
  return {
    title: metadata.title,
    description: metadata.description,
    keywords: metadata.keywords?.join(', '),
    openGraph: {
      title: metadata.title,
      description: metadata.description,
      siteName: metadata.siteName,
      url: `${metadata.baseUrl}/docs/filter-settings`,
      type: 'website',
    },
    twitter: {
      card: 'summary_large_image',
      title: metadata.title,
      description: metadata.description,
      creator: metadata.twitterHandle,
    },
  };
}

export default function FilterSettingsLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return <>{children}</>;
}