import { Metadata } from 'next';
import { getMetadata } from '@/lib/docs-metadata';
import Link from "next/link";
import CodeBlock from "@/components/CodeBlock";

export const metadata: Metadata = getMetadata('/docs/import-your-data');

export default function ImportYourDataPage() {
  return (
    <div className="max-w-3xl mx-auto py-8 px-4">
      <h1 className="text-3xl font-bold mb-8">Import your data</h1>

      <div className="space-y-6">
        <p className="text-lg">
          Already using Plausible Analytics? You can easily import your historical data to Versatailor
          to maintain continuity in your analytics and get the full picture of your website performance.
        </p>

        <section className="space-y-4">
          <h2 className="text-2xl font-semibold">1) Retrieve your data from Plausible</h2>
          <div className="space-y-2">
            <p>To extract your analytics information from Plausible:</p>
            <ol className="list-decimal pl-6 space-y-2">
              <li>Access your Plausible Analytics dashboard</li>
              <li>Navigate to the configuration area for your specific website</li>
              <li>Find the &quot;Imports & Exports&quot; area on the page</li>
              <li>Select the &quot;Export to CSV&quot; option from the data export section</li>
              <li>Allow some processing time - you&apos;ll receive an email notification once ready</li>
              <li>Use the link in your email to download your analytics data (note: links expire after 24 hours)</li>
            </ol>
          </div>

          <h2 className="text-2xl font-semibold mt-8">2) Import to Versatailor</h2>
          <div className="space-y-3">
            <p>
              Once you have your Plausible data exported, follow these steps to import it to Versatailor:
            </p>
            <ol className="list-decimal pl-6 space-y-3">
              <li>Navigate to your website settings in the Versatailor dashboard</li>
              <li>Select the &quot;Import&quot; tab from the settings menu</li>
              <li>Find the Plausible Analytics section and click the &quot;Import&quot; button</li>
              <li>In the import modal, upload your exported Plausible ZIP file</li>
              <li>Wait for the upload and processing to complete - a progress bar will show the current status</li>
              <li>Once complete, you&apos;ll see a success message confirming the data import</li>
            </ol>
            <div className="bg-blue-500/10 border border-blue-500/20 p-4 rounded-lg">
              <p className="text-blue-300/90">
                Versatailor accepts ZIP files containing CSV data exported from Plausible Analytics.
                Make sure your export is in the correct format to ensure successful data migration.
              </p>
            </div>
          </div>

          <h2 className="text-2xl font-semibold mt-8">3) Verify your imported data</h2>
          <p>
            After importing, check your Versatailor dashboard to confirm your historical data appears correctly.
            The import process may take a few minutes depending on the amount of data.
          </p>
          <p>
            You should see your imported metrics alongside any new data being collected by Versatailor,
            giving you a seamless analytics experience.
          </p>

          <h2 className="text-2xl font-semibold mt-8">4) Managing imported data</h2>
          <p>
            If you need to remove previously imported Plausible data, you can do so from the Import settings:
          </p>
          <ol className="list-decimal pl-6 space-y-2">
            <li>Navigate to your website settings in Versatailor</li>
            <li>Go to the &quot;Import&quot; tab</li>
            <li>Scroll to the &quot;Remove Imported Data&quot; section</li>
            <li>Click the &quot;Remove Plausible Data&quot; button</li>
            <li>Confirm the removal when prompted</li>
          </ol>
          <div className="bg-red-500/10 border border-red-500/20 p-4 rounded-lg mt-4">
            <p className="text-red-300/90">
              Note: Removing imported data is permanent and cannot be undone.
            </p>
          </div>
        </section>
      </div>
    </div>
  );
}
