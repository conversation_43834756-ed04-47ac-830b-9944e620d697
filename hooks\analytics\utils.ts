import { Event } from './types';

export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

export function createDataWindow<T>(data: T[], windowSize: number = 100): T[] {
  if (data.length <= windowSize) return data;
  const skip = Math.floor(data.length / windowSize);
  return data.filter((_, index) => index % skip === 0).slice(0, windowSize);
}

export function calculateBounceRate(events: Event[]) {
  if (!events.length) return 0;
  const sessions = new Map<string, { pages: Set<string>; events: Event[] }>();

  events.forEach((event) => {
    const sessionId = event.sessionId;
    if (!sessions.has(sessionId)) {
      sessions.set(sessionId, { pages: new Set(), events: [] });
    }
    const sessionData = sessions.get(sessionId);
    if (sessionData) {
      // Safely extract pathname from href
      let pathname: string;
      try {
        // Try to create URL object
        const url = new URL(event.href);
        pathname = url.pathname;
      } catch (error) {
        // If href is not a valid URL (e.g., relative path), use it as-is
        // Remove query params and hash if present
        pathname = event.href.split('?')[0].split('#')[0];
      }
      
      sessionData.pages.add(pathname);
      sessionData.events.push(event);
    }
  });

  let bounceCount = 0;
  const ENGAGEMENT_THRESHOLD_MS = 30 * 1000; // 30 seconds

  sessions.forEach(({ pages, events }) => {
    // Consider single-page visits
    if (pages.size === 1) {
      // Check if they spent significant time on the page
      if (events.length >= 2) {
        events.sort((a, b) => 
          new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
        );
        
        const sessionDuration = 
          new Date(events[events.length - 1].timestamp).getTime() - 
          new Date(events[0].timestamp).getTime();
        
        // Only count as bounce if duration is less than threshold
        if (sessionDuration < ENGAGEMENT_THRESHOLD_MS) {
          bounceCount++;
        }
      } else {
        // Single event in session - definitely a bounce
        bounceCount++;
      }
    }
  });

  return Math.round((bounceCount / sessions.size) * 100);
}

export function calculateAvgVisitTime(events: Event[]) {
  if (!events.length) return 0;

  const sessions = new Map<string, Event[]>();
  const CONTINUOUS_ACTIVITY_THRESHOLD_MS = 1 * 60 * 1000;

  events.forEach((event) => {
    if (!sessions.has(event.sessionId)) {
      sessions.set(event.sessionId, []);
    }
    sessions.get(event.sessionId)?.push(event);
  });

  let totalActiveDuration = 0;
  let validSessionCount = 0;

  sessions.forEach((sessionEvents) => {
    if (sessionEvents.length < 2) return;

    sessionEvents.sort(
      (a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
    );

    let sessionActiveDuration = 0;
    for (let i = 0; i < sessionEvents.length - 1; i++) {
      const timeDiff =
        new Date(sessionEvents[i + 1].timestamp).getTime() -
        new Date(sessionEvents[i].timestamp).getTime();

      if (timeDiff < CONTINUOUS_ACTIVITY_THRESHOLD_MS) {
        sessionActiveDuration += timeDiff;
      }
    }

    if (sessionActiveDuration > 0) {
      totalActiveDuration += sessionActiveDuration;
      validSessionCount++;
    }
  });

  return validSessionCount === 0 ? 0 : Math.round(totalActiveDuration / (validSessionCount * 1000));
}

export function normalizeDate(date: Date, selectedPeriod: string): string {
  if (!["today", "yesterday", "last24h"].includes(selectedPeriod)) {
    return new Date(
      date.getFullYear(),
      date.getMonth(),
      date.getDate()
    ).toISOString();
  }
  return date.toISOString();
}

export function generateCacheKey(
  websiteId: string,
  period: string,
  fromDate: Date,
  toDate: Date,
  customPeriod?: { startDate: string; endDate: string },
  selectedPeriod?: string
): string {
  const normalizedFrom = normalizeDate(fromDate, selectedPeriod || period);
  const normalizedTo = normalizeDate(toDate, selectedPeriod || period);

  return `website_${websiteId}_${normalizedFrom}_${normalizedTo}_${period}${
    customPeriod
      ? `_custom_${normalizeDate(
          new Date(customPeriod.startDate),
          selectedPeriod || period
        )}_${normalizeDate(
          new Date(customPeriod.endDate),
          selectedPeriod || period
        )}`
      : ""
  }`;
}

export function areCacheKeysEquivalent(key1: string, key2: string): boolean {
  const getWebsiteId = (key: string) => {
    const parts = key.split("_");
    if (parts.length < 2 || parts[0] !== "website") return null;
    return parts[1];
  };

  const website1 = getWebsiteId(key1);
  const website2 = getWebsiteId(key2);

  if (!website1 || !website2 || website1 !== website2) {
    return false;
  }

  const extractDates = (key: string) => {
    const parts = key.split("_");
    if (parts.length < 4) return null;

    try {
      return {
        from: new Date(parts[2]),
        to: new Date(parts[3]),
      };
    } catch (e) {
      return null;
    }
  };

  const dates1 = extractDates(key1);
  const dates2 = extractDates(key2);

  if (!dates1 || !dates2) return false;

  const range1 = dates1.to.getTime() - dates1.from.getTime();
  const range2 = dates2.to.getTime() - dates2.from.getTime();

  const overlapStart = Math.max(dates1.from.getTime(), dates2.from.getTime());
  const overlapEnd = Math.min(dates1.to.getTime(), dates2.to.getTime());

  if (overlapEnd <= overlapStart) return false;

  const overlapDuration = overlapEnd - overlapStart;
  const shorterRange = Math.min(range1, range2);

  return overlapDuration / shorterRange > 0.8;
} 