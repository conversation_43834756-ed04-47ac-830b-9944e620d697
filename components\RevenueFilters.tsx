"use client";
import React, { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import type { RevenueFilters } from "@/hooks/useRevenueAnalytics";

interface Props {
  filters: RevenueFilters;
  onFiltersChange: (filters: RevenueFilters) => void;
  onApplyFilters: () => void;
  onClearFilters: () => void;
}

function RevenueFilters({ filters, onFiltersChange, onApplyFilters, onClearFilters }: Props) {
  const [isExpanded, setIsExpanded] = useState(false);

  const updateFilter = (key: keyof RevenueFilters, value: any) => {
    onFiltersChange({
      ...filters,
      [key]: value,
    });
  };

  const updateAmountRange = (type: "min" | "max", value: string) => {
    const numValue = value === "" ? null : parseFloat(value);
    updateFilter("amountRange", {
      ...filters.amountRange,
      [type]: numValue,
    });
  };

  const hasActiveFilters = () => {
    return (
      filters.amountRange.min !== null ||
      filters.amountRange.max !== null ||
      filters.customerType !== "all" ||
      filters.paymentMethod !== "all" ||
      filters.paymentType !== "all" ||
      filters.currency !== "all" ||
      filters.dateRange.start !== null ||
      filters.dateRange.end !== null
    );
  };

  return (
    <div className="bg-gray-800/50 rounded-lg border border-gray-700/50 p-4">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-medium flex items-center gap-2">
          <svg
            className="w-5 h-5 text-blue-500"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"
            />
          </svg>
          Revenue Filters
          {hasActiveFilters() && (
            <span className="bg-blue-500 text-white text-xs px-2 py-1 rounded-full">
              Active
            </span>
          )}
        </h3>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setIsExpanded(!isExpanded)}
          className="text-gray-400 hover:text-white"
        >
          {isExpanded ? "Collapse" : "Expand"}
        </Button>
      </div>

      {isExpanded && (
        <div className="space-y-6">
          {/* Amount Range Filter */}
          <div className="space-y-3">
            <Label className="text-sm font-medium">Revenue Amount Range</Label>
            <div className="grid grid-cols-2 gap-3">
              <div>
                <Label htmlFor="min-amount" className="text-xs text-gray-400">
                  Minimum ($)
                </Label>
                <Input
                  id="min-amount"
                  type="number"
                  placeholder="0"
                  value={filters.amountRange.min || ""}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) => updateAmountRange("min", e.target.value)}
                  className="bg-gray-700 border-gray-600 text-sm"
                />
              </div>
              <div>
                <Label htmlFor="max-amount" className="text-xs text-gray-400">
                  Maximum ($)
                </Label>
                <Input
                  id="max-amount"
                  type="number"
                  placeholder="No limit"
                  value={filters.amountRange.max || ""}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) => updateAmountRange("max", e.target.value)}
                  className="bg-gray-700 border-gray-600 text-sm"
                />
              </div>
            </div>
          </div>

          {/* Customer Type Filter */}
          <div className="space-y-3">
            <Label className="text-sm font-medium">Customer Type</Label>
            <div className="flex gap-2">
              {[
                { value: "all", label: "All Customers" },
                { value: "new", label: "New Customers" },
                { value: "returning", label: "Returning Customers" },
              ].map((option) => (
                <Button
                  key={option.value}
                  variant={filters.customerType === option.value ? "default" : "outline"}
                  size="sm"
                  onClick={() => updateFilter("customerType", option.value)}
                  className={`text-xs ${
                    filters.customerType === option.value
                      ? "bg-blue-600 hover:bg-blue-700"
                      : "border-gray-600 hover:bg-gray-700"
                  }`}
                >
                  {option.label}
                </Button>
              ))}
            </div>
          </div>

          {/* Payment Type Filter */}
          <div className="space-y-3">
            <Label className="text-sm font-medium">Payment Type</Label>
            <div className="flex gap-2 flex-wrap">
              {[
                { value: "all", label: "All Types" },
                { value: "one_time", label: "One-time" },
                { value: "subscription", label: "Subscription" },
                { value: "renewal", label: "Renewal" },
              ].map((option) => (
                <Button
                  key={option.value}
                  variant={filters.paymentType === option.value ? "default" : "outline"}
                  size="sm"
                  onClick={() => updateFilter("paymentType", option.value)}
                  className={`text-xs ${
                    filters.paymentType === option.value
                      ? "bg-blue-600 hover:bg-blue-700"
                      : "border-gray-600 hover:bg-gray-700"
                  }`}
                >
                  {option.label}
                </Button>
              ))}
            </div>
          </div>

          {/* Currency Filter */}
          <div className="space-y-3">
            <Label htmlFor="currency-filter" className="text-sm font-medium">
              Currency
            </Label>
            <select
              id="currency-filter"
              value={filters.currency}
              onChange={(e: React.ChangeEvent<HTMLSelectElement>) => updateFilter("currency", e.target.value)}
              className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-sm"
            >
              <option value="all">All Currencies</option>
              <option value="usd">USD ($)</option>
              <option value="eur">EUR (€)</option>
              <option value="gbp">GBP (£)</option>
              <option value="cad">CAD (C$)</option>
              <option value="aud">AUD (A$)</option>
              <option value="jpy">JPY (¥)</option>
            </select>
          </div>

          {/* Date Range Filter */}
          <div className="space-y-3">
            <Label className="text-sm font-medium">Date Range</Label>
            <div className="grid grid-cols-2 gap-3">
              <div>
                <Label htmlFor="start-date" className="text-xs text-gray-400">
                  Start Date
                </Label>
                <Input
                  id="start-date"
                  type="date"
                  value={filters.dateRange.start?.toISOString().split("T")[0] || ""}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                    updateFilter("dateRange", {
                      ...filters.dateRange,
                      start: e.target.value ? new Date(e.target.value) : null,
                    })
                  }
                  className="bg-gray-700 border-gray-600 text-sm"
                />
              </div>
              <div>
                <Label htmlFor="end-date" className="text-xs text-gray-400">
                  End Date
                </Label>
                <Input
                  id="end-date"
                  type="date"
                  value={filters.dateRange.end?.toISOString().split("T")[0] || ""}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                    updateFilter("dateRange", {
                      ...filters.dateRange,
                      end: e.target.value ? new Date(e.target.value) : null,
                    })
                  }
                  className="bg-gray-700 border-gray-600 text-sm"
                />
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-3 pt-4 border-t border-gray-700">
            <Button
              onClick={onApplyFilters}
              className="bg-blue-600 hover:bg-blue-700 text-white"
              size="sm"
            >
              Apply Filters
            </Button>
            <Button
              onClick={onClearFilters}
              variant="outline"
              size="sm"
              className="border-gray-600 hover:bg-gray-700"
            >
              Clear All
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}

export default RevenueFilters; 