import { format, subHours, isAfter } from "date-fns";
import {
  Event,
  TopReferrer,
  ChartDataItem,
  PageView,
  OperatingSystem,
  Location,
} from "./types";

// Get unique visitors count
export function getUniqueVisitors(events: Event[]): number {
  return new Set(
    events
      .filter((event) => event.location?.city && event.location?.country)
      .map((event) => event.visitorId)
  ).size;
}

// Get current visitors (active in last 5 minutes)
export function getCurrentVisitors(events: Event[]): number {
  const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);
  return new Set(
    events
      .filter((event) => event.location?.city && event.location?.country)
      .filter((event) => isAfter(new Date(event.timestamp), fiveMinutesAgo))
      .map((event) => event.visitorId)
  ).size;
}

// Calculate bounce rate (percentage of single-page sessions)
export function getBounceRate(events: Event[]): number {
  // Filter out events without location data first
  const eventsWithLocation = events.filter(
    (event) => event.location?.city && event.location?.country
  );

  // Group events by session
  const sessions = eventsWithLocation.reduce(
    (acc, event) => {
      if (!acc[event.sessionId]) {
        acc[event.sessionId] = {
          pages: new Set<string>(),
          timestamps: [],
          events: 0,
          hasInteraction: false,
        };
      }
      acc[event.sessionId].pages.add(event.href);
      acc[event.sessionId].timestamps.push(new Date(event.timestamp).getTime());
      acc[event.sessionId].events++;

      // Check if this is an interaction event (you'll need to define what counts as interaction)
      if (
        event.type === "click" ||
        event.type === "form_submit" ||
        event.type === "scroll"
      ) {
        acc[event.sessionId].hasInteraction = true;
      }

      return acc;
    },
    {} as Record<
      string,
      {
        pages: Set<string>;
        timestamps: number[];
        events: number;
        hasInteraction: boolean;
      }
    >
  );

  const totalSessions = Object.keys(sessions).length;
  if (totalSessions === 0) return 0;

  // A session is considered a bounce if:
  // 1. Only one page was viewed AND
  // 2. No interaction events AND
  // 3. Session duration is less than 30 seconds
  const bouncedSessions = Object.values(sessions).filter((session) => {
    const timestamps = [...session.timestamps].sort((a, b) => a - b);
    const sessionDuration =
      timestamps.length > 1
        ? (timestamps[timestamps.length - 1] - timestamps[0]) / 1000
        : 0;

    return (
      session.pages.size === 1 &&
      !session.hasInteraction &&
      sessionDuration < 30
    );
  }).length;

  return Math.round((bouncedSessions / totalSessions) * 100);
}

// Calculate average session time in seconds
export function getAverageSessionTime(events: Event[]): number {
  // Filter out events without location data first
  const eventsWithLocation = events.filter(
    (event) => event.location?.city && event.location?.country
  );

  const sessions = eventsWithLocation.reduce((acc, event) => {
    if (!acc[event.sessionId]) {
      acc[event.sessionId] = {
        timestamps: [new Date(event.timestamp).getTime()],
      };
    } else {
      acc[event.sessionId].timestamps.push(new Date(event.timestamp).getTime());
    }
    return acc;
  }, {} as Record<string, { timestamps: number[] }>);

  // Calculate total duration for each session
  const sessionDurations = Object.values(sessions).map((session) => {
    const timestamps = [...session.timestamps].sort((a, b) => a - b);
    if (timestamps.length < 2) {
      // For single-event sessions, use a small default value (e.g., 10 seconds)
      // This is similar to how GA4 handles bounces
      return 10;
    }

    // Calculate total session duration in seconds
    const duration = (timestamps[timestamps.length - 1] - timestamps[0]) / 1000;

    // Cap extremely long sessions (e.g., 30 minutes)
    return Math.min(duration, 1800);
  });

  if (sessionDurations.length === 0) return 0;

  // Calculate overall average across all sessions (including bounces)
  return Math.round(
    sessionDurations.reduce((sum, duration) => sum + duration, 0) /
      sessionDurations.length
  );
}

// Get hourly visitors data for chart
export function getHourlyVisitors(events: Event[]): ChartDataItem[] {
  const now = new Date();
  const currentHour = now.getHours();
  const hourlyData: Record<string, Set<string> | null> = {};

  // Initialize all 24 hours with empty Sets for unique visitor tracking
  for (let i = 0; i < 24; i++) {
    const hour = format(new Date().setHours(i, 0, 0, 0), "ha");
    // Only track visitors for hours up to current hour
    hourlyData[hour] = i <= currentHour ? new Set() : null;
  }

  // Track unique visitors per hour for today only
  const todayStart = new Date().setHours(0, 0, 0, 0);
  events
    .filter((event) => event.location?.city && event.location?.country)
    .filter((event) => new Date(event.timestamp).getTime() >= todayStart)
    .forEach((event) => {
      const hour = format(new Date(event.timestamp), "ha");
      const visitors = hourlyData[hour];
      if (visitors !== null) {
        // Only count if hour is not null (i.e., up to current hour)
        visitors.add(event.visitorId); // Add visitor ID to Set to ensure uniqueness
      }
    });

  // Convert to chart data format and add pulse to current hour
  const currentHourStr = format(now, "ha");
  const chartData = Object.entries(hourlyData).map(([time, visitors]) => ({
    time,
    visitors: visitors?.size ?? null, // Get count of unique visitors from Set
    pulse:
      time === currentHourStr && visitors !== null && visitors.size > 0
        ? visitors.size
        : null,
  }));

  return chartData;
}

// Get top pages with unique visitors
export function getTopPages(events: Event[]): PageView[] {
  const pages = events.reduce((acc, event) => {
    // Skip events without location data
    if (!event.location?.city || !event.location?.country) return acc;

    // Safely extract pathname from href
    let path: string;
    try {
      // Try to create URL object
      const url = new URL(event.href);
      path = url.pathname;
    } catch (error) {
      // If href is not a valid URL (e.g., relative path), use it as-is
      // Remove query params and hash if present
      path = event.href.split('?')[0].split('#')[0];
    }
    
    if (!acc[path]) {
      acc[path] = new Set();
    }
    acc[path].add(event.visitorId);
    return acc;
  }, {} as Record<string, Set<string>>);

  return Object.entries(pages)
    .map(([path, visitors]) => ({ path, visitors: visitors.size }))
    .sort((a, b) => b.visitors - a.visitors);
}

// Get top referrers with unique visitors
export function getTopReferrers(events: Event[]): TopReferrer[] {
  // Log total unique visitors for comparison
  const totalUniqueVisitors = new Set(
    events
      .filter((event) => event.location?.city && event.location?.country)
      .map((event) => event.visitorId)
  ).size;

  // First, sort events by visitorId and timestamp to ensure proper ordering
  const sortedEvents = [...events].sort((a, b) => {
    // First sort by visitorId
    if (a.visitorId !== b.visitorId) {
      return a.visitorId.localeCompare(b.visitorId);
    }
    // Then sort by timestamp
    return new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime();
  });

  // Track visitors that are filtered out
  const filteredOutVisitors = new Set();
  const visitorReferrerMapping = new Map();

  // Group events by visitorId to track previous referrers
  const visitorReferrers: Record<string, string | null> = {};

  const referrers = sortedEvents.reduce((acc, event) => {
    // Skip events that don't have location data
    if (!event.location?.city || !event.location?.country) {
      filteredOutVisitors.add(`${event.visitorId} (no location)`);
      return acc;
    }

    // Extract ref from URL if present
    let urlWithParams;
    try {
      urlWithParams = new URL(event.href);
    } catch (e) {
      urlWithParams = null;
    }

    const ref = urlWithParams?.searchParams.get("ref");
    let referrer = null;
    
    // Properly handle referrer URL
    if (event.referrer) {
      try {
        // Normalize the referrer URL
        const referrerUrl = new URL(event.referrer);
        referrer = referrerUrl.hostname;
      } catch (e) {
        // If URL parsing fails, try to extract hostname manually
        referrer = event.referrer.replace(/^(https?:\/\/)?(www\.)?/, "").split("/")[0];
      }
    }

    // Check if this is an auth redirect or from the same domain
    const isAuthRedirect = event.isAuthRedirect;
    let isSameDomain = false;
    try {
      const currentHostname = new URL(event.href).hostname;
      isSameDomain = referrer === currentHostname;
    } catch (e) {
      console.error('Error comparing domains:', e);
    }

    // If this is an auth redirect or from the same domain, use the previous referrer for this visitor
    if ((isAuthRedirect || isSameDomain) && visitorReferrers[event.visitorId]) {
      referrer = visitorReferrers[event.visitorId];
    } else {
      // Store this referrer for future events from this visitor
      visitorReferrers[event.visitorId] = ref || referrer;
    }

    // Use ref parameter as the key if present, otherwise use referrer
    const key = ref || referrer || "direct";

    // Track the referrer for this visitor
    visitorReferrerMapping.set(event.visitorId, key);

    if (!acc[key]) {
      acc[key] = {
        visitors: new Set(),
        referralParams: {},
        // Store the normalized referrer
        actualReferrerUrl: referrer,
      };
    }

    acc[key].visitors.add(event.visitorId);

    // If there are referral parameters or ref parameter, merge them
    if (event.referralParams || ref) {
      acc[key].referralParams = {
        ...acc[key].referralParams,
        ...event.referralParams,
        ...(ref ? { ref, campaign: ref } : {}),
      };
    }

    // Ensure referralParams are merged correctly even if entry exists
    if (event.referralParams || ref) {
      acc[key].referralParams = {
        ...acc[key].referralParams,
        ...event.referralParams,
        ...(ref ? { ref, campaign: ref } : {}),
      };
    }

    return acc;
  }, {} as Record<string, { visitors: Set<string>; referralParams: any; actualReferrerUrl: string | null }>);

  // Calculate total visitors in referrers
  const totalReferrerVisitors = Object.values(referrers).reduce(
    (sum, { visitors }) => sum + visitors.size,
    0
  );

  if (totalReferrerVisitors !== totalUniqueVisitors) {
    // Find visitors that are in the total but not in any referrer
    const allVisitorIds = new Set(
      events
        .filter((event) => event.location?.city && event.location?.country)
        .map((event) => event.visitorId)
    );

    const referrerVisitorIds = new Set(
      Object.values(referrers).flatMap(({ visitors }) => Array.from(visitors))
    );

    const missingVisitors = Array.from(allVisitorIds).filter(
      (id) => !referrerVisitorIds.has(id)
    );
  }

  return Object.entries(referrers)
    .map(([key, data]) => ({
      // Use the already normalized hostname
      url: data.actualReferrerUrl,
      visitors: data.visitors.size,
      referralParams: data.referralParams,
    }))
    .sort((a, b) => b.visitors - a.visitors);
}

// Get operating systems data with unique visitors
export function getOperatingSystems(events: Event[]): OperatingSystem[] {
  const osData = events.reduce((acc, event) => {
    const userAgent = event.userAgent || "";
    let os = "Unknown";

    // Simplified OS detection without versions
    if (/Windows/.test(userAgent)) {
      os = "Windows";
    } else if (/Android/.test(userAgent)) {
      os = "Android";
    } else if (/(iPhone|iPad|iPod)/.test(userAgent)) {
      os = "iOS";
    } else if (/Mac OS X/.test(userAgent) && !/(iPhone|iPad|iPod)/.test(userAgent)) {
      os = "macOS";
    } else if (/Linux/.test(userAgent)) {
      os = "Linux";
    } else if (/CrOS/.test(userAgent)) {
      os = "Chrome OS";
    } else if (/Windows Phone/.test(userAgent)) {
      os = "Windows Phone";
    }

    if (!acc[os]) {
      acc[os] = new Set();
    }
    acc[os].add(event.visitorId);
    return acc;
  }, {} as Record<string, Set<string>>);

  return Object.entries(osData)
    .map(([name, visitors]) => ({ name, visitors: visitors.size }))
    .sort((a, b) => b.visitors - a.visitors);
}

// Get top locations with unique visitors
export function getTopLocations(events: Event[]): Location[] {
  const locations = events.reduce((acc, event) => {
    if (!event.location?.country) return acc; // Only require country

    // Create a key based on available location info
    const key = event.location.city
      ? `${event.location.city}, ${event.location.country}`
      : event.location.region
      ? `${event.location.region}, ${event.location.country}`
      : event.location.country;

    if (!acc[key]) {
      acc[key] = {
        city: event.location.city || null,
        country: event.location.country,
        region: event.location?.region || null,
        visitors: new Set(),
      };
    }
    acc[key].visitors.add(event.visitorId);
    return acc;
  }, {} as Record<string, { city: string | null; country: string; region: string | null; visitors: Set<string> }>);

  return Object.values(locations)
    .map((loc) => ({
      city: loc.city,
      country: loc.country,
      region: loc.region,
      visitors: loc.visitors.size,
    }))
    .sort((a, b) => b.visitors - a.visitors); // Removed .slice(0, 10)
}

// Get browser data with unique visitors
export function getBrowsers(
  events: Event[]
): { name: string; visitors: number }[] {
  const browserData = events.reduce((acc, event) => {
    // Skip events without location data
    if (!event.location?.city || !event.location?.country) return acc;

    const userAgent = event.userAgent || "";
    let browser = "Unknown";

    if (/Chrome/.test(userAgent) && !/Chromium|Edge/.test(userAgent))
      browser = "Chrome";
    else if (/Firefox/.test(userAgent)) browser = "Firefox";
    else if (
      /Safari/.test(userAgent) &&
      !/Chrome|Chromium|Edge/.test(userAgent)
    )
      browser = "Safari";
    else if (/Edge/.test(userAgent)) browser = "Edge";
    else if (/Opera|OPR/.test(userAgent)) browser = "Opera";
    else if (/MSIE|Trident/.test(userAgent)) browser = "Internet Explorer";

    if (!acc[browser]) {
      acc[browser] = new Set();
    }
    acc[browser].add(event.visitorId);
    return acc;
  }, {} as Record<string, Set<string>>);

  return Object.entries(browserData)
    .map(([name, visitors]) => ({ name, visitors: visitors.size }))
    .sort((a, b) => b.visitors - a.visitors);
}

// Get device data with unique visitors
export function getDevices(
  events: Event[]
): { name: string; visitors: number }[] {
  const deviceData = events.reduce((acc, event) => {
    // Skip events without location data
    if (!event.location?.city || !event.location?.country) return acc;

    const userAgent = event.userAgent || "";
    let device = "Unknown";

    if (/Mobile|Android|iPhone|iPad|iPod/.test(userAgent)) {
      if (/iPad/.test(userAgent)) device = "Tablet";
      else if (/Mobile|Android|iPhone|iPod/.test(userAgent)) device = "Mobile";
    } else {
      device = "Desktop";
    }

    if (!acc[device]) {
      acc[device] = new Set();
    }
    acc[device].add(event.visitorId);
    return acc;
  }, {} as Record<string, Set<string>>);

  return Object.entries(deviceData)
    .map(([name, visitors]) => ({ name, visitors: visitors.size }))
    .sort((a, b) => b.visitors - a.visitors);
}

// Get entry pages (first page in each session)
export function getEntryPages(events: Event[]): PageView[] {
  const entryPages = events.reduce((acc, event) => {
    // Skip events without location data
    if (!event.location?.city || !event.location?.country) return acc;

    const sessionId = event.sessionId;
    
    // Safely extract pathname from href
    let path: string;
    try {
      // Try to create URL object
      const url = new URL(event.href);
      path = url.pathname;
    } catch (error) {
      // If href is not a valid URL (e.g., relative path), use it as-is
      // Remove query params and hash if present
      path = event.href.split('?')[0].split('#')[0];
    }

    if (!acc[sessionId]) {
      // Find the earliest event for this session
      const sessionEvents = events
        .filter((e) => e.sessionId === sessionId)
        // Only consider events with location data
        .filter((e) => e.location?.city && e.location?.country);

      if (sessionEvents.length === 0) return acc;

      const earliestEvent = sessionEvents.reduce((earliest, current) => {
        return new Date(current.timestamp) < new Date(earliest.timestamp)
          ? current
          : earliest;
      }, sessionEvents[0]);

      // Safely extract pathname from earliest event href
      let entryPath: string;
      try {
        // Try to create URL object
        const url = new URL(earliestEvent.href);
        entryPath = url.pathname;
      } catch (error) {
        // If href is not a valid URL (e.g., relative path), use it as-is
        // Remove query params and hash if present
        entryPath = earliestEvent.href.split('?')[0].split('#')[0];
      }
      
      if (!acc[entryPath]) {
        acc[entryPath] = new Set();
      }
      acc[entryPath].add(event.visitorId);
    }

    return acc;
  }, {} as Record<string, Set<string>>);

  return Object.entries(entryPages)
    .map(([path, visitors]) => ({ path, visitors: visitors.size }))
    .sort((a, b) => b.visitors - a.visitors);
}

// Get exit pages (last page in each session)
export function getExitPages(events: Event[]): PageView[] {
  const exitPages = events.reduce((acc, event) => {
    // Skip events without location data
    if (!event.location?.city || !event.location?.country) return acc;

    const sessionId = event.sessionId;
    
    // Safely extract pathname from href
    let path: string;
    try {
      // Try to create URL object
      const url = new URL(event.href);
      path = url.pathname;
    } catch (error) {
      // If href is not a valid URL (e.g., relative path), use it as-is
      // Remove query params and hash if present
      path = event.href.split('?')[0].split('#')[0];
    }

    if (!acc[sessionId]) {
      // Find the latest event for this session
      const sessionEvents = events
        .filter((e) => e.sessionId === sessionId)
        // Only consider events with location data
        .filter((e) => e.location?.city && e.location?.country);

      if (sessionEvents.length === 0) return acc;

      const latestEvent = sessionEvents.reduce((latest, current) => {
        return new Date(current.timestamp) > new Date(latest.timestamp)
          ? current
          : latest;
      }, sessionEvents[0]);

      // Safely extract pathname from latest event href
      let exitPath: string;
      try {
        // Try to create URL object
        const url = new URL(latestEvent.href);
        exitPath = url.pathname;
      } catch (error) {
        // If href is not a valid URL (e.g., relative path), use it as-is
        // Remove query params and hash if present
        exitPath = latestEvent.href.split('?')[0].split('#')[0];
      }
      
      if (!acc[exitPath]) {
        acc[exitPath] = new Set();
      }
      acc[exitPath].add(event.visitorId);
    }

    return acc;
  }, {} as Record<string, Set<string>>);

  return Object.entries(exitPages)
    .map(([path, visitors]) => ({ path, visitors: visitors.size }))
    .sort((a, b) => b.visitors - a.visitors);
}

// Get external exit links (external URLs that visitors clicked to leave the site)
export function getExitLinks(events: Event[]): { url: string; visitors: number }[] {
  // Filter for external_link events
  const externalClickEvents = events.filter(event => 
    event.type === 'external_link' || 
    event.type === 'external_link_update'
  );
  
  // Group by URL and count unique visitors
  const exitLinksData = externalClickEvents.reduce((acc, event) => {
    // Get the URL from the extraData field
    const url = event.extraData?.url;
    
    if (!url) return acc;
    
    if (!acc[url]) {
      acc[url] = new Set();
    }
    
    acc[url].add(event.visitorId);
    return acc;
  }, {} as Record<string, Set<string>>);
  
  return Object.entries(exitLinksData)
    .map(([url, visitors]) => ({ url, visitors: visitors.size }))
    .sort((a, b) => b.visitors - a.visitors);
}

// ===== REVENUE ANALYTICS FUNCTIONS =====

// Get total revenue with period comparison
export function getTotalRevenue(events: Event[], comparisonEvents?: Event[]): {
  total: number;
  currency: string;
  delta?: number;
  deltaPercentage?: number;
} {
  const paymentEvents = events.filter(event => 
    event.type === 'payment' && 
    event.revenueData?.amount &&
    !event.isBot
  );
  
  const totalRevenue = paymentEvents.reduce((sum, event) => {
    return sum + (event.revenueData?.amount || 0) / 100; // Convert from cents to dollars
  }, 0);
  
  // Get the most common currency, defaulting to 'usd'
  const currencies = paymentEvents.map(e => e.revenueData?.currency || 'usd');
  const currency = currencies.length > 0 
    ? currencies.sort((a, b) => 
        currencies.filter(c => c === a).length - currencies.filter(c => c === b).length
      ).pop() || 'usd'
    : 'usd';
  
  let delta: number | undefined;
  let deltaPercentage: number | undefined;
  
  if (comparisonEvents) {
    const comparisonPaymentEvents = comparisonEvents.filter(event => 
      event.type === 'payment' && 
      event.revenueData?.amount &&
      !event.isBot
    );
    
    const comparisonRevenue = comparisonPaymentEvents.reduce((sum, event) => {
      return sum + (event.revenueData?.amount || 0) / 100; // Convert from cents to dollars
    }, 0);
    
    delta = totalRevenue - comparisonRevenue;
    deltaPercentage = comparisonRevenue > 0 
      ? Math.round((delta / comparisonRevenue) * 100)
      : totalRevenue > 0 ? 100 : 0;
  }
  
  return {
    total: totalRevenue,
    currency,
    delta,
    deltaPercentage
  };
}

// Get revenue by time period for chart data
export function getRevenueByPeriod(events: Event[], periodType: 'hour' | 'day' | 'week' | 'month' = 'day'): Array<{
  period: string;
  revenue: number;
  transactions: number;
}> {
  const paymentEvents = events.filter(event => 
    event.type === 'payment' && 
    event.revenueData?.amount &&
    !event.isBot
  );
  
  const periodData: Record<string, { revenue: number; transactions: number }> = {};
  
  paymentEvents.forEach(event => {
    const date = new Date(event.timestamp);
    let periodKey: string;
    
    switch (periodType) {
      case 'hour':
        periodKey = format(date, 'ha');
        break;
      case 'week':
        // Get start of week
        const weekStart = new Date(date);
        weekStart.setDate(date.getDate() - date.getDay());
        periodKey = format(weekStart, 'MMM dd');
        break;
      case 'month':
        periodKey = format(date, 'MMM yyyy');
        break;
      default: // day
        periodKey = format(date, 'MMM dd');
        break;
    }
    
    if (!periodData[periodKey]) {
      periodData[periodKey] = { revenue: 0, transactions: 0 };
    }
    
    periodData[periodKey].revenue += (event.revenueData?.amount || 0) / 100; // Convert from cents to dollars
    periodData[periodKey].transactions += 1;
  });
  
  return Object.entries(periodData)
    .map(([period, data]) => ({
      period,
      revenue: data.revenue,
      transactions: data.transactions
    }))
    .sort((a, b) => a.period.localeCompare(b.period));
}

// Calculate conversion rate (visitors who made purchases / total visitors)
export function getConversionRate(events: Event[], comparisonEvents?: Event[]): {
  rate: number;
  delta?: number;
  deltaPercentage?: number;
} {
  const totalVisitors = getUniqueVisitors(events);
  const paymentEvents = events.filter(event => 
    event.type === 'payment' && 
    event.revenueData?.amount &&
    !event.isBot
  );
  const convertedVisitors = new Set(paymentEvents.map(e => e.visitorId)).size;
  
  const conversionRate = totalVisitors > 0 ? Math.round((convertedVisitors / totalVisitors) * 100 * 100) / 100 : 0;
  
  let delta: number | undefined;
  let deltaPercentage: number | undefined;
  
  if (comparisonEvents) {
    const comparisonTotalVisitors = getUniqueVisitors(comparisonEvents);
    const comparisonPaymentEvents = comparisonEvents.filter(event => 
      event.type === 'payment' && 
      event.revenueData?.amount &&
      !event.isBot
    );
    const comparisonConvertedVisitors = new Set(comparisonPaymentEvents.map(e => e.visitorId)).size;
    const comparisonConversionRate = comparisonTotalVisitors > 0 
      ? Math.round((comparisonConvertedVisitors / comparisonTotalVisitors) * 100 * 100) / 100 
      : 0;
    
    delta = Math.round((conversionRate - comparisonConversionRate) * 100) / 100;
    deltaPercentage = comparisonConversionRate > 0 
      ? Math.round((delta / comparisonConversionRate) * 100)
      : conversionRate > 0 ? 100 : 0;
  }
  
  return {
    rate: conversionRate,
    delta,
    deltaPercentage
  };
}

// Calculate average order value
export function getAverageOrderValue(events: Event[], comparisonEvents?: Event[]): {
  value: number;
  currency: string;
  delta?: number;
  deltaPercentage?: number;
} {
  const paymentEvents = events.filter(event => 
    event.type === 'payment' && 
    event.revenueData?.amount &&
    !event.isBot
  );
  
  const totalRevenue = paymentEvents.reduce((sum, event) => sum + (event.revenueData?.amount || 0), 0);
  const averageOrderValue = paymentEvents.length > 0 ? totalRevenue / paymentEvents.length : 0;
  
  // Get most common currency
  const currencies = paymentEvents.map(e => e.revenueData?.currency || 'usd');
  const currency = currencies.length > 0 
    ? currencies.sort((a, b) => 
        currencies.filter(c => c === a).length - currencies.filter(c => c === b).length
      ).pop() || 'usd'
    : 'usd';
  
  let delta: number | undefined;
  let deltaPercentage: number | undefined;
  
  if (comparisonEvents) {
    const comparisonPaymentEvents = comparisonEvents.filter(event => 
      event.type === 'payment' && 
      event.revenueData?.amount &&
      !event.isBot
    );
    
    const comparisonTotalRevenue = comparisonPaymentEvents.reduce((sum, event) => sum + (event.revenueData?.amount || 0), 0);
    const comparisonAOV = comparisonPaymentEvents.length > 0 ? comparisonTotalRevenue / comparisonPaymentEvents.length : 0;
    
    delta = averageOrderValue - comparisonAOV;
    deltaPercentage = comparisonAOV > 0 
      ? Math.round((delta / comparisonAOV) * 100)
      : averageOrderValue > 0 ? 100 : 0;
  }
  
  return {
    value: Math.round(averageOrderValue),
    currency,
    delta: delta ? Math.round(delta) : undefined,
    deltaPercentage
  };
}

// Get revenue attribution by referrer
export function getRevenueByReferrer(events: Event[]): Array<{
  referrer: string;
  revenue: number;
  transactions: number;
  visitors: number;
}> {
  const paymentEvents = events.filter(event => 
    event.type === 'payment' && 
    event.revenueData?.amount &&
    !event.isBot
  );
  
  const referrerData: Record<string, {
    revenue: number;
    transactions: number;
    visitors: Set<string>;
  }> = {};
  
  paymentEvents.forEach(event => {
    const referrer = event.referrer || 'Direct';
    
    if (!referrerData[referrer]) {
      referrerData[referrer] = {
        revenue: 0,
        transactions: 0,
        visitors: new Set()
      };
    }
    
    referrerData[referrer].revenue += event.revenueData?.amount || 0;
    referrerData[referrer].transactions += 1;
    referrerData[referrer].visitors.add(event.visitorId);
  });
  
  return Object.entries(referrerData)
    .map(([referrer, data]) => ({
      referrer,
      revenue: data.revenue,
      transactions: data.transactions,
      visitors: data.visitors.size
    }))
    .sort((a, b) => b.revenue - a.revenue);
}

// Get revenue attribution by location
export function getRevenueByLocation(events: Event[]): Array<{
  country: string;
  city?: string;
  revenue: number;
  transactions: number;
  visitors: number;
}> {
  const paymentEvents = events.filter(event => 
    event.type === 'payment' && 
    event.revenueData?.amount &&
    event.location?.country &&
    !event.isBot
  );
  
  const locationData: Record<string, {
    country: string;
    city?: string;
    revenue: number;
    transactions: number;
    visitors: Set<string>;
  }> = {};
  
  paymentEvents.forEach(event => {
    const country = event.location!.country!;
    const city = event.location!.city;
    const key = city ? `${city}, ${country}` : country;
    
    if (!locationData[key]) {
      locationData[key] = {
        country,
        city: city || undefined,
        revenue: 0,
        transactions: 0,
        visitors: new Set()
      };
    }
    
    locationData[key].revenue += event.revenueData?.amount || 0;
    locationData[key].transactions += 1;
    locationData[key].visitors.add(event.visitorId);
  });
  
  return Object.entries(locationData)
    .map(([_, data]) => ({
      country: data.country,
      city: data.city,
      revenue: data.revenue,
      transactions: data.transactions,
      visitors: data.visitors.size
    }))
    .sort((a, b) => b.revenue - a.revenue);
}

// Get revenue per visitor metric
export function getRevenuePerVisitor(events: Event[], comparisonEvents?: Event[]): {
  value: number;
  currency: string;
  delta?: number;
  deltaPercentage?: number;
} {
  const totalRevenue = getTotalRevenue(events);
  const totalVisitors = getUniqueVisitors(events);
  const revenuePerVisitor = totalVisitors > 0 ? totalRevenue.total / totalVisitors : 0;
  
  let delta: number | undefined;
  let deltaPercentage: number | undefined;
  
  if (comparisonEvents) {
    const comparisonTotalRevenue = getTotalRevenue(comparisonEvents);
    const comparisonTotalVisitors = getUniqueVisitors(comparisonEvents);
    const comparisonRPV = comparisonTotalVisitors > 0 ? comparisonTotalRevenue.total / comparisonTotalVisitors : 0;
    
    delta = revenuePerVisitor - comparisonRPV;
    deltaPercentage = comparisonRPV > 0 
      ? Math.round((delta / comparisonRPV) * 100)
      : revenuePerVisitor > 0 ? 100 : 0;
  }
  
  return {
    value: Math.round(revenuePerVisitor),
    currency: totalRevenue.currency,
    delta: delta ? Math.round(delta) : undefined,
    deltaPercentage
  };
}

// Get customer analytics (new vs returning)
export function getCustomerAnalytics(events: Event[]): {
  newCustomers: { count: number; revenue: number; };
  returningCustomers: { count: number; revenue: number; };
  totalCustomers: number;
} {
  const paymentEvents = events.filter(event => 
    event.type === 'payment' && 
    event.revenueData?.amount &&
    !event.isBot
  );
  
  const customerData = paymentEvents.reduce((acc, event) => {
    const isNew = event.revenueData?.isNewCustomer ?? true;
    const revenue = event.revenueData?.amount || 0;
    
    if (isNew) {
      acc.newCustomers.count += 1;
      acc.newCustomers.revenue += revenue;
    } else {
      acc.returningCustomers.count += 1;
      acc.returningCustomers.revenue += revenue;
    }
    
    return acc;
  }, {
    newCustomers: { count: 0, revenue: 0 },
    returningCustomers: { count: 0, revenue: 0 }
  });
  
  const totalCustomers = new Set(
    paymentEvents
      .filter(e => e.revenueData?.stripeCustomerId)
      .map(e => e.revenueData!.stripeCustomerId!)
  ).size;
  
  return {
    ...customerData,
    totalCustomers
  };
}

// Get revenue attribution by first-touch source
export function getFirstTouchRevenue(events: Event[]): Array<{
  source: string;
  revenue: number;
  transactions: number;
}> {
  const paymentEvents = events.filter(event => 
    event.type === 'payment' && 
    event.revenueData?.amount &&
    !event.isBot
  );
  
  const sourceData: Record<string, { revenue: number; transactions: number }> = {};
  
  // For each payment, find the first event for that visitor
  paymentEvents.forEach(paymentEvent => {
    const visitorEvents = events
      .filter(e => e.visitorId === paymentEvent.visitorId)
      .filter(e => new Date(e.timestamp) <= new Date(paymentEvent.timestamp))
      .sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());
    
    const firstEvent = visitorEvents[0];
    const source = firstEvent?.referrer || 'Direct';
    
    if (!sourceData[source]) {
      sourceData[source] = { revenue: 0, transactions: 0 };
    }
    
    sourceData[source].revenue += paymentEvent.revenueData?.amount || 0;
    sourceData[source].transactions += 1;
  });
  
  return Object.entries(sourceData)
    .map(([source, data]) => ({
      source,
      revenue: data.revenue,
      transactions: data.transactions
    }))
    .sort((a, b) => b.revenue - a.revenue);
}

// Get revenue attribution by last-touch source  
export function getLastTouchRevenue(events: Event[]): Array<{
  source: string;
  revenue: number;
  transactions: number;
}> {
  const paymentEvents = events.filter(event => 
    event.type === 'payment' && 
    event.revenueData?.amount &&
    !event.isBot
  );
  
  const sourceData: Record<string, { revenue: number; transactions: number }> = {};
  
  // For each payment, find the last non-payment event for that visitor before the payment
  paymentEvents.forEach(paymentEvent => {
    const visitorEvents = events
      .filter(e => e.visitorId === paymentEvent.visitorId)
      .filter(e => e.type !== 'payment')
      .filter(e => new Date(e.timestamp) <= new Date(paymentEvent.timestamp))
      .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
    
    const lastEvent = visitorEvents[0];
    const source = lastEvent?.referrer || 'Direct';
    
    if (!sourceData[source]) {
      sourceData[source] = { revenue: 0, transactions: 0 };
    }
    
    sourceData[source].revenue += paymentEvent.revenueData?.amount || 0;
    sourceData[source].transactions += 1;
  });
  
  return Object.entries(sourceData)
    .map(([source, data]) => ({
      source,
      revenue: data.revenue,
      transactions: data.transactions
    }))
    .sort((a, b) => b.revenue - a.revenue);
}
