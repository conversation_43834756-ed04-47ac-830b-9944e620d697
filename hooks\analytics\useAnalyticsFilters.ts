import { useMemo } from 'react';
import { Event, Filter } from './types';

export function useAnalyticsFilters(events: Event[], prevEvents: Event[], activeFilters: Filter[]) {
  const filteredEvents = useMemo(() => {
    if (!activeFilters.length) return events;

    // First, group events by session to identify entry pages
    const sessionFirstEvents = events.reduce((acc, event) => {
      if (!acc[event.sessionId]) {
        // Find the earliest event for this session
        const sessionEvents = events.filter(e => e.sessionId === event.sessionId);
        const earliestEvent = sessionEvents.reduce((earliest, current) => {
          return new Date(current.timestamp) < new Date(earliest.timestamp)
            ? current
            : earliest;
        }, sessionEvents[0]);
        acc[event.sessionId] = earliestEvent;
      }
      return acc;
    }, {} as Record<string, Event>);

    return events.filter((event) => {
      // Event must match ALL filters (AND condition)
      return activeFilters.every((activeFilter) => {
        switch (activeFilter.type) {
          case "referrer":
            // Handle the "direct" case (no referrer)
            if (activeFilter.value === "direct") {
              return !event.referrer;
            }
            // For all other referrers, extract hostname and compare
            else {
              if (!event.referrer) return false;
              
              try {
                // Normalize the event referrer domain in the same way we do in the UI
                const eventUrl = new URL(event.referrer);
                const normalizedEventDomain = eventUrl.hostname.replace(/^www\./, "");
                
                // Normalize the filter value in case it's a full URL (for backward compatibility)
                let normalizedFilterDomain;
                if (activeFilter.value.includes("://") || activeFilter.value.includes(".")) {
                  try {
                    // Try to parse as URL if it looks like one
                    const filterUrl = new URL(activeFilter.value.startsWith("http") ? 
                      activeFilter.value : `https://${activeFilter.value}`);
                    normalizedFilterDomain = filterUrl.hostname.replace(/^www\./, "");
                  } catch (e) {
                    // If URL parsing fails, just use the value as is (could be already normalized)
                    normalizedFilterDomain = activeFilter.value.replace(/^www\./, "");
                  }
                } else {
                  // Already normalized or simple domain
                  normalizedFilterDomain = activeFilter.value;
                }
                
                return normalizedEventDomain === normalizedFilterDomain;
              } catch (e) {
                // If URL parsing fails, try to extract hostname manually and normalize it
                const normalizedEventDomain = event.referrer.replace(/^(https?:\/\/)?(www\.)?/, "").split("/")[0];
                const normalizedFilterDomain = activeFilter.value.replace(/^(https?:\/\/)?(www\.)?/, "").split("/")[0];
                return normalizedEventDomain === normalizedFilterDomain;
              }
            }

          case "entry-page":
            // Check if this event is the first event in its session and matches the path
            const isEntryPage = sessionFirstEvents[event.sessionId]?.timestamp === event.timestamp;
            
            // Safely extract pathname from href
            let entryPathname: string;
            try {
              // Try to create URL object
              const url = new URL(event.href);
              entryPathname = url.pathname;
            } catch (error) {
              // If href is not a valid URL (e.g., relative path), use it as-is
              // Remove query params and hash if present
              entryPathname = event.href.split('?')[0].split('#')[0];
            }
            
            return isEntryPage && entryPathname === activeFilter.value;

          case "campaign":
            // Check campaign data in multiple places
            const campaignFilter = activeFilter.value.toLowerCase();

            // Check in referralParams
            const refParam = event.referralParams?.ref?.toLowerCase();
            const campaignParam = event.referralParams?.campaign?.toLowerCase();
            const utmCampaignParam = event.referralParams?.utm_campaign?.toLowerCase();

            // Check in URL parameters
            let urlRef, urlCampaign, urlUtmCampaign;
            try {
              const url = new URL(event.href);
              urlRef = url.searchParams.get("ref")?.toLowerCase();
              urlCampaign = url.searchParams.get("campaign")?.toLowerCase();
              urlUtmCampaign = url.searchParams.get("utm_campaign")?.toLowerCase();
            } catch (e) {
              // Invalid URL, ignore
            }

            // Check in event properties directly
            const eventRef = (event as any).ref?.toLowerCase();
            const eventCampaign = (event as any).campaign?.toLowerCase();
            const eventUtmCampaign = (event as any).utm_campaign?.toLowerCase();

            // Check for exact matches first
            return (
              campaignFilter === refParam ||
              campaignFilter === campaignParam ||
              campaignFilter === utmCampaignParam ||
              campaignFilter === urlRef ||
              campaignFilter === urlCampaign ||
              campaignFilter === urlUtmCampaign ||
              campaignFilter === eventRef ||
              campaignFilter === eventCampaign ||
              campaignFilter === eventUtmCampaign
            );

          case "utm":
            // Check UTM data in multiple places
            // 1. Check in referralParams
            const utmSourceParam = event.referralParams?.utm_source?.toLowerCase();
            const utmMediumParam = event.referralParams?.utm_medium?.toLowerCase();

            // 2. Check in URL parameters
            let urlUtmSource, urlUtmMedium;
            try {
              const url = new URL(event.href);
              urlUtmSource = url.searchParams.get("utm_source")?.toLowerCase();
              urlUtmMedium = url.searchParams.get("utm_medium")?.toLowerCase();
            } catch (e) {
              // Invalid URL, ignore
            }

            // 3. Check directly in event properties
            const eventUtmSource = (event as any).utm_source?.toLowerCase();
            const eventUtmMedium = (event as any).utm_medium?.toLowerCase();

            // If filter value is "No UTM" and there are no UTM params anywhere, return true
            if (
              activeFilter.value === "No UTM" &&
              !utmSourceParam &&
              !utmMediumParam &&
              !urlUtmSource &&
              !urlUtmMedium &&
              !eventUtmSource &&
              !eventUtmMedium
            ) {
              return true;
            }

            const utmFilterValue = activeFilter.value.toLowerCase();

            // Combine UTM values from all sources
            const sourcesToCheck = [utmSourceParam, urlUtmSource, eventUtmSource].filter(Boolean);
            const mediumsToCheck = [utmMediumParam, urlUtmMedium, eventUtmMedium].filter(Boolean);

            // Create strings to match in the format "source / medium" for all combinations
            const stringsToCheck = new Set<string>();

            // Add each source by itself
            sourcesToCheck.forEach((source) => {
              stringsToCheck.add(source);
            });

            // Add source / medium combinations
            sourcesToCheck.forEach((source) => {
              mediumsToCheck.forEach((medium) => {
                stringsToCheck.add(`${source} / ${medium}`);
              });
            });

            // Exact match on any UTM string
            for (const str of stringsToCheck) {
              if (str === utmFilterValue) {
                return true;
              }
            }

            // Partial match on any UTM string
            for (const str of stringsToCheck) {
              if (str.includes(utmFilterValue) || utmFilterValue.includes(str)) {
                return true;
              }
            }

            return false;

          case "page":
            // Check for external link filter
            if (activeFilter.value.startsWith('external_link:')) {
              const targetUrl = activeFilter.value.replace('external_link:', '');
              return (
                (event.type === 'external_link' || event.type === 'external_link_update') &&
                event.extraData?.url === targetUrl
              );
            }
            // Regular page filter
            // Safely extract pathname from href
            let pagePathname: string;
            try {
              // Try to create URL object
              const url = new URL(event.href);
              pagePathname = url.pathname;
            } catch (error) {
              // If href is not a valid URL (e.g., relative path), use it as-is
              // Remove query params and hash if present
              pagePathname = event.href.split('?')[0].split('#')[0];
            }
            
            return pagePathname === activeFilter.value;

          case "location":
            if (!event.location) return false;
            const value = activeFilter.value;
            if (typeof value === "string") {
              return event.location.country === value;
            } else if ("city" in value) {
              return (
                event.location.city === value.city &&
                event.location.country === value.country
              );
            } else if ("region" in value) {
              return (
                event.location.region === value.region &&
                event.location.country === value.country
              );
            }
            return false;

          case "system":
            const userAgent = event.userAgent || "";

            // OS check - simplified to match generic OS names
            if (activeFilter.value === "Windows") {
              return /Windows/.test(userAgent);
            } else if (activeFilter.value === "Android") {
              return /Android/.test(userAgent);
            } else if (activeFilter.value === "iOS") {
              return /(iPhone|iPad|iPod)/.test(userAgent);
            } else if (activeFilter.value === "macOS") {
              // More specific macOS check that excludes iOS devices
              return /Mac OS X/.test(userAgent) && !/(iPhone|iPad|iPod)/.test(userAgent);
            } else if (activeFilter.value === "Linux") {
              return /Linux/.test(userAgent);
            } else if (activeFilter.value === "Chrome OS") {
              return /CrOS/.test(userAgent);
            } else if (activeFilter.value === "Windows Phone") {
              return /Windows Phone/.test(userAgent);
            } else if (activeFilter.value === "Unknown") {
              // Return true if none of the known OS patterns match
              return !(
                /Windows/.test(userAgent) ||
                /Android/.test(userAgent) ||
                /(iPhone|iPad|iPod)/.test(userAgent) ||
                (/Mac OS X/.test(userAgent) && !/(iPhone|iPad|iPod)/.test(userAgent)) ||
                /Linux/.test(userAgent) ||
                /CrOS/.test(userAgent) ||
                /Windows Phone/.test(userAgent)
              );
            }
            // Browser check
            else if (["Chrome", "Firefox", "Safari", "Edge", "Opera"].includes(activeFilter.value)) {
              const browserMap: Record<string, RegExp> = {
                Chrome: /Chrome/,
                Firefox: /Firefox/,
                Safari: {
                  test: (ua: string) => /Safari/.test(ua) && !/Chrome|Chromium|Edge/.test(ua),
                } as RegExp,
                Edge: /Edge/,
                Opera: /Opera|OPR/,
              };
              return browserMap[activeFilter.value].test(userAgent);
            }
            // Device check
            else if (["Mobile", "Tablet", "Desktop"].includes(activeFilter.value)) {
              if (activeFilter.value === "Mobile") {
                return /Mobile|Android|iPhone|iPod/.test(userAgent) && !/iPad/.test(userAgent);
              } else if (activeFilter.value === "Tablet") {
                return /iPad/.test(userAgent);
              } else {
                return !/Mobile|Android|iPhone|iPad|iPod/.test(userAgent);
              }
            }
            return false;

          case "hour":
            const eventHour = new Date(event.timestamp).getHours();
            const filterHour = activeFilter.value;

            // Convert the filter hour string to a number for comparison
            let hourToCompare = 0;
            // Normalize filter value by removing spaces and converting to lowercase
            const normalizedFilter = filterHour.toLowerCase().replace(/\s+/g, "");

            if (normalizedFilter.includes("am")) {
              // Handle 12am as 0
              if (normalizedFilter === "12am") {
                hourToCompare = 0;
              } else {
                hourToCompare = parseInt(normalizedFilter.replace("am", ""));
              }
            } else if (normalizedFilter.includes("pm")) {
              // Handle 12pm as 12, others add 12
              if (normalizedFilter === "12pm") {
                hourToCompare = 12;
              } else {
                hourToCompare = parseInt(normalizedFilter.replace("pm", "")) + 12;
              }
            } else {
              // If no am/pm specified, just parse the number
              hourToCompare = parseInt(normalizedFilter);
            }

            return eventHour === hourToCompare;

          default:
            return true;
        }
      });
    });
  }, [events, activeFilters]);

  const filteredPrevEvents = useMemo(() => {
    if (!activeFilters.length) return prevEvents;

    return prevEvents.filter((event) => {
      return activeFilters.every((activeFilter) => {
        // Use the same filtering logic as above
        // This is a bit redundant but keeps the logic together
        switch (activeFilter.type) {
          // ... same cases as above ...
          default:
            return true;
        }
      });
    });
  }, [prevEvents, activeFilters]);

  return {
    filteredEvents,
    filteredPrevEvents,
  };
} 