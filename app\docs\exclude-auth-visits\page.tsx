import Image from "next/image";
import Link from "next/link";
import React from "react";

function Page() {
  return (
    <div className="max-w-3xl mx-auto py-8 px-4">
      <h1 className="text-3xl font-bold mb-6">Exclude internal traffic from your analytics</h1>

      <div className="space-y-6">
        <section className="space-y-4">
          <ol className="list-decimal list-inside space-y-3">
            <li>
              Go to your{" "}
              <Link
                href="/dashboard"
                target="_blank"
                className="text-blue-400 hover:underline"
              >
                dashboard
              </Link>{" "}
              and select the website you wish to filter out internal traffic.
            </li>
            <li>
              Click on the dropdown menu for your website in the top left of
              your dashboard
            </li>
            <Image
              src={"/docs_assets/site_settings.png"}
              width={1200}
              height={600}
              alt="Site Settings screenshot"
            />
            <li>Click on &quot;Site Settings&quot;</li>
            <li>
              Under &quot;Filter&quot; tab, enable the
          &quot;Exclude authenticated visits&quot; toggle.
            </li>
            
          </ol>
        </section>

        <section className="mb-12">
        <h2 className="text-2xl font-semibold mb-4">
          Private Routes Configuration
        </h2>
        <p className="mb-4">
          Define which routes in your application should be considered private.
          Visits to these routes will be used to identify authenticated users.
        </p>

        <div className="space-y-4">
          <h3 className="text-xl font-semibold">Example Routes:</h3>
          <ul className="space-y-3">
            <li className="flex items-start gap-2">
              <code className="bg-blue-500/20 text-blue-400 px-2 py-1 rounded">
                /dashboard/**
              </code>
              <span className="text-gray-400">
                - Matches all dashboard routes
              </span>
            </li>
            <li className="flex items-start gap-2">
              <code className="bg-blue-500/20 text-blue-400 px-2 py-1 rounded">
                /settings/**
              </code>
              <span className="text-gray-400">
                - Matches all settings pages
              </span>
            </li>
            <li className="flex items-start gap-2">
              <code className="bg-blue-500/20 text-blue-400 px-2 py-1 rounded">
                /profile/**
              </code>
              <span className="text-gray-400">
                - Matches all profile related pages
              </span>
            </li>
          </ul>
        </div>

        <div className="bg-blue-500/10 border border-blue-500/20 p-4 rounded-lg mt-6">
          <h3 className="text-blue-400 font-semibold mb-2">
            Configuration Tips:
          </h3>
          <ul className="list-disc ml-6 space-y-2 text-blue-300/90">
            <li>
              {" "}
              Wild card tags (/**) will be automatically added to your private
              routes
            </li>
            <li>
              You can add multiple routes to cover different private sections
            </li>
            <li>Routes are case-sensitive</li>
          </ul>
        </div>
      </section>
      
      </div>
    </div>
  );
}

export default Page;
