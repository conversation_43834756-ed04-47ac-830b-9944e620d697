import mongoose from "mongoose";
import { v4 as uuidv4 } from "uuid";

const websiteSchema = new mongoose.Schema({
  id: {
    type: String,
    required: true,
    unique: true,
    default: () => uuidv4(),
  },
  name: {
    type: String,
    required: true,
  },
  domain: {
    type: String,
    required: true,
    unique: true,
  },
  createdAt: {
    type: Date,
    default: Date.now,
  },
  userId: {
    type: String,
    required: true,
  },
  excludeAuthVisits: {
    type: Boolean,
    default: false,
  },
  privateRoutes: {
    type: [String],
    default: [],
  },
  // Revenue Attribution Fields
  stripeApiKey: {
    type: String,
    required: false,
    select: false, // Don't return in queries by default for security
  },
  stripeWebhookId: {
    type: String,
    required: false,
  },
  stripeWebhookSecret: {
    type: String,
    required: false,
    select: false, // Don't return in queries by default for security
  },
  revenueAttributionEnabled: {
    type: Boolean,
    default: false,
  },
});

export interface Website extends mongoose.Document {
  id: string;
  name: string;
  domain: string;
  createdAt: Date;
  userId: string;
  excludeAuthVisits: boolean;
  privateRoutes: string[];
  stripeApiKey?: string;
  stripeWebhookId?: string;
  stripeWebhookSecret?: string;
  revenueAttributionEnabled: boolean;
}

export default mongoose.models.Website ||
  mongoose.model<Website>("Website", websiteSchema);
