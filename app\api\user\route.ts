import { auth } from "@clerk/nextjs/server";
import { NextRequest, NextResponse } from "next/server";
import connect from "@/util/db";
import User from "@/models/user";

export async function GET(req: NextRequest) {
    try {
        const { userId } = await auth();
        
        if (!userId) {
            return new NextResponse("Unauthorized", { status: 401 });
        }

        await connect();
        
        const user = await User.findOne({ clerkId: userId });
        if (!user) {
            return new NextResponse("User not found", { status: 404 });
        }

        return NextResponse.json({
            accessUntil: user.accessUntil,
            priceId: user.priceId
        });
    } catch (error) {
        console.error("Error fetching user data:", error);
        return new NextResponse("Internal Error", { status: 500 });
    }
} 