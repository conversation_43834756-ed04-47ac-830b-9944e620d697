import { Bar<PERSON>hart3, <PERSON>, Gith<PERSON>, Linkedin } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import React from "react";

function Footer() {
  return (
    <footer className="w-full border-t border-gray-700/50 py-6 md:py-12 bg-customGray">
      <div className="container px-4 md:px-6">
        <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-4">
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <Image src="/icon.png" alt="Versatailor" width={32} height={32} />
              <span className="text-xl font-bold">Versatailor</span>
            </div>
            <p className="text-sm text-gray-300">
              Powerful analytics for SaaS and business owners.
            </p>

          </div>
          <div className="space-y-4">
            <h3 className="text-sm font-medium">PRODUCT</h3>
            <ul className="grid gap-2">
              <li>
                <Link
                  href="#features"
                  className="text-sm text-muted-white hover:text-white"
                >
                  Features
                </Link>
              </li>
              <li>
                <Link
                  href="#pricing"
                  className="text-sm text-muted-white hover:text-white"
                >
                  Pricing
                </Link>
              </li>
              <li>
                <Link
                  href="/docs"
                  className="text-sm text-muted-white hover:text-white"
                >
                  Docs
                </Link>
              </li>
              <li>
                <Link
                  href="/changelog"
                  className="text-sm text-muted-white hover:text-white"
                >
                  Changelog
                </Link>
              </li>
              <li>
                <Link
                  href="mailto:<EMAIL>"
                  className="text-sm text-muted-white hover:text-white"
                >
                  Support
                </Link>
              </li>
            </ul>
          </div>
          <div className="space-y-4">
            <h3 className="text-sm font-medium">LEGAL</h3>
            <ul className="grid gap-2">
              <li>
                <Link
                  href="/tos"
                  className="text-sm text-muted-white hover:text-white"
                >
                  Terms of Service
                </Link>
              </li>
              <li>
                <Link
                  href="/privacy-policy"
                  className="text-sm text-muted-white hover:text-white"
                >
                  Privacy Policy
                </Link>
              </li>

            </ul>
          </div>
          <div className="space-y-4">
            <h3 className="text-sm font-medium">MORE FROM DEVELOPER</h3>
            <ul className="grid gap-2">
              <li>
                <Link target="_blank"
                  href="https://apply-nexus.com/?ref=versatailor"
                  className="text-sm text-muted-white hover:text-white"
                >
                  Apply Nexus
                </Link>
              </li>
              <li>
                <Link target="_blank"
                  href="https://ziadhussein.com/?ref=versatailor"
                  className="text-sm text-muted-white hover:text-white"
                >
                  Portfolio
                </Link>
              </li>
              <li>
                <Link target="_blank"
                  href="https://minimado.com/?ref=versatailor"
                  className="text-sm text-muted-white hover:text-white"
                >
                  Minimado
                </Link>
              </li>
            </ul>
          </div>
        </div>
        <div className="mt-4 text-center text-sm text-muted-white">
          &copy; {new Date().getFullYear()} Versatailor. All rights
          reserved.
        </div>
      </div>
    </footer>
  );
}

export default Footer;
