import React, { Dispatch, RefObject, SetStateAction } from "react";
import WebsiteIcon from "./WebsiteIcon";
import { useRouter } from "next/navigation";
import { Website } from "@/lib/types";

type WebsiteDropdownProps = {
  dropdownRef: RefObject<HTMLDivElement | null>;
  setIsDropdownOpen: Dispatch<SetStateAction<boolean>>;
  isDropdownOpen: boolean;
  website: Website;
  websites: Website[];
};

function WebsiteDropdown({
  dropdownRef,
  setIsDropdownOpen,
  isDropdownOpen,
  website,
  websites,
}: WebsiteDropdownProps) {
  const router = useRouter();

  // Sort websites by createdAt date in ascending order
  const sortedWebsites = [...websites].sort(
    (a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
  );

  return (
    <div className="relative" ref={dropdownRef}>
      <button
        onClick={() => setIsDropdownOpen(!isDropdownOpen)}
        className="text-gray-400 flex items-center gap-1 sm:gap-2 hover:text-white transition-colors group px-2 sm:px-3 py-1.5 rounded-lg text-xs sm:text-sm"
      >
        <WebsiteIcon domain={website.domain} name={website.name} size={16} className="flex-shrink-0" />
        <span className="group-hover:text-white truncate max-w-[100px] sm:max-w-[200px] text-md">{website.domain}</span>
        <svg
          className={`w-3 h-3 sm:w-4 sm:h-4 flex-shrink-0 transition-transform ${isDropdownOpen ? "rotate-180" : ""
            }`}
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M19 9l-7 7-7-7"
          />
        </svg>
      </button>

      {isDropdownOpen && (
        <div className="absolute top-full left-0 mt-1 w-64 bg-[#363636] rounded-lg border border-gray-700/50 shadow-lg py-1 z-10 max-w-[calc(100vw-2rem)]">

          {/* Site Settings Option */}
          <button
            onClick={() => {
              router.push(`/settings/${website.id}`);
              setIsDropdownOpen(false);
            }}
            className="w-full px-4 py-2.5 text-left flex items-center gap-3 hover:bg-[#404040] transition-colors text-gray-400"
          >
            <svg
              className="w-5 h-5 flex-shrink-0"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"
              />
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
              />
            </svg>
            <span>Site Settings</span>
          </button>

          {/* Divider */}
          <div className="border-t border-gray-700/50 my-1"></div>


          {sortedWebsites.map((site) => (
            <button
              key={site.id}
              onClick={() => {
                router.push(`/dashboard/${site.id}`);
                setIsDropdownOpen(false);
              }}
              className={`w-full px-4 py-2.5 text-left flex items-center gap-3 hover:bg-[#404040] transition-colors ${site.id === website.id
                  ? "bg-[#404040] text-white"
                  : "text-gray-400"
                }`}
            >
              <WebsiteIcon domain={site.domain} name={site.name} size={20} className="flex-shrink-0" />
              <div className="flex flex-col overflow-hidden">
                <span className="text-sm font-medium truncate">{site.name}</span>
                <span className="text-xs text-gray-500 truncate">{site.domain}</span>
              </div>
            </button>
          ))}



        </div>
      )}
    </div>
  );
}
export default WebsiteDropdown;
