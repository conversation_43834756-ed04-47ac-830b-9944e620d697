import { NextRequest, NextResponse } from "next/server";
import { auth } from "@clerk/nextjs/server";
import connect from "@/util/db";
import Website from "@/models/website";
import { 
  validateStripeApi<PERSON>ey, 
  createWebhookEndpoint, 
  deleteWebhookEndpoint,
  testWebhookConnection,
  encryptApiKey,
  decryptApiKey
} from "@/lib/stripe-utils";

export async function POST(request: NextRequest) {
  try {
    console.log("=== Webhook setup API called ===");
    await connect();

    const { userId } = await auth();
    console.log("User ID:", userId);
    if (!userId) {
      console.log("No user ID found, returning 401");
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();
    console.log("Request body:", JSON.stringify(body, null, 2));
    
    const { websiteId, stripeApiKey, action = "setup" } = body;
    console.log("Extracted values:", { websiteId, stripeApiKeyPresent: !!stripeApiKey, action });

    if (!websiteId) {
      console.log("No websiteId provided, returning 400");
      return NextResponse.json(
        { error: "Website ID is required" },
        { status: 400 }
      );
    }

    // Find the website and verify ownership
    console.log("Looking for website with ID:", websiteId, "and userId:", userId);
    const website = await Website.findOne({ 
      id: websiteId, 
      userId 
    }).select("+stripeApiKey +stripeWebhookSecret");

    console.log("Website found:", !!website);
    if (!website) {
      console.log("Website not found, returning 404");
      return NextResponse.json(
        { error: "Website not found" },
        { status: 404 }
      );
    }

    console.log("Processing action:", action);
    if (action === "setup") {
      return await handleWebhookSetup(website, stripeApiKey);
    } else if (action === "disable") {
      return await handleWebhookDisable(website);
    } else if (action === "test") {
      return await handleWebhookTest(website);
    } else {
      console.log("Invalid action provided:", action);
      return NextResponse.json(
        { error: "Invalid action" },
        { status: 400 }
      );
    }

  } catch (error: any) {
    console.error("Webhook setup error:", error);
    console.error("Error stack:", error.stack);
    console.error("Error details:", {
      message: error.message,
      type: error.type,
      code: error.code,
      name: error.name
    });
    return NextResponse.json(
      { error: `Internal server error: ${error.message || 'Unknown error'}` },
      { status: 500 }
    );
  }
}

async function handleWebhookSetup(website: any, stripeApiKey: string) {
  console.log("=== handleWebhookSetup called ===");
  console.log("stripeApiKey provided:", !!stripeApiKey);
  console.log("stripeApiKey length:", stripeApiKey?.length || 0);
  
  if (!stripeApiKey) {
    console.log("No stripe API key provided, returning 400");
    return NextResponse.json(
      { error: "Stripe API key is required" },
      { status: 400 }
    );
  }

  try {
    console.log("Validating Stripe API key...");
    // Validate the Stripe API key
    const validation = await validateStripeApiKey(stripeApiKey);
    console.log("Validation result:", validation);
    
    if (!validation.isValid || !validation.hasRequiredPermissions) {
      console.log("API key validation failed:", validation.error);
      return NextResponse.json(
        { error: validation.error || "Invalid Stripe API key or insufficient permissions" },
        { status: 400 }
      );
    }

    console.log("API key validation successful, proceeding with webhook setup...");

    // If there's an existing webhook, delete it first
    if (website.stripeWebhookId && website.stripeApiKey) {
      try {
        const existingDecryptedKey = decryptApiKey(website.stripeApiKey);
        await deleteWebhookEndpoint(existingDecryptedKey, website.stripeWebhookId);
      } catch (error) {
        console.log("Failed to delete existing webhook, continuing with setup");
      }
    }

    // Create new webhook endpoint
    const webhookResult = await createWebhookEndpoint(stripeApiKey, website.id);
    if (!webhookResult.success || !webhookResult.webhookEndpoint) {
      return NextResponse.json(
        { error: webhookResult.error || "Failed to create webhook endpoint" },
        { status: 400 }
      );
    }

    // Encrypt and store the API key and webhook details
    let encryptedApiKey;
    try {
      encryptedApiKey = encryptApiKey(stripeApiKey);
    } catch (encryptionError: any) {
      console.error("Encryption error:", encryptionError);
      return NextResponse.json(
        { error: `Failed to encrypt API key: ${encryptionError.message}` },
        { status: 500 }
      );
    }
    
    website.stripeApiKey = encryptedApiKey;
    website.stripeWebhookId = webhookResult.webhookEndpoint.id;
    website.stripeWebhookSecret = webhookResult.webhookEndpoint.secret;
    website.revenueAttributionEnabled = true;
    
    await website.save();

    return NextResponse.json({
      success: true,
      message: "Revenue attribution enabled successfully",
      webhookId: webhookResult.webhookEndpoint.id,
      webhookUrl: webhookResult.webhookEndpoint.url,
    });

  } catch (error: any) {
    console.error("handleWebhookSetup error:", error);
    return NextResponse.json(
      { error: `Webhook setup failed: ${error.message || 'Unknown error'}` },
      { status: 500 }
    );
  }
}

async function handleWebhookDisable(website: any) {
  // Delete webhook from Stripe if it exists
  if (website.stripeWebhookId && website.stripeApiKey) {
    try {
      const decryptedApiKey = decryptApiKey(website.stripeApiKey);
      const deleteResult = await deleteWebhookEndpoint(decryptedApiKey, website.stripeWebhookId);
      
      if (!deleteResult.success) {
        console.error("Failed to delete webhook from Stripe:", deleteResult.error);
        // Continue anyway to clean up local data
      }
    } catch (error) {
      console.error("Error deleting webhook:", error);
      // Continue anyway to clean up local data
    }
  }

  // Clear webhook configuration from website
  website.stripeApiKey = undefined;
  website.stripeWebhookId = undefined;
  website.stripeWebhookSecret = undefined;
  website.revenueAttributionEnabled = false;
  
  await website.save();

  return NextResponse.json({
    success: true,
    message: "Revenue attribution disabled successfully",
  });
}

async function handleWebhookTest(website: any) {
  if (!website.stripeWebhookId || !website.stripeApiKey) {
    return NextResponse.json(
      { error: "No webhook configured" },
      { status: 400 }
    );
  }

  try {
    const decryptedApiKey = decryptApiKey(website.stripeApiKey);
    const testResult = await testWebhookConnection(decryptedApiKey, website.stripeWebhookId);
    
    return NextResponse.json({
      success: true,
      isActive: testResult.isActive,
      error: testResult.error,
    });
  } catch (error: any) {
    return NextResponse.json(
      { error: error.message || "Failed to test webhook connection" },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    await connect();

    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const websiteId = searchParams.get("websiteId");

    if (!websiteId) {
      return NextResponse.json(
        { error: "Website ID is required" },
        { status: 400 }
      );
    }

    // Get webhook status for the website
    const website = await Website.findOne({ 
      id: websiteId, 
      userId 
    });

    if (!website) {
      return NextResponse.json(
        { error: "Website not found" },
        { status: 404 }
      );
    }

    return NextResponse.json({
      isEnabled: website.revenueAttributionEnabled || false,
      hasWebhook: !!(website.stripeWebhookId),
      webhookId: website.stripeWebhookId || null,
    });

  } catch (error: any) {
    console.error("Webhook status error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
} 