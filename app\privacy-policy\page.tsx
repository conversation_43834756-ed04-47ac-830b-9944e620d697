import Link from "next/link";
import config from "@/config";
import { getSEOTags } from "@/lib/seo";

// CHATGPT PROMPT TO GENERATE YOUR PRIVACY POLICY — replace with your own data 👇

// 1. Go to https://chat.openai.com/
// 2. Copy paste bellow
// 3. Replace the data with your own (if needed)
// 4. Paste the answer from ChatGPT directly in the <pre> tag below

// You are an excellent lawyer.

// I need your help to write a simple privacy policy for my website. Here is some context:
// - Website: https://shipfa.st
// - Name: ShipFast
// - Description: A JavaScript code boilerplate to help entrepreneurs launch their startups faster
// - User data collected: name, email and payment information
// - Non-personal data collection: web cookies
// - Purpose of Data Collection: Order processing
// - Data sharing: we do not share the data with any other parties
// - Children's Privacy: we do not collect any data from children
// - Updates to the Privacy Policy: users will be updated by email
// - Contact information: <EMAIL>

// Please write a simple privacy policy for my site. Add the current date.  Do not add or explain your reasoning. Answer:

export const metadata = getSEOTags({
  title: `Privacy Policy | ${config.appName}`,
  canonicalUrlRelative: "/privacy-policy",
});

const PrivacyPolicy = () => {
  return (
    <main className="max-w-xl mx-auto">
      <div className="p-5">
        <Link href="/" className="btn btn-ghost">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 20 20"
            fill="currentColor"
            className="w-5 h-5"
          >
            <path
              fillRule="evenodd"
              d="M15 10a.75.75 0 01-.75.75H7.612l2.158 1.96a.75.75 0 11-1.04 1.08l-3.5-3.25a.75.75 0 010-1.08l3.5-3.25a.75.75 0 111.04 1.08L7.612 9.25h6.638A.75.75 0 0115 10z"
              clipRule="evenodd"
            />
          </svg>{" "}
          Back
        </Link>
        <h1 className="text-3xl font-extrabold pb-6">
          Privacy Policy for {config.appName}
        </h1>

        <pre
          className="leading-relaxed whitespace-pre-wrap"
          style={{ fontFamily: "sans-serif" }}
        >
          {`
          
Privacy Policy
Effective Date: April 2, 2025

At Versatailor (accessible at https://versatailor.com), we value your privacy and are committed to protecting your personal information. This Privacy Policy outlines the types of data we collect and how it is used.

Information We Collect
Personal Information:
We collect your name, email address, and payment information solely for order processing and to provide our services efficiently.

Non-Personal Information:
Our website collects web cookies to gather non-personal data for analytics and service improvement.

Use of Your Information
The information we collect is used only for processing orders and enhancing our service delivery. We do not share, sell, or trade your personal information with any third parties.

Children's Privacy
We do not knowingly collect any information from children. If it is discovered that data has been inadvertently collected from a child, appropriate measures will be taken promptly.

Updates to This Privacy Policy
We may update this Privacy Policy from time to time. In the event of significant changes, we will notify all users by email.

Contact Us
If you have any questions or concerns regarding this Privacy Policy, please contact us at:
Email: <EMAIL>

By using our website, you acknowledge and agree to the practices described in this Privacy Policy.

`}
        </pre>
      </div>
    </main>
  );
};

export default PrivacyPolicy;
