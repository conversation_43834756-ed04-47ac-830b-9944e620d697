import React, { RefObject, useState } from "react";
import { useFilterState } from "@/hooks/useFilterState";
import { GroupedReferrers } from "@/hooks/useGroupedData";
import { TopReferrer } from "@/lib/types";
import { getFriendlyReferrerName } from "@/lib/utils"; // Import shared function

type ReferrerModalProps = {
  referrerModalRef: RefObject<HTMLDivElement | null>;
  topReferrers: TopReferrer[];
  maxReferrerVisitors: number;
  groupedReferrers: GroupedReferrers;
  setShowReferrerModal: (show: boolean) => void;
  websiteDomain?: string;
};

function ReferrerModal({
  referrerModalRef,
  topReferrers,
  maxReferrerVisitors,
  groupedReferrers,
  setShowReferrerModal,
  websiteDomain,
}: ReferrerModalProps) {
  const { handleFilterClick, activeFilters } = useFilterState();
  const [referrerView, setReferrerView] = useState<
    "referrer" | "campaign" | "utm"
  >("referrer");

  const handleReferrerFilterClick = (
    type: "referrer" | "campaign" | "utm",
    value: string,
    label: string
  ) => {
    // First apply the filter
    handleFilterClick(type, value, label);
    // Then close the modal
    setTimeout(() => setShowReferrerModal(false), 100);
  };

  return (
    <div 
      className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4 overflow-hidden"
      onClick={() => setShowReferrerModal(false)}
    >
      <div
        ref={referrerModalRef}
        className="bg-[#2A2A2A] rounded-lg w-full max-w-2xl max-h-[80vh] overflow-hidden flex flex-col"
        onClick={(e) => e.stopPropagation()}
      >
        <div className="flex items-center justify-between p-4 border-b border-gray-700">
          <h2 className="text-lg font-semibold text-white">Referrers</h2>
          <button
            onClick={() => setShowReferrerModal(false)}
            className="text-gray-400 hover:text-white"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-5 w-5"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fillRule="evenodd"
                d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                clipRule="evenodd"
              />
            </svg>
          </button>
        </div>
        <div className="flex justify-center p-2 border-b border-gray-700">
          <div className="flex rounded-lg p-1">
            <button
              onClick={() => setReferrerView("referrer")}
              className={`px-2 py-1 rounded-md text-xs sm:text-sm transition-colors ${
                referrerView === "referrer"
                  ? "bg-blue-500 text-white"
                  : "text-gray-400 hover:text-white"
              }`}
            >
              Referrer
            </button>
            <button
              onClick={() => setReferrerView("campaign")}
              className={`px-2 py-1 rounded-md text-xs sm:text-sm transition-colors ${
                referrerView === "campaign"
                  ? "bg-blue-500 text-white"
                  : "text-gray-400 hover:text-white"
              }`}
            >
              Campaign
            </button>
            <button
              onClick={() => setReferrerView("utm")}
              className={`px-2 py-1 rounded-md text-xs sm:text-sm transition-colors ${
                referrerView === "utm"
                  ? "bg-blue-500 text-white"
                  : "text-gray-400 hover:text-white"
              }`}
            >
              UTM
            </button>
          </div>
        </div>
        <div className="overflow-y-auto p-4 flex-grow">
          <div className="space-y-1">
            {referrerView === "referrer" &&
              topReferrers
                .filter(referrer => {
                  // Only filter out Google Accounts referrers
                  const isGoogleAccounts = referrer.url && referrer.url.includes('accounts.google.com');
                  return !isGoogleAccounts;
                })
                // First convert to displayReferrers
                .map((referrer: TopReferrer) => {
                  let displayReferrer = {...referrer};
                  if (!referrer.url || referrer.url === '') {
                    displayReferrer.url = null; // This will make it show as Direct/None
                  } else if (referrer.url) {
                    // Check for same domain or website domain to mark as Direct/None
                    let isSameDomain = false;
                    if (typeof window !== 'undefined') {
                      try {
                        // Correct: Compare hostnames
                        const referrerHostname = new URL(referrer.url.startsWith('http') ? referrer.url : `https://${referrer.url}`).hostname;
                        const currentHostname = window.location.hostname;
                        isSameDomain = referrerHostname === currentHostname;
                      } catch (e) {
                        console.error('Error parsing or comparing domains:', e);
                        // Fallback or default behavior if URL parsing fails
                        isSameDomain = false;
                      }
                    }
                    const isWebsiteDomain = websiteDomain && referrer.url.includes(websiteDomain);
                    const isGoogleAccounts = referrer.url.includes('accounts.google.com');

                    // If there's a campaign but no referral, mark as Direct/None in referrer tab
                    const hasCampaign = referrer.referralParams?.ref || referrer.referralParams?.campaign || referrer.referralParams?.utm_campaign;
                    // const hasReferral = referrer.referralParams?.referral; // Removed unclear 'referral' check

                    // Corrected: Only nullify for Google Accounts, Same Domain, or Website Domain
                    if (isGoogleAccounts || isSameDomain || isWebsiteDomain) {
                      displayReferrer.url = null; // This will make it show as Direct/None
                    } else {
                      // Normalize the URL format for consistent grouping
                      try {
                        const url = new URL(referrer.url.startsWith('http') ? referrer.url : `https://${referrer.url}`);
                        // Store just the hostname for grouping purposes
                        displayReferrer.normalizedDomain = url.hostname.replace(/^www\./, "");
                      } catch (e) {
                        // If URL parsing fails, try a simpler normalization
                        displayReferrer.normalizedDomain = referrer.url.replace(/^(https?:\/\/)?(www\.)?/, "").split("/")[0];
                      }
                    }
                  }
                  return displayReferrer;
                })
                // Then group and combine entries with the same domain
                .reduce((acc: TopReferrer[], curr: TopReferrer) => {
                  if (!curr.url) {
                    // This is a Direct/None entry
                    const existingDirectNone = acc.find(r => !r.url);
                    if (existingDirectNone) {
                      // Add to existing Direct/None
                      existingDirectNone.visitors += curr.visitors;
                      return acc;
                    }
                  } else if (curr.normalizedDomain) {
                    // Look for existing entry with the same normalized domain
                    const existingDomain = acc.find(r => 
                      r.normalizedDomain && r.normalizedDomain === curr.normalizedDomain);
                    
                    if (existingDomain) {
                      // Add visitors to the existing domain entry
                      existingDomain.visitors += curr.visitors;
                      
                      // Merge referral params if needed
                      if (curr.referralParams) {
                        existingDomain.referralParams = {
                          ...existingDomain.referralParams,
                          ...curr.referralParams
                        };
                      }
                      return acc;
                    }
                  }
                  // Add as new entry
                  acc.push(curr);
                  return acc;
                }, [])
                .map((displayReferrer: TopReferrer, i) => {
                  return (
                <div
                  key={i}
                  className={`flex items-center justify-between p-1 sm:p-2 rounded text-xs sm:text-sm relative overflow-hidden group cursor-pointer ${
                    activeFilters.some(
                      (filter) =>
                        filter.type === "referrer" && 
                        ((!displayReferrer.url && filter.value === "direct") || 
                         (displayReferrer.normalizedDomain && 
                          filter.value === displayReferrer.normalizedDomain))
                    )
                      ? "bg-blue-500/20"
                      : ""
                  }`}
                  onClick={() => {
                    const cleanedUrl = displayReferrer.url 
                      ? displayReferrer.url.replace(/^(https?:\/\/)?(www\.)?/, "").replace(/\/$/, "") 
                      : "Direct/None";
                    // Use normalized domain for filtering if available, otherwise fall back to URL
                    const filterValue = !displayReferrer.url 
                      ? "direct" 
                      : displayReferrer.normalizedDomain || displayReferrer.url;
                    
                    handleReferrerFilterClick(
                      "referrer",
                      filterValue,
                      getFriendlyReferrerName(displayReferrer.url) || cleanedUrl
                    );
                  }}
                >
                  <div
                    className="absolute inset-0 bg-blue-500/40 transition-transform origin-left group-hover:bg-blue-500/20"
                    style={{
                      transform: `scaleX(${
                        displayReferrer.visitors / maxReferrerVisitors
                      })`,
                    }}
                  />
                  <span className="text-white flex items-center gap-2 min-w-0 relative">
                    {displayReferrer.url ? (() => {
                      // Calculate these only when url is definitely a string
                      const friendlyName = getFriendlyReferrerName(displayReferrer.url);
                      const displayName = friendlyName || displayReferrer.url.replace(/^(https?:\/\/)?(www\.)?/, "").replace(/\/$/, "");
                      const faviconDomain = friendlyName === "X" ? "x.com" : displayReferrer.url;

                      return (
                        <>
                          <img
                            src={`https://www.google.com/s2/favicons?domain=${faviconDomain}&sz=32`}
                            alt=""
                            className="w-4 h-4 flex-shrink-0"
                            onError={(e) => { // Keep only the outer onError
                              const target = e.target as HTMLImageElement;
                              target.onerror = null; // Prevent infinite loop if fallback fails
                              target.src = "https://icons.duckduckgo.com/ip3/null.ico";
                            }}
                          />
                          <span className="truncate">
                            {displayName}
                            {displayReferrer.referralParams?.referral && (
                              <span className="text-blue-400 ml-1">
                                ({displayReferrer.referralParams.referral})
                              </span>
                            )}
                          </span>
                        </>
                      );
                    })() : (
                      <>
                        <img
                          src="https://icons.duckduckgo.com/ip3/null.ico"
                          height={16}
                          width={16}
                          className="flex-shrink-0"
                        />
                        <span className="truncate">Direct/None</span>
                      </>
                    )}
                  </span>
                  <span className="text-white ml-2 flex-shrink-0 relative">
                    {displayReferrer.visitors}
                  </span>
                </div>
              );
            })}
            {referrerView === "campaign" &&
              groupedReferrers.campaign.map((referrer, i) => (
                <div
                  key={i}
                  className={`flex items-center justify-between p-1 sm:p-2 rounded text-xs sm:text-sm relative overflow-hidden group cursor-pointer ${
                    activeFilters.some(
                      (filter) =>
                        filter.type === "campaign" &&
                        filter.value === referrer.campaign
                    )
                      ? "bg-blue-500/20"
                      : ""
                  }`}
                  onClick={() =>
                    handleReferrerFilterClick(
                      "campaign",
                      referrer.campaign,
                      referrer.campaign
                    )
                  }
                >
                  <div
                    className="absolute inset-0 bg-blue-500/40 transition-transform origin-left group-hover:bg-blue-500/20"
                    style={{
                      transform: `scaleX(${
                        referrer.visitors / maxReferrerVisitors
                      })`,
                    }}
                  />
                  <span className="text-white flex items-center gap-2 min-w-0 relative">
                    <svg
                      className="w-4 h-4"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M11 4a2 2 0 114 0v1a1 1 0 001 1h3a1 1 0 011 1v3a1 1 0 01-1 1h-1a2 2 0 100 4h1a1 1 0 011 1v3a1 1 0 01-1 1h-3a1 1 0 01-1-1v-1a2 2 0 10-4 0v1a1 1 0 01-1 1H7a1 1 0 01-1-1v-3a1 1 0 00-1-1H4a2 2 0 110-4h1a1 1 0 001-1V7a1 1 0 011-1h3a1 1 0 001-1V4z"
                      />
                    </svg>
                    <span className="truncate">{referrer.campaign}</span>
                  </span>
                  <span className="text-white ml-2 flex-shrink-0 relative">
                    {referrer.visitors}
                  </span>
                </div>
              ))}
            {referrerView === "utm" &&
              groupedReferrers.utm.map((referrer, i) => (
                <div
                  key={i}
                  className={`flex items-center justify-between p-1 sm:p-2 rounded text-xs sm:text-sm relative overflow-hidden group cursor-pointer ${
                    activeFilters.some(
                      (filter) =>
                        filter.type === "utm" &&
                        filter.value ===
                          `${referrer.source}${
                            referrer.medium ? ` / ${referrer.medium}` : ""
                          }`
                    )
                      ? "bg-blue-500/20"
                      : ""
                  }`}
                  onClick={() =>
                    handleReferrerFilterClick(
                      "utm",
                      `${referrer.source}${
                        referrer.medium ? ` / ${referrer.medium}` : ""
                      }`,
                      referrer.source
                        ? `${referrer.source}${
                            referrer.medium ? ` / ${referrer.medium}` : ""
                          }`
                        : "No UTM"
                    )
                  }
                >
                  <div
                    className="absolute inset-0 bg-blue-500/40 transition-transform origin-left group-hover:bg-blue-500/20"
                    style={{
                      transform: `scaleX(${
                        referrer.visitors / maxReferrerVisitors
                      })`,
                    }}
                  />
                  <span className="text-white flex items-center gap-2 min-w-0 relative">
                    <svg
                      className="w-4 h-4"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M13 10V3L4 14h7v7l9-11h-7z"
                      />
                    </svg>
                    <span className="truncate">
                      {referrer.source
                        ? `${referrer.source}${
                            referrer.medium ? ` / ${referrer.medium}` : ""
                          }`
                        : "No UTM"}
                    </span>
                  </span>
                  <span className="text-white ml-2 flex-shrink-0 relative">
                    {referrer.visitors}
                  </span>
                </div>
              ))}
          </div>
        </div>
      </div>
    </div>
  );
}

export default ReferrerModal;
