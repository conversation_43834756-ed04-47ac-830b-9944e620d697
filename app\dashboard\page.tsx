"use client";
import { useState, useEffect, useCallback } from "react";
import { useRouter } from "next/navigation";
import { AreaChart, Area, ResponsiveContainer } from "recharts";
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
  DragStartEvent,
  DragOverlay,
} from "@dnd-kit/core";
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  useSortable,
  verticalListSortingStrategy, // Or another strategy if needed
  rectSortingStrategy,
} from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import WebsiteIcon from "@/components/WebsiteIcon";
import { DashboardSkeleton } from "@/components/Skeletons";
import { useClerk } from "@clerk/nextjs";
import { Event } from "@/lib/types"; // Import Event type

interface Website {
  id: string;
  name: string;
  domain: string;
  createdAt: string;
  visitorCount24h?: number;
  visitorCount48h?: number;
  hourlyData?: { time: string; visitors: number }[];
  trend?: {
    percentage: number;
    isPositive: boolean;
  };
}

interface FormState {
  isSubmitting: boolean;
  error: string | null;
}

// Add Modal Component
const Modal = ({
  isOpen,
  onClose,
  children,
}: {
  isOpen: boolean;
  onClose: () => void;
  children: React.ReactNode;
}) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4 overflow-hidden">
      <div
        className="bg-customGrayLight rounded-lg w-full max-w-2xl border border-gray-700/50 shadow-xl overflow-hidden"
        onClick={(e) => e.stopPropagation()}
      >
        {children}
      </div>
      {/* Backdrop click to close */}
      <div className="absolute inset-0 -z-10" onClick={onClose} />
    </div>
  );
};

// Add Website Form Component
const AddWebsiteForm = ({
  onSubmit,
  onClose,
  formState,
}: {
  onSubmit: (name: string, domain: string, revenueAttribution?: { enabled: boolean; stripeApiKey?: string }) => Promise<void>;
  onClose: () => void;
  formState: FormState;
}) => {
  const [name, setName] = useState("");
  const [domain, setDomain] = useState("");
  const [domainError, setDomainError] = useState<string | null>(null);
  const [revenueAttributionEnabled, setRevenueAttributionEnabled] = useState(false);
  const [stripeApiKey, setStripeApiKey] = useState("");
  const [stripeKeyError, setStripeKeyError] = useState<string | null>(null);

  // Add function to clean domain
  const cleanDomain = (domain: string) => {
    return domain
      .toLowerCase()
      .replace(/^(https?:\/\/)?(www\.)?/i, "") // Remove http://, https://, and www.
      .replace(/\/$/, ""); // Remove trailing slash
  };

  // Add function to generate name from domain
  const generateNameFromDomain = (domain: string) => {
    if (!domain) return "";
    const cleanedDomain = cleanDomain(domain);
    // Remove common TLDs and special characters, capitalize first letter
    return cleanedDomain
      .replace(/\.(com|org|net|io|dev|co|uk|edu|gov)$/i, "")
      .replace(/[^a-zA-Z0-9]/g, " ")
      .trim()
      .split(" ")
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(" ");
  };

  // Add function to generate name from domain
  const validateDomain = (domain: string): boolean => {
    // Basic domain regex pattern
    const domainPattern =
      /^([a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?\.)+[a-zA-Z]{2,}$/;
    const cleanedDomain = cleanDomain(domain);

    if (!cleanedDomain) {
      setDomainError("Domain is required");
      return false;
    }

    if (!domainPattern.test(cleanedDomain)) {
      setDomainError("Please enter a valid domain (e.g., example.com)");
      return false;
    }

    setDomainError(null);
    return true;
  };

  // Validate Stripe API key format
  const validateStripeApiKey = (apiKey: string): boolean => {
    if (!apiKey.trim()) {
      setStripeKeyError("Stripe API key is required when revenue attribution is enabled");
      return false;
    }

    if (!apiKey.startsWith("rk_")) {
      setStripeKeyError("Please use a restricted API key (starts with rk_)");
      return false;
    }

    setStripeKeyError(null);
    return true;
  };

  // Update domain change handler
  const handleDomainChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newDomain = e.target.value;
    setDomain(newDomain);
    // Only update name if it hasn't been manually edited
    if (name === generateNameFromDomain(domain)) {
      setName(generateNameFromDomain(newDomain));
    }
    // Validate domain on change
    validateDomain(newDomain);
  };

  // Handle Stripe API key change
  const handleStripeApiKeyChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newKey = e.target.value;
    setStripeApiKey(newKey);
    if (revenueAttributionEnabled && newKey) {
      validateStripeApiKey(newKey);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    const cleanedDomain = cleanDomain(domain);

    // Validate domain before submission
    if (!validateDomain(cleanedDomain)) {
      return;
    }

    // Validate Stripe API key if revenue attribution is enabled
    if (revenueAttributionEnabled && !validateStripeApiKey(stripeApiKey)) {
      return;
    }

    const revenueAttribution = revenueAttributionEnabled ? {
      enabled: true,
      stripeApiKey: stripeApiKey.trim()
    } : { enabled: false };

    await onSubmit(name, cleanedDomain, revenueAttribution);
    if (!formState.error) {
      setName("");
      setDomain("");
      setDomainError(null);
      setRevenueAttributionEnabled(false);
      setStripeApiKey("");
      setStripeKeyError(null);
    }
  };

  return (
    <div className="p-4 sm:p-6">
      <div className="flex items-center justify-between mb-4 sm:mb-6">
        <h2 className="text-lg sm:text-xl font-semibold flex items-center gap-2">
          <svg
            className="w-5 h-5 text-blue-500"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 6v6m0 0v6m0-6h6m-6 0H6"
            />
          </svg>
          Add Website
        </h2>
        <button
          onClick={onClose}
          className="text-gray-400 hover:text-white transition-colors"
          aria-label="Close modal"
        >
          <svg
            className="w-5 h-5"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M6 18L18 6M6 6l12 12"
            />
          </svg>
        </button>
      </div>
      <form onSubmit={handleSubmit} className="space-y-4">
        <div className="space-y-4">
          <div>
            <label
              htmlFor="domain"
              className="block text-sm font-medium text-gray-400 mb-1"
            >
              Domain
            </label>
            <div className="flex flex-nowrap items-stretch">
              <span className="inline-flex items-center px-3 rounded-l bg-customGrayLight border border-r-0 border-gray-700/50 text-gray-400 text-sm">
                https://
              </span>
              <input
                id="domain"
                type="text"
                value={domain}
                onChange={handleDomainChange}
                className={`flex-1 min-w-0 bg-[#363636] rounded-r px-3 py-2 text-white border border-gray-700/50 focus:border-blue-500/50 focus:ring-2 focus:ring-blue-500/20 transition-all duration-200 ${
                  domainError ? "border-red-500/50" : ""
                }`}
                placeholder="example.com"
                required
                disabled={formState.isSubmitting}
              />
            </div>
            {domainError ? (
              <p className="text-sm text-red-500 mt-1">{domainError}</p>
            ) : (
              <p className="text-sm text-gray-400 mt-1">
                Enter the domain without http://, https://, or www.
              </p>
            )}
          </div>

          <div>
            <label
              htmlFor="name"
              className="block text-sm font-medium text-gray-400 mb-1"
            >
              Name
            </label>
            <input
              id="name"
              type="text"
              value={name}
              onChange={(e) => setName(e.target.value)}
              className="w-full bg-[#363636] rounded px-3 py-2 text-white border border-gray-700/50 focus:border-blue-500/50 focus:ring-2 focus:ring-blue-500/20 transition-all duration-200"
              placeholder="My Website"
              required
              disabled={formState.isSubmitting}
            />
          </div>

          {/* Revenue Attribution Section */}
          <div className="border-t border-gray-700/50 pt-4">
            <div className="flex items-start gap-3">
              <input
                id="revenueAttribution"
                type="checkbox"
                checked={revenueAttributionEnabled}
                onChange={(e) => {
                  setRevenueAttributionEnabled(e.target.checked);
                  if (!e.target.checked) {
                    setStripeApiKey("");
                    setStripeKeyError(null);
                  }
                }}
                className="mt-1 w-4 h-4 text-blue-600 bg-gray-700 border-gray-600 rounded focus:ring-blue-500 focus:ring-2"
                disabled={formState.isSubmitting}
              />
              <div className="flex-1">
                <label
                  htmlFor="revenueAttribution"
                  className="block text-sm font-medium text-gray-300 mb-1 cursor-pointer"
                >
                  Enable Revenue Attribution
                </label>
                <p className="text-xs text-gray-400 mb-3">
                  Track revenue and conversions from your Stripe payments. Requires a Stripe restricted API key.
                </p>

                {revenueAttributionEnabled && (
                  <div className="space-y-3">
                    <div>
                      <label
                        htmlFor="stripeApiKey"
                        className="block text-sm font-medium text-gray-400 mb-1"
                      >
                        Stripe Restricted API Key
                      </label>
                      <input
                        id="stripeApiKey"
                        type="password"
                        value={stripeApiKey}
                        onChange={handleStripeApiKeyChange}
                        className={`w-full bg-[#363636] rounded px-3 py-2 text-white border border-gray-700/50 focus:border-blue-500/50 focus:ring-2 focus:ring-blue-500/20 transition-all duration-200 ${
                          stripeKeyError ? "border-red-500/50" : ""
                        }`}
                        placeholder="rk_test_... or rk_live_..."
                        disabled={formState.isSubmitting}
                      />
                      {stripeKeyError ? (
                        <p className="text-sm text-red-500 mt-1">{stripeKeyError}</p>
                      ) : (
                        <div className="text-xs text-gray-400 mt-1">
                          <p className="mb-1">Create a restricted API key with these permissions:</p>
                          <ul className="text-xs space-y-0.5 ml-4 list-disc">
                            <li>Read access to charges, customers, checkout sessions</li>
                            <li>Write access to webhook endpoints</li>
                          </ul>
                          <a 
                            href="https://dashboard.stripe.com/apikeys/create?name=VersaTailor&permissions%5B%5D=rak_charge_read&permissions%5B%5D=rak_customer_read&permissions%5B%5D=rak_checkout_session_read&permissions%5B%5D=rak_webhook_write&permissions%5B%5D=rak_payment_intent_read"
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-blue-400 hover:text-blue-300 underline"
                          >
                            Create restricted API key →
                          </a>
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        <div className="flex flex-col-reverse sm:flex-row items-center justify-between gap-4 pt-2">
          <div className="w-full sm:w-auto">
            {formState.error && (
              <div className="text-red-500 text-sm flex items-center gap-2 font-bold">
                <svg
                  className="w-4 h-4"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
                {formState.error}
              </div>
            )}
          </div>
          <button
            type="submit"
            disabled={formState.isSubmitting}
            className="w-full sm:w-auto bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 focus:ring-2 focus:ring-blue-500/50 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2"
          >
            {formState.isSubmitting ? (
              <>
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                <span>Adding...</span>
              </>
            ) : (
              "Add Website"
            )}
          </button>
        </div>
      </form>
    </div>
  );
};

// Add Script Modal Component
const ScriptModal = ({
  isOpen,
  onClose,
  website,
}: {
  isOpen: boolean;
  onClose: () => void;
  website: Website;
}) => {
  const [copied, setCopied] = useState(false);
  const [validationStatus, setValidationStatus] = useState<{
    isLoading: boolean;
    result: null | {
      isValid: boolean;
      message: string;
    };
  }>({ isLoading: false, result: null });

  const getScriptCode = (website: Website) => {
    return `<script 
  defer
  src="https://versatailor.com/script.js"
  data-website-id="${website.id}"
  data-domain="${website.domain}"
></script>`;
  };

  const handleCopy = () => {
    navigator.clipboard.writeText(getScriptCode(website));
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  const handleValidateScript = async () => {
    try {
      setValidationStatus({ isLoading: true, result: null });

      const response = await fetch("/api/validate/script", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          url: website.domain,
          websiteId: website.id,
        }),
      });

      const data = await response.json();

      if (response.ok) {
        if (data.isValid) {
          setValidationStatus({
            isLoading: false,
            result: {
              isValid: true,
              message: "The script is correctly installed on your website! 🎉",
            },
          });
        } else if (data.isInstalled && !data.isInHead) {
          setValidationStatus({
            isLoading: false,
            result: {
              isValid: false,
              message:
                "Script was found but not in the <head> section. Please move it there for optimal tracking.",
            },
          });
        } else {
          setValidationStatus({
            isLoading: false,
            result: {
              isValid: false,
              message:
                "Could not find the tracking script on your website. Please verify it's installed correctly.",
            },
          });
        }
      } else {
        setValidationStatus({
          isLoading: false,
          result: {
            isValid: false,
            message:
              data.error || "Failed to validate script. Please try again.",
          },
        });
      }
    } catch (error) {
      setValidationStatus({
        isLoading: false,
        result: {
          isValid: false,
          message: "An error occurred during validation. Please try again.",
        },
      });
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4 overflow-hidden">
      <div
        className="bg-customGrayLight rounded-lg w-full max-w-2xl border border-gray-700/50 shadow-xl overflow-hidden"
        onClick={(e) => e.stopPropagation()}
      >
        <div className="p-6 space-y-4">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold flex items-center gap-2">
              <svg
                className="w-5 h-5 text-blue-500"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
              Tracking Code for {website.name}
            </h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-white transition-colors"
              aria-label="Close modal"
            >
              <svg
                className="w-5 h-5"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>
          </div>

          <div className="space-y-2">
            <p className="text-sm text-gray-400">
              Add this code to your website&apos;s{" "}
              <code className="text-blue-400">&lt;head&gt;</code> tag to start
              tracking.
            </p>
            <div className="bg-[#1C1C1C] rounded-lg border border-gray-700/50 overflow-hidden">
              <div className="flex items-center justify-end p-2 bg-[#363636] border-b border-gray-700/50">
                <button
                  onClick={handleCopy}
                  className="text-sm text-blue-400 hover:text-blue-300 focus:ring-2 focus:ring-blue-500/20 rounded px-2 py-1 transition-all duration-200 flex items-center gap-2"
                >
                  {copied ? (
                    <>
                      <svg
                        className="w-4 h-4"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M5 13l4 4L19 7"
                        />
                      </svg>
                      Copied!
                    </>
                  ) : (
                    <>
                      <svg
                        className="w-4 h-4"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"
                        />
                      </svg>
                      Copy Code
                    </>
                  )}
                </button>
              </div>
              <pre className="p-4 overflow-x-auto whitespace-pre-wrap break-all text-xs sm:text-sm">
                <code className="font-mono text-gray-300">
                  {getScriptCode(website)}
                </code>
              </pre>
            </div>
          </div>

          <div className="mt-6 pt-4 border-t border-gray-700/50">
            <button
              onClick={handleValidateScript}
              disabled={validationStatus.isLoading}
              className="w-full bg-blue-500 hover:bg-blue-600 focus:ring-2 focus:ring-blue-500/50 text-white px-4 py-2 rounded transition-all duration-200 flex items-center justify-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {validationStatus.isLoading ? (
                <>
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                  <span>Validating...</span>
                </>
              ) : (
                <>
                  <svg
                    className="w-4 h-4"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                    />
                  </svg>
                  Verify Installation
                </>
              )}
            </button>

            {validationStatus.result && (
              <div
                className={`mt-4 p-3 rounded ${
                  validationStatus.result.isValid
                    ? "bg-green-500/10 text-green-400 border border-green-500/20"
                    : "bg-red-500/10 text-red-400 border border-red-500/20"
                }`}
              >
                <div className="flex items-start gap-2">
                  {validationStatus.result.isValid ? (
                    <svg
                      className="w-5 h-5 mt-0.5 flex-shrink-0"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                      />
                    </svg>
                  ) : (
                    <svg
                      className="w-5 h-5 mt-0.5 flex-shrink-0"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                      />
                    </svg>
                  )}
                  <p>{validationStatus.result.message}</p>
                </div>
                {!validationStatus.result.isValid && (
                  <p className="mt-1">
                    {" "}
                    {/* Add margin-top */}
                    Having trouble?{" "}
                    <a
                      href="mailto:<EMAIL>?subject=Having trouble setting up Versatailor script"
                      className="text-blue-400"
                    >
                      Email Support
                    </a>
                  </p>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
      <div className="absolute inset-0 -z-10" onClick={onClose} />
    </div>
  );
};

// New Sortable Item Component
function SortableWebsiteCard({
  website,
  activeId,
  isDragMode,
}: {
  website: Website;
  activeId: string | null;
  isDragMode: boolean;
}) {
  const router = useRouter();
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: website.id });

  const isBeingDragged = activeId === website.id;

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    zIndex: isDragging ? 10 : undefined,
    opacity: isBeingDragged ? 0 : 1,
    visibility: isBeingDragged ? ("hidden" as const) : ("visible" as const),
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      {...attributes}
      {...(isDragMode ? listeners : {})}
      onClick={(e) => {
        if (!isDragMode) {
          router.push(`/dashboard/${website.id}`);
        }
      }}
      className={`bg-customGrayLight p-6 rounded-lg space-y-4 border border-gray-700/50 transition-all duration-300 hover:border-gray-600/50 hover:bg-[#2D2D2D] ${
        isDragMode ? 'cursor-grab touch-none' : 'cursor-pointer'
      }`}
    >
      {/* Website Card Content */}
      <div className="space-y-1">
        <div className="flex items-start gap-2 mb-5">
          <div className="flex items-start gap-2 flex-1 min-w-0">
            <div className="flex-shrink-0 mt-1">
              <WebsiteIcon
                domain={website.domain}
                name={website.name}
                size={20}
              />
            </div>
            <div className="min-w-0 flex-1">
              <h3 className="text-lg truncate" title={website.domain}>
                {website.domain}
              </h3>
              <p className="text-sm text-gray-400 truncate" title={website.name}>
                {website.name}
              </p>
            </div>
          </div>
        </div>
        {website.hourlyData && (
          <div className="h-[40px] mt-3">
            <ResponsiveContainer width="100%" height="100%">
              <AreaChart
                data={website.hourlyData}
                style={{ cursor: "default" }}
              >
                <defs>
                  <linearGradient
                    id={`colorVisitors-${website.id}`}
                    x1="0"
                    y1="0"
                    x2="0"
                    y2="1"
                  >
                    <stop
                      offset="5%"
                      stopColor="rgb(59, 130, 246)"
                      stopOpacity={0.1}
                    />
                    <stop
                      offset="95%"
                      stopColor="rgb(59, 130, 246)"
                      stopOpacity={0}
                    />
                  </linearGradient>
                </defs>
                <Area
                  type="monotone"
                  dataKey="visitors"
                  stroke="rgb(59, 130, 246)"
                  fillOpacity={1}
                  fill={`url(#colorVisitors-${website.id})`}
                  strokeWidth={1.5}
                  isAnimationActive={true}
                />
              </AreaChart>
            </ResponsiveContainer>
          </div>
        )}
        <div className="flex items-center gap-2 text-sm text-gray-400 mt-2">
          {typeof website.visitorCount24h === "number" ? (
            <div className="flex items-center gap-2 min-w-0">
              {website.trend && website.trend.percentage !== 0 && (
                <span
                  className={`flex-shrink-0 flex items-center ${
                    website.trend.isPositive ? "text-green-500" : "text-red-500"
                  }`}
                  title={`${website.trend.isPositive ? "Up" : "Down"} ${
                    website.trend.percentage
                  }% compared to previous 24h`}
                >
                  {website.trend.isPositive ? (
                    <svg
                      className="w-4 h-4"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"
                      />
                    </svg>
                  ) : (
                    <svg
                      className="w-4 h-4"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M13 17h8m0 0V9m0 8l-8-8-4 4-6-6"
                      />
                    </svg>
                  )}
                </span>
              )}
              <span className="truncate">
                {website.visitorCount24h.toLocaleString()}{" "}
                {website.visitorCount24h === 1 ? "visitor" : "visitors"} in the
                last 24h
              </span>
            </div>
          ) : (
            <span className="text-gray-500">No visitor data</span>
          )}
        </div>
      </div>
    </div>
  );
}

export default function DashboardPage() {
  const [websites, setWebsites] = useState<Website[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [activeId, setActiveId] = useState<string | null>(null);
  const [isDragMode, setIsDragMode] = useState(false);
  const [formState, setFormState] = useState<FormState>({
    isSubmitting: false,
    error: null,
  });
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedWebsite, setSelectedWebsite] = useState<Website | null>(null);
  const router = useRouter();
  const { user } = useClerk();
  const username = user?.firstName || user?.username || "there";

  const fetchWebsites = useCallback(async () => {
    try {
      setIsLoading(true);
      const response = await fetch("/api/websites");
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const data = await response.json();

      // Fetch visitor counts and hourly data for each website
      const websitesWithVisitors = await Promise.all(
        data.map(async (website: Website) => {
          try {
            const now = new Date();
            const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000);
            const twoDaysAgo = new Date(now.getTime() - 48 * 60 * 60 * 1000);

            // Fetch last 48 hours of data to calculate trend
            const eventsResponse = await fetch(
              `/api/events?websiteId=${
                website.id
              }&startDate=${twoDaysAgo.toISOString()}&endDate=${now.toISOString()}`
            );
            if (!eventsResponse.ok) return website;

            const events = await eventsResponse.json();

            // Process events into hourly data for the chart (last 24h)
            const hourlyData = Array.from({ length: 24 }, (_, i) => {
              const hour = new Date(now);
              hour.setHours(hour.getHours() - (23 - i));
              hour.setMinutes(0, 0, 0);
              const nextHour = new Date(hour);
              nextHour.setHours(hour.getHours() + 1);

              const hourEvents = events.filter((event: any) => {
                const eventDate = new Date(event.timestamp);
                return eventDate >= hour && eventDate < nextHour;
              });

              // Count unique visitors per hour
              const uniqueVisitors = new Set(
                hourEvents.map((event: any) => event.visitorId)
              ).size;

              const hourNum = hour.getHours();
              const timeLabel =
                hourNum === 0
                  ? "12 AM"
                  : hourNum < 12
                    ? `${hourNum} AM`
                    : hourNum === 12
                      ? "12 PM"
                      : `${hourNum - 12} PM`;

              return {
                time: timeLabel,
                visitors: uniqueVisitors,
              };
            });

            // Calculate visitor counts for trend
            const last24hEvents = events.filter((event: any) => {
              const eventDate = new Date(event.timestamp);
              return eventDate >= yesterday && eventDate <= now;
            });

            const previous24hEvents = events.filter((event: any) => {
              const eventDate = new Date(event.timestamp);
              return eventDate >= twoDaysAgo && eventDate < yesterday;
            });

            // Calculate unique visitors without location filter
            const visitorCount24h = new Set(
              last24hEvents.map((e: Event) => e.visitorId)
            ).size;
            const visitorCount48h = new Set(
              previous24hEvents.map((e: Event) => e.visitorId)
            ).size;

            // Calculate trend
            let trend = {
              percentage: 0,
              isPositive: true,
            };

            if (visitorCount48h > 0) {
              const change = visitorCount24h - visitorCount48h;
              const percentage = Math.round((change / visitorCount48h) * 100);
              trend = {
                percentage: Math.abs(percentage),
                isPositive: change >= 0,
              };
            } else if (visitorCount24h > 0) {
              // If previous period had 0 visitors but current has some, show 100% increase
              trend = {
                percentage: 100,
                isPositive: true,
              };
            }

            return {
              ...website,
              visitorCount24h,
              visitorCount48h,
              hourlyData,
              trend,
            };
          } catch (error) {
            console.error(
              `Failed to fetch visitors for ${website.domain}:`,
              error
            );
            return website;
          }
        })
      );

      // Sort websites by creation date (oldest first)
      const sortedWebsites = websitesWithVisitors.sort(
        (a, b) =>
          new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
      );

      // Load order from localStorage after fetching
      const savedOrder = localStorage.getItem("websiteOrder");
      let finalWebsites = sortedWebsites;
      if (savedOrder) {
        const orderedIds = JSON.parse(savedOrder);
        finalWebsites = orderedIds
          .map((id: string) => sortedWebsites.find((w) => w.id === id))
          .filter((w: Website | undefined): w is Website => w !== undefined);
        // Add any websites not in the saved order (e.g., newly added)
        sortedWebsites.forEach((w) => {
          if (!finalWebsites.find((fw) => fw.id === w.id)) {
            finalWebsites.push(w);
          }
        });
      }
      setWebsites(finalWebsites);
    } catch (err) {
      setFormState((prev) => ({
        ...prev,
        error: "Failed to fetch websites. Please try again later.",
      }));
    } finally {
      setIsLoading(false);
    }
  }, []); // Add empty dependency array

  useEffect(() => {
    fetchWebsites();
  }, [fetchWebsites]); // fetchWebsites is now stable due to useCallback

  const handleAddWebsite = async (name: string, domain: string, revenueAttribution?: { enabled: boolean; stripeApiKey?: string }) => {
    try {
      setFormState({ isSubmitting: true, error: null });
      const response = await fetch("/api/websites", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ name: name.trim(), domain: domain.trim() }),
      });
      const data = await response.json();

      if (response.ok) {
        // If revenue attribution is enabled, set up the webhook
        if (revenueAttribution?.enabled && revenueAttribution.stripeApiKey) {
          try {
            const webhookResponse = await fetch("/api/stripe/webhook-setup", {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
              },
              body: JSON.stringify({
                websiteId: data.id,
                stripeApiKey: revenueAttribution.stripeApiKey,
                action: "setup"
              }),
            });

            const webhookData = await webhookResponse.json();
            
            if (!webhookResponse.ok) {
              // Revenue attribution setup failed, but website was created
              setFormState({
                isSubmitting: false,
                error: `Website created but revenue attribution setup failed: ${webhookData.error}. You can enable it later in settings.`,
              });
              return;
            }
          } catch (webhookError) {
            console.error("Webhook setup error:", webhookError);
            setFormState({
              isSubmitting: false,
              error: "Website created but revenue attribution setup failed. You can enable it later in settings.",
            });
            return;
          }
        }

        // Add new website to the list (respecting current order if possible)
        const newWebsite = {
          ...data,
          visitorCount24h: 0,
          hourlyData: [],
          trend: { percentage: 0, isPositive: true },
        }; // Add default values
        const currentWebsites = [...websites, newWebsite];
        setWebsites(currentWebsites);

        // Update localStorage order
        const currentOrder = currentWebsites.map((w) => w.id);
        localStorage.setItem("websiteOrder", JSON.stringify(currentOrder));

        setFormState({ isSubmitting: false, error: null });
        setIsModalOpen(false); // Close add website modal
        setSelectedWebsite(data); // Show script modal for the new website
      } else {
        setFormState({
          isSubmitting: false,
          error: data.error || "Failed to create website. Please try again.",
        });
      }
    } catch (err) {
      setFormState({
        isSubmitting: false,
        error: "An unexpected error occurred. Please try again later.",
      });
    }
  };

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: isDragMode ? 5 : Number.POSITIVE_INFINITY,
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  const handleDragStart = (event: DragStartEvent) => {
    setActiveId(event.active.id as string);
  };

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (over && active.id !== over.id) {
      setWebsites((items) => {
        const oldIndex = items.findIndex((item) => item.id === active.id);
        const newIndex = items.findIndex((item) => item.id === over.id);
        const newOrderedItems = arrayMove(items, oldIndex, newIndex);

        // Save the new order to localStorage
        const newOrder = newOrderedItems.map((website) => website.id);
        localStorage.setItem("websiteOrder", JSON.stringify(newOrder));

        return newOrderedItems;
      });
    }
    setActiveId(null); // Clear active ID when drag ends
  };

  // Find the website data for the active dragged item
  const activeWebsite = activeId
    ? websites.find((w) => w.id === activeId)
    : null;

  if (isLoading) {
    return <DashboardSkeleton />;
  }

  return (
    <div className="min-h-screen bg-customGray text-gray-100 overflow-x-hidden">
      <Modal isOpen={isModalOpen} onClose={() => setIsModalOpen(false)}>
        <AddWebsiteForm
          onSubmit={handleAddWebsite}
          onClose={() => setIsModalOpen(false)}
          formState={formState}
        />
      </Modal>

      {selectedWebsite && (
        <ScriptModal
          isOpen={!!selectedWebsite}
          onClose={() => setSelectedWebsite(null)}
          website={selectedWebsite}
        />
      )}

      <div className="container mx-auto px-4 py-8 max-w-full sm:max-w-6xl">
        {/* Welcome Message */}
        <div className="mb-8">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <h2 className="text-md font-medium flex flex-col sm:flex-row sm:items-center sm:gap-2">
              <span>Hey {username} 👋</span>
              {websites.length > 0 ? (
                <span className="text-gray-400">
                  You have{" "}
                  <span className="text-white font-semibold">
                    {websites
                      .reduce(
                        (total, website) =>
                          total + (website.visitorCount24h || 0),
                        0
                      )
                      .toLocaleString()}
                  </span>{" "}
                  visitors in the last 24 hours.{" "}
                </span>
              ) : (
                <span className="text-gray-400 font-semibold">
                  Let&apos;s add your first website!
                </span>
              )}
            </h2>
            <div className="flex justify-start sm:justify-end items-center gap-3">
              {websites.length > 1 && (
                <button
                  onClick={() => setIsDragMode(!isDragMode)}
                  className={`group relative inline-flex items-center justify-center w-10 h-10 rounded transition-all duration-200 ${
                    isDragMode
                      ? 'bg-blue-500 text-white hover:bg-blue-600'
                      : 'bg-customGrayLight text-gray-400 hover:text-white hover:bg-customGray'
                  }`}
                  aria-label={isDragMode ? "Exit reorder mode" : "Enter reorder mode"}
                >
                  <svg
                    className={`w-5 h-5 transition-all duration-200 ${isDragMode ? 'scale-110' : 'scale-100'}`}
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M9 3h6m-6 18h6M3 9v6m18-6v6M9 3c0 6 6 6 6 0M9 21c0-6 6-6 6 0M3 9c6 0 6 6 0 6m18-6c-6 0-6 6 0 6"
                    />
                  </svg>
                  <span className="absolute -top-10 left-1/2 -translate-x-1/2 whitespace-nowrap px-3 py-1.5 bg-gray-900 text-white text-sm rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none">
                    {isDragMode ? 'Exit reorder mode' : 'Reorder websites'}
                  </span>
                </button>
              )}
              <button
                onClick={() => setIsModalOpen(true)}
                className="inline-flex bg-customGrayLight text-white px-4 py-2 rounded hover:bg-customGray focus:ring-2 focus:ring-blue-500/50 transition-all duration-200 items-center justify-center gap-2"
              >
                <svg
                  className="w-4 h-4"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                  />
                </svg>
                Add Website
              </button>
            </div>
          </div>
          {isDragMode && websites.length > 1 && (
            <div className="mt-4 p-3 bg-blue-500/10 border border-blue-500/20 rounded-lg">
              <p className="text-sm text-blue-400 flex items-center gap-2">
                <svg className="w-4 h-4 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4" />
                </svg>
                Drag websites to reorder them. Click the reorder icon again when finished.
              </p>
            </div>
          )}
        </div>

        {/* Websites List */}
        <DndContext
          sensors={sensors}
          collisionDetection={closestCenter}
          onDragStart={handleDragStart}
          onDragEnd={handleDragEnd}
        >
          <SortableContext
            items={websites.map((w) => w.id)}
            strategy={rectSortingStrategy}
          >
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
              {websites.length === 0 && !isLoading ? (
                <div className="bg-customGrayLight p-8 rounded-lg text-center space-y-4 border border-gray-700/50 col-span-full">
                  <div className="w-16 h-16 mx-auto rounded-full bg-blue-500/10 flex items-center justify-center">
                    <svg
                      className="w-8 h-8 text-blue-500"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                      />
                    </svg>
                  </div>
                  <h3 className="text-xl font-semibold">No websites yet</h3>
                  <p className="text-gray-400">
                    Add your first website to start tracking
                  </p>
                  <button
                    onClick={() => setIsModalOpen(true)}
                    className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 focus:ring-2 focus:ring-blue-500/50 transition-all duration-200 inline-flex items-center gap-2"
                  >
                    <svg
                      className="w-4 h-4"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                      />
                    </svg>
                    Add Your First Website
                  </button>
                </div>
              ) : (
                websites.map((website) => (
                  <SortableWebsiteCard
                    key={website.id}
                    website={website}
                    activeId={activeId}
                    isDragMode={isDragMode}
                  />
                ))
              )}
            </div>
          </SortableContext>

          <DragOverlay
            dropAnimation={{
              duration: 250,
              easing: "cubic-bezier(0.18, 0.67, 0.6, 1.22)",
            }}
          >
            {activeWebsite && isDragMode ? (
              <div className="bg-customGrayLight p-6 rounded-lg space-y-4 border border-gray-700/50 shadow-xl cursor-grabbing">
                {/* Website Card Content - Copied from SortableWebsiteCard */}
                <div className="space-y-1">
                  <div className="flex items-start gap-2 mb-5">
                    <div className="flex items-start gap-2 flex-1 min-w-0">
                      <div className="flex-shrink-0 mt-1">
                        <WebsiteIcon
                          domain={activeWebsite.domain}
                          name={activeWebsite.name}
                          size={20}
                        />
                      </div>
                      <div className="min-w-0 flex-1">
                        <h3 className="text-lg truncate" title={activeWebsite.domain}>
                          {activeWebsite.domain}
                        </h3>
                        <p className="text-sm text-gray-400 truncate" title={activeWebsite.name}>
                          {activeWebsite.name}
                        </p>
                      </div>
                    </div>
                  </div>
                  {activeWebsite.hourlyData && (
                    <div className="h-[40px] mt-3">
                      <ResponsiveContainer width="100%" height="100%">
                        <AreaChart
                          data={activeWebsite.hourlyData}
                          style={{ cursor: "default" }}
                        >
                          <defs>
                            <linearGradient
                              id={`colorVisitorsOverlay-${activeWebsite.id}`}
                              x1="0"
                              y1="0"
                              x2="0"
                              y2="1"
                            >
                              <stop
                                offset="5%"
                                stopColor="rgb(59, 130, 246)"
                                stopOpacity={0.1}
                              />
                              <stop
                                offset="95%"
                                stopColor="rgb(59, 130, 246)"
                                stopOpacity={0}
                              />
                            </linearGradient>
                          </defs>
                          <Area
                            type="monotone"
                            dataKey="visitors"
                            stroke="rgb(59, 130, 246)"
                            fillOpacity={1}
                            fill={`url(#colorVisitorsOverlay-${activeWebsite.id})`}
                            strokeWidth={1.5}
                            isAnimationActive={false} // Disable animation in overlay
                          />
                        </AreaChart>
                      </ResponsiveContainer>
                    </div>
                  )}
                  <div className="flex items-center gap-2 text-sm text-gray-400 mt-2">
                    {typeof activeWebsite.visitorCount24h === "number" ? (
                      <div className="flex items-center gap-2 min-w-0">
                        {activeWebsite.trend &&
                          activeWebsite.trend.percentage !== 0 && (
                            <span
                              className={`flex-shrink-0 flex items-center ${
                                activeWebsite.trend.isPositive
                                  ? "text-green-500"
                                  : "text-red-500"
                              }`}
                              title={`${activeWebsite.trend.isPositive ? "Up" : "Down"} ${
                                activeWebsite.trend.percentage
                              }% compared to previous 24h`}
                            >
                              {activeWebsite.trend.isPositive ? (
                                <svg
                                  className="w-4 h-4"
                                  fill="none"
                                  stroke="currentColor"
                                  viewBox="0 0 24 24"
                                >
                                  <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"
                                  />
                                </svg>
                              ) : (
                                <svg
                                  className="w-4 h-4"
                                  fill="none"
                                  stroke="currentColor"
                                  viewBox="0 0 24 24"
                                >
                                  <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M13 17h8m0 0V9m0 8l-8-8-4 4-6-6"
                                  />
                                </svg>
                              )}
                            </span>
                          )}
                        <span className="truncate">
                          {activeWebsite.visitorCount24h.toLocaleString()}{" "}
                          {activeWebsite.visitorCount24h === 1
                            ? "visitor"
                            : "visitors"}{" "}
                          in last 24h
                        </span>
                      </div>
                    ) : (
                      <span className="text-gray-500">No visitor data</span>
                    )}
                  </div>
                </div>
                {/* End Website Card Content */}
              </div>
            ) : null}
          </DragOverlay>
        </DndContext>
      </div>
    </div>
  );
}
