import User from "@/models/user";
import { auth } from "@clerk/nextjs/server";
import { NextResponse } from "next/server";

export async function GET() {
  const { userId } = await auth();

  if (!userId) {
    return NextResponse.json({ error: "Unauthorized." }, { status: 401 });
  }
  
  const user = await User.findOne({ clerkId: userId });

  if (!user) {
    return NextResponse.json({ error: "User Not Found" }, { status: 404 });
  }

  if (user.accessUntil < new Date()) {
    console.log(
      `today is ${new Date()} and user's access ends on ${user.accessUntil}`
    );

    return NextResponse.json(
      {
        success: false,
        message: "Access expired",
      },
      { status: 403 }
    );
  } 

  return NextResponse.json(
    {
      success: true,
      message: "Access granted",
    },
    { status: 200 }
  );
}
