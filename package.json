{"name": "versatailor", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "email": "email dev", "start": "next start", "lint": "next lint"}, "dependencies": {"@clerk/nextjs": "^6.12.0", "@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@headlessui/react": "^2.2.0", "@heroicons/react": "^2.2.0", "@mux/mux-player-react": "^3.4.0", "@radix-ui/react-alert-dialog": "^1.1.7", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tooltip": "^1.1.8", "@react-email/components": "^0.0.25", "@tanstack/react-query": "^5.67.2", "@types/jszip": "^3.4.1", "@types/papaparse": "^5.3.15", "@types/uuid": "^10.0.0", "@vercel/analytics": "^1.5.0", "@vercel/functions": "^2.0.0", "axios": "^1.8.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "country-flag-emoji": "^1.0.3", "date-fns": "^4.1.0", "framer-motion": "^12.4.9", "jszip": "^3.10.1", "lucide-react": "^0.475.0", "mongoose": "^8.10.1", "next": "15.1.7", "next-pwa": "^5.6.0", "papaparse": "^5.5.2", "react": "19.0.0", "react-dom": "19.0.0", "react-hot-toast": "^2.5.2", "react-icons": "^5.5.0", "react-tooltip": "^5.28.0", "recharts": "^2.15.1", "resend": "^4.2.0", "stripe": "^17.7.0", "svix": "^1.59.2", "tailwind-merge": "^3.0.2", "tailwindcss-animate": "^1.0.7", "ua-parser-js": "^2.0.2", "uuid": "^11.1.0"}, "devDependencies": {"@types/node": "^20", "@types/react": "19.0.10", "@types/react-dom": "19.0.4", "@types/react-window": "^1.8.8", "@types/ua-parser-js": "^0.7.39", "@types/youtube": "^0.1.0", "eslint": "^8", "eslint-config-next": "15.1.7", "file-loader": "^6.2.0", "postcss": "^8", "react-email": "3.0.1", "tailwindcss": "^3.4.1", "typescript": "^5"}, "overrides": {"@types/react": "19.0.10", "@types/react-dom": "19.0.4"}}