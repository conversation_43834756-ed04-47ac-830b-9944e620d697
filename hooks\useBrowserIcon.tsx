import Image from "next/image";

export function useBrowserIcon() {
  const getBrowserIcon = (browserName: string) => {
    const name = browserName.toLowerCase();
    
    if (name.includes('chrome')) {
      return (
        <Image src="/assets/chrome.svg" alt="Chrome" width={40} height={40} className="w-5 h-5"/>
      );
    }
    
    if (name.includes('firefox')) {
      return (
        <Image src="/assets/firefox.svg" alt="Firefox" width={40} height={40} className="w-5 h-5"/>
      );
    }
    
    if (name.includes('safari')) {
      return (
        <Image src="/assets/safari.png" alt="Safari" width={40} height={40} className="w-5 h-5"/>
      );
    }
    
    if (name.includes('edge')) {
      return (
        <Image src="/assets/edge.png" alt="Edge" width={40} height={40} className="w-5 h-5"/>
      );
    }
    
    if (name.includes('opera')) {
      return (
        <Image src="/assets/opera.svg" alt="Opera" width={40} height={40} className="w-5 h-5"/>
      );
    }
    
    // Default question mark icon for unknown browsers
    return (
      <svg className="w-5 h-5" viewBox="0 0 24 24" fill="currentColor">
        <path d="M12 0C5.373 0 0 5.373 0 12s5.373 12 12 12 12-5.373 12-12S18.627 0 12 0zm0 19.5a1.5 1.5 0 110-3 1.5 1.5 0 010 3zm1.5-6.5c0 .828-.672 1.5-1.5 1.5s-1.5-.672-1.5-1.5V7c0-.828.672-1.5 1.5-1.5s1.5.672 1.5 1.5v6z"/>
      </svg>
    );
  };

  return { getBrowserIcon };
} 
