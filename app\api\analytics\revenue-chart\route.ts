import { NextRequest, NextResponse } from "next/server";
import { auth } from "@clerk/nextjs/server";
import connect from "@/util/db";
import Website from "@/models/website";
import Event from "@/models/event";
import { getDateRangeFromPeriod } from "@/lib/periodHelpers";

export async function GET(request: NextRequest) {
  try {
    await connect();

    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const websiteId = searchParams.get("websiteId");
    const startDate = searchParams.get("startDate");
    const endDate = searchParams.get("endDate");
    const period = searchParams.get("period") || "7d";

    // Revenue-specific filters
    const minAmount = searchParams.get("minAmount");
    const maxAmount = searchParams.get("maxAmount");
    const customerType = searchParams.get("customerType");
    const paymentMethod = searchParams.get("paymentMethod");
    const paymentType = searchParams.get("paymentType");
    const currency = searchParams.get("currency");
    const revenueStartDate = searchParams.get("revenueStartDate");
    const revenueEndDate = searchParams.get("revenueEndDate");

    if (!websiteId) {
      return NextResponse.json(
        { error: "Website ID is required" },
        { status: 400 }
      );
    }

    // Verify website ownership and revenue attribution
    // Conditionally include userId and revenueAttributionEnabled based on environment
    const websiteQuery = process.env.NODE_ENV === "production"
      ? { id: websiteId, userId, revenueAttributionEnabled: true }
      : { id: websiteId };

    const website = await Website.findOne(websiteQuery);

    if (!website) {
      return NextResponse.json(
        { error: "Website not found or revenue attribution not enabled" },
        { status: 404 }
      );
    }

    // Calculate date range using the same helper as visitor analytics
    let currentStart: Date, currentEnd: Date;

    if (startDate && endDate) {
      currentStart = new Date(startDate);
      currentEnd = new Date(endDate);
    } else {
      // Use the same date range logic as visitor analytics
      const customPeriod = startDate && endDate ? {
        startDate: new Date(startDate),
        endDate: new Date(endDate)
      } : undefined;
      
      const dateRange = getDateRangeFromPeriod(period as any, customPeriod);
      currentStart = dateRange.from;
      currentEnd = dateRange.to;
    }

    // Build query filters
    const baseQuery: any = {
      websiteId,
      type: "payment",
      revenueData: { $exists: true }
    };

    // Apply revenue-specific filters
    if (minAmount || maxAmount) {
      baseQuery["revenueData.amount"] = {};
      if (minAmount) {
        baseQuery["revenueData.amount"].$gte = parseFloat(minAmount) * 100; // Convert to cents
      }
      if (maxAmount) {
        baseQuery["revenueData.amount"].$lte = parseFloat(maxAmount) * 100; // Convert to cents
      }
    }

    if (customerType && customerType !== "all") {
      if (customerType === "new") {
        baseQuery["revenueData.isNewCustomer"] = true;
      } else if (customerType === "returning") {
        baseQuery["revenueData.isNewCustomer"] = { $ne: true };
      }
    }

    if (paymentMethod && paymentMethod !== "all") {
      baseQuery["revenueData.paymentMethod"] = paymentMethod;
    }

    if (paymentType && paymentType !== "all") {
      if (paymentType === "subscription") {
        baseQuery["revenueData.paymentType"] = { $in: ["subscription", "renewal"] };
      } else {
        baseQuery["revenueData.paymentType"] = paymentType;
      }
    }

    if (currency && currency !== "all") {
      baseQuery["revenueData.currency"] = currency;
    }

    // Use revenue date filter if provided, otherwise use general date filter
    let dateFilter;
    if (revenueStartDate && revenueEndDate) {
      dateFilter = {
        $gte: new Date(revenueStartDate),
        $lte: new Date(revenueEndDate)
      };
    } else {
      dateFilter = { $gte: currentStart, $lte: currentEnd };
    }

    // Query for payment events
    const eventsQuery = {
      ...baseQuery,
      timestamp: dateFilter
    };

    const paymentEvents = await Event.find(eventsQuery);
    
    console.log('Query:', eventsQuery);
    console.log('Found payment events:', paymentEvents.length);
    console.log('Sample payment event:', paymentEvents[0]);

    // Determine time period granularity
    const timeDiff = currentEnd.getTime() - currentStart.getTime();
    const isToday = period === "24h" || period === "today";
    const isHourly = isToday || timeDiff <= 24 * 60 * 60 * 1000; // 1 day or less

    console.log('Time period:', { isToday, isHourly, period, timeDiff });
    
    // Generate time periods
    const chartData: Array<{ time: string; revenue: number; orders: number }> = [];

    if (isHourly) {
      // Generate hourly data for today/24h
      for (let i = 0; i < 24; i++) {
        // Use local timezone for hour calculations
        const hour = new Date(currentStart);
        hour.setHours(i, 0, 0, 0);
        const nextHour = new Date(hour);
        nextHour.setHours(hour.getHours() + 1);

        const hourEvents = paymentEvents.filter(event => {
          const eventDate = new Date(event.timestamp);
          // Use local timezone comparison - compare dates directly
          const eventHour = eventDate.getHours();
          const targetHour = i;
          
          // For today/24h periods, also ensure we're on the right day
          if (isToday) {
            const eventDateOnly = new Date(eventDate);
            eventDateOnly.setHours(0, 0, 0, 0);
            const targetDateOnly = new Date(currentStart);
            targetDateOnly.setHours(0, 0, 0, 0);
            
            return eventDateOnly.toDateString() === targetDateOnly.toDateString() && 
                   eventHour === targetHour;
          } else {
            // For last24h, check if event is within the hour range
            return eventDate >= hour && eventDate < nextHour;
          }
        });

        console.log(`Hour ${i}:`, { 
          start: hour.toISOString(), 
          events: hourEvents.length,
          eventDetails: hourEvents.map(e => ({
            timestamp: e.timestamp,
            amount: e.revenueData?.amount
          }))
        });

        // Don't divide by 100 here since the Chart component will handle the conversion
        const revenue = hourEvents.reduce((sum, event) => 
          sum + (event.revenueData?.amount || 0), 0);

        const timeLabel = hour.getHours() === 0 ? "12am" :
                         hour.getHours() === 12 ? "12pm" :
                         hour.getHours() > 12 ? `${hour.getHours() - 12}pm` :
                         `${hour.getHours()}am`;

        chartData.push({
          time: timeLabel,
          revenue,
          orders: hourEvents.length
        });
      }
    } else {
      // Generate daily data using local timezone
      const days = Math.ceil(timeDiff / (24 * 60 * 60 * 1000));
      
      for (let i = 0; i < days; i++) {
        const day = new Date(currentStart);
        day.setDate(currentStart.getDate() + i);
        day.setHours(0, 0, 0, 0);
        const nextDay = new Date(day);
        nextDay.setDate(day.getDate() + 1);

        const dayEvents = paymentEvents.filter(event => {
          const eventDate = new Date(event.timestamp);
          // Use local timezone comparison
          const eventDateOnly = new Date(eventDate);
          eventDateOnly.setHours(0, 0, 0, 0);
          const targetDateOnly = new Date(day);
          targetDateOnly.setHours(0, 0, 0, 0);
          
          return eventDateOnly.toDateString() === targetDateOnly.toDateString();
        });

        // Don't divide by 100 here since the Chart component will handle the conversion
        const revenue = dayEvents.reduce((sum, event) => 
          sum + (event.revenueData?.amount || 0), 0);

        const timeLabel = day.toLocaleDateString('en-US', { 
          month: 'short', 
          day: 'numeric' 
        });

        chartData.push({
          time: timeLabel,
          revenue,
          orders: dayEvents.length
        });
      }
    }

    return NextResponse.json(chartData);

  } catch (error: any) {
    console.error("Revenue chart error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
} 