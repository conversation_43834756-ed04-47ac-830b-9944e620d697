import type { Metadata } from "next";
import config from "@/config";
import React from "react";
type CustomOpenGraph = {
  title?: string;
  description?: string;
  url?: string;
  siteName?: string;
  images?: Array<{
    url: string;
    width?: number;
    height?: number;
    alt?: string;
  }>;
  locale?: string;
  type?: string;
  publishedTime?: string;
  authors?: string[];
};

type SEOTagsProps = {
  title?: string;
  description?: string;
  keywords?: string | string[];
  openGraph?: CustomOpenGraph;
  canonicalUrlRelative?: string;
  extraTags?: Record<string, any>;
};

export const getSEOTags = ({
  title,
  description,
  keywords,
  openGraph,
  canonicalUrlRelative,
  extraTags,
}: SEOTagsProps = {}): Metadata => {
  const baseUrl =
    process.env.NODE_ENV === "development"
      ? "http://localhost:3000"
      : `https://${config.domainName}`;

  const metadata: Metadata = {
    title: title || config.appName,
    description:
      "The ultimate web analytics tool for SaaS and online businesses. Track visitors, analyze behavior, and make data-driven decisions.",
    keywords: keywords || [config.appName],
    applicationName: config.appName,
    metadataBase: new URL(baseUrl),

    openGraph: {
      ...(openGraph?.type === "article"
        ? {
            type: "article",
            articles: {
              publishedTime: openGraph.publishedTime,
              authors: openGraph.authors,
            },
          }
        : {
            type: "website",
          }),
      title: "Versatailor - Web Analytics Tool",
      description:
        "The ultimate web analytics tool for SaaS and online businesses. Track visitors, analyze behavior, and make data-driven decisions.",

      url: openGraph?.url || baseUrl,
      siteName: openGraph?.siteName || config.appName,
      locale: openGraph?.locale || "en_US",
      images: [
        {
          url: `${baseUrl}/og_image.png`,
          width: 1200,
          height: 630,
          alt: "Versatailor Preview",
        },
      ],
    },

    twitter: {
      card: "summary_large_image",
      title: "Versatailor - Web Analytics Tool",
      description:
        "The ultimate web analytics tool for SaaS and online businesses. Track visitors, analyze behavior, and make data-driven decisions.",
      creator: "@ziadbeshippin",
      images: [
        {
          url: `${baseUrl}/og_image.png`,
          width: 1200,
          height: 630,
          alt: "Versatailor Preview",
        },
      ],
    },
  };

  if (canonicalUrlRelative) {
    metadata.alternates = { canonical: canonicalUrlRelative };
  }

  if (extraTags) {
    Object.assign(metadata, extraTags);
  }

  return metadata;
};

