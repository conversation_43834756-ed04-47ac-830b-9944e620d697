import React, { RefObject } from "react";
import Image from "next/image";
import { useCountryCode } from "@/hooks/useCountryCode";

type JourneyListModalProps = {
  journeyListModalRef: RefObject<HTMLDivElement | null>;
  selectedEventJourneys: {
    eventName: string;
    journeys: Array<{
      visitorId: string;
      timestamp: string;
      location?: {
        city: string;
        country: string;
      };
      extraData?: any;
    }>;
  };
  setShowJourneyListModal: (show: boolean) => void;
  handleJourneyClick: (
    eventName: string,
    visitorId: string,
    timestamp: string
  ) => void;
};

function JourneyListModal({
  journeyListModalRef,
  selectedEventJourneys,
  setShowJourneyListModal,
  handleJourneyClick,
}: JourneyListModalProps) {
  const { getCountryFlagUrl } = useCountryCode();
  const isSignupEvent = selectedEventJourneys.eventName === "signup";
  
  // For signup events, we want to deduplicate by visitorId to show only unique users
  const journeys = isSignupEvent
    ? Array.from(
        new Map(
          selectedEventJourneys.journeys.map(journey => [journey.visitorId, journey])
        ).values()
      )
    : selectedEventJourneys.journeys;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4 overflow-hidden">
      <div
        ref={journeyListModalRef}
        className="bg-[#1C1C1C] rounded-lg shadow-xl w-full max-w-2xl max-h-[80vh] overflow-hidden"
      >
        <div className="p-4 border-b border-gray-800 flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="w-12 h-12 rounded-full bg-[#2A2A2A] flex items-center justify-center">
              {isSignupEvent ? (
                <svg
                  className="w-6 h-6 text-green-400"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={1.5}
                    d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z"
                  />
                </svg>
              ) : (
                <svg
                  className="w-6 h-6 text-blue-400"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={1.5}
                    d="M13 10V3L4 14h7v7l9-11h-7z"
                  />
                </svg>
              )}
            </div>
            <div>
              <h3 className="text-lg font-medium text-white">
                {isSignupEvent ? "User Signups" : selectedEventJourneys.eventName}
              </h3>
              <p className="text-sm text-gray-400">
                {isSignupEvent
                  ? `${journeys.length} ${journeys.length === 1 ? "user" : "users"}`
                  : `${selectedEventJourneys.journeys.length} occurrences`}
              </p>
            </div>
          </div>
          <button
            onClick={() => setShowJourneyListModal(false)}
            className="text-gray-500 hover:text-gray-400 transition-colors"
          >
            <svg
              className="w-5 h-5"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </button>
        </div>
        <div className="p-4 overflow-y-auto max-h-[calc(80vh-5rem)]">
          <div className="space-y-2">
            {journeys.map((journey, index) => {
              const eventDate = new Date(journey.timestamp);
              return (
                <button
                  key={index}
                  onClick={() =>
                    handleJourneyClick(
                      selectedEventJourneys.eventName,
                      journey.visitorId,
                      journey.timestamp
                    )
                  }
                  className="w-full flex items-center justify-between p-3 rounded-lg bg-[#2A2A2A] hover:bg-[#363636] transition-colors group"
                >
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 rounded-full bg-[#363636] group-hover:bg-[#404040] flex items-center justify-center transition-colors">
                      <svg
                        className="w-5 h-5 text-gray-400"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={1.5}
                          d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                        />
                      </svg>
                    </div>
                    <div className="text-left">
                      <div className="text-sm font-medium text-white flex items-center gap-2">
                        {isSignupEvent && journey.extraData?.email ? (
                          <span className="text-blue-400">{journey.extraData.email}</span>
                        ) : (
                          <>Visitor {journey.visitorId.slice(0, 8)}</>
                        )}
                      </div>
                      <div className="text-xs text-gray-400">
                        {eventDate.toLocaleDateString("en-US", {
                          month: "long",
                          day: "numeric",
                          year: "numeric",
                        })}{" "}
                        at{" "}
                        {eventDate.toLocaleTimeString("en-US", {
                          hour: "numeric",
                          minute: "numeric",
                          hour12: true,
                        })}
                      </div>
                      {journey.location && journey.location.country && (
                        <div className="text-xs text-gray-500 flex items-center gap-1 mt-1">
                          <span className="w-6 h-4 relative">
                            {getCountryFlagUrl(journey.location.country) && (
                              <Image
                                src={getCountryFlagUrl(journey.location.country)!}
                                alt={`${journey.location.country} flag`}
                                fill
                                className="object-cover rounded-sm"
                              />
                            )}
                          </span>
                          <span>
                            {journey.location.city || "Unknown"}, {journey.location.country}
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                  <div className="text-blue-400 opacity-0 group-hover:opacity-100 transition-opacity">
                    View Journey
                    <svg
                      className="w-4 h-4 inline-block ml-1"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M9 5l7 7-7 7"
                      />
                    </svg>
                  </div>
                </button>
              );
            })}
          </div>
        </div>
      </div>
    </div>
  );
}
export default JourneyListModal;
