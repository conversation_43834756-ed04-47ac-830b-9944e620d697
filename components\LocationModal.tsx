import React, { RefObject, useState } from "react";
import { useFilterState } from "@/hooks/useFilterState";
import { GroupedLocations } from "@/hooks/useGroupedData";
import { useCountryCode } from "@/hooks/useCountryCode";
import { Filter } from "@/lib/types";
import Image from "next/image";

type LocationModalProps = {
  locationModalRef: RefObject<HTMLDivElement | null>;
  locationView: "country" | "region" | "city";
  groupedLocations: GroupedLocations;
  maxLocationVisitors: number;
  setShowLocationModal: (show: boolean) => void;
};

function LocationModal({
  locationModalRef,
  locationView: initialLocationView,
  groupedLocations,
  maxLocationVisitors,
  setShowLocationModal,
}: LocationModalProps) {
  const { getCountryFlagUrl } = useCountryCode();
  const { handleFilterClick, activeFilters } = useFilterState();
  const [modalLocationView, setModalLocationView] = useState<"country" | "region" | "city">(initialLocationView);

  const handleLocationFilterClick = (
    type: Filter["type"],
    value: any,
    label: string
  ) => {
    // First apply the filter
    handleFilterClick(type, value, label);
    // Then close the modal
    setTimeout(() => setShowLocationModal(false), 100);
  };

  return (
    <div 
      className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4 overflow-hidden"
      onClick={() => setShowLocationModal(false)}
    >
      <div
        ref={locationModalRef}
        className="bg-[#2A2A2A] rounded-lg w-full max-w-2xl max-h-[80vh] overflow-hidden flex flex-col"
        onClick={(e) => e.stopPropagation()}
      >
        <div className="flex items-center justify-between p-4 border-b border-gray-700">
          <h2 className="text-lg font-semibold text-white">
            {modalLocationView === "country"
              ? "Countries"
              : modalLocationView === "region"
              ? "Regions"
              : "Cities"}
          </h2>
          <button
            onClick={() => setShowLocationModal(false)}
            className="text-gray-400 hover:text-white"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-5 w-5"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fillRule="evenodd"
                d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                clipRule="evenodd"
              />
            </svg>
          </button>
        </div>
        <div className="flex justify-center p-2 border-b border-gray-700">
          <div className="flex rounded-lg p-1">
            <button
              onClick={() => setModalLocationView("country")}
              className={`px-2 py-1 rounded-md text-xs sm:text-sm transition-colors ${
                modalLocationView === "country"
                  ? "bg-blue-500 text-white"
                  : "text-gray-400 hover:text-white"
              }`}
            >
              Country
            </button>
            <button
              onClick={() => setModalLocationView("region")}
              className={`px-2 py-1 rounded-md text-xs sm:text-sm transition-colors ${
                modalLocationView === "region"
                  ? "bg-blue-500 text-white"
                  : "text-gray-400 hover:text-white"
              }`}
            >
              Region
            </button>
            <button
              onClick={() => setModalLocationView("city")}
              className={`px-2 py-1 rounded-md text-xs sm:text-sm transition-colors ${
                modalLocationView === "city"
                  ? "bg-blue-500 text-white"
                  : "text-gray-400 hover:text-white"
              }`}
            >
              City
            </button>
          </div>
        </div>
        <div className="overflow-y-auto p-4 flex-grow">
          <div className="space-y-1">
            {modalLocationView === "country" &&
              groupedLocations.country.map((location, i) => (
                <div
                  key={i}
                  className={`flex items-center justify-between p-1 sm:p-2 rounded transition-colors relative overflow-hidden group cursor-pointer text-xs sm:text-sm ${
                    activeFilters.some(
                      (filter) =>
                        filter.type === "location" &&
                        filter.value === location.country
                    )
                      ? "bg-blue-500/20"
                      : ""
                  }`}
                  onClick={() =>
                    handleLocationFilterClick(
                      "location",
                      location.country,
                      location.country
                    )
                  }
                >
                  <div
                    className="absolute inset-0 bg-blue-500/40 transition-transform origin-left group-hover:bg-blue-500/20"
                    style={{
                      transform: `scaleX(${
                        location.visitors / maxLocationVisitors
                      })`,
                    }}
                  />
                  <span className="text-white flex items-center gap-2 relative">
                    <span className="w-6 h-4 relative">
                      {getCountryFlagUrl(location.country) && (
                        <Image
                          src={getCountryFlagUrl(location.country)!}
                          alt={`${location.country} flag`}
                          fill
                          className="object-cover rounded-sm"
                        />
                      )}
                    </span>
                    <span className="truncate">{location.country}</span>
                  </span>
                  <span className="text-white ml-2 relative">
                    {location.visitors}
                  </span>
                </div>
              ))}
            {modalLocationView === "region" &&
              groupedLocations.region.map((location, i) => (
                <div
                  key={i}
                  className={`flex items-center justify-between p-1 sm:p-2 rounded transition-colors relative overflow-hidden group cursor-pointer text-xs sm:text-sm ${
                    activeFilters.some(
                      (filter) =>
                        filter.type === "location" &&
                        typeof filter.value === "object" &&
                        "region" in filter.value &&
                        filter.value.region === location.region &&
                        filter.value.country === location.country
                    )
                      ? "bg-blue-500/20"
                      : ""
                  }`}
                  onClick={() =>
                    handleLocationFilterClick(
                      "location",
                      { region: location.region, country: location.country },
                      `${location.region}, ${location.country}`
                    )
                  }
                >
                  <div
                    className="absolute inset-0 bg-blue-500/40 transition-transform origin-left group-hover:bg-blue-500/20"
                    style={{
                      transform: `scaleX(${
                        location.visitors / maxLocationVisitors
                      })`,
                    }}
                  />
                  <span className="text-white flex items-center gap-2 relative">
                    <span className="w-6 h-4 relative">
                      {getCountryFlagUrl(location.country) && (
                        <Image
                          src={getCountryFlagUrl(location.country)!}
                          alt={`${location.country} flag`}
                          fill
                          className="object-cover rounded-sm"
                        />
                      )}
                    </span>
                    <span className="truncate">
                      {location.region}, {location.country}
                    </span>
                  </span>
                  <span className="text-white ml-2 relative">
                    {location.visitors}
                  </span>
                </div>
              ))}
            {modalLocationView === "city" &&
              groupedLocations.city.map((location, i) => (
                <div
                  key={i}
                  className={`flex items-center justify-between p-1 sm:p-2 rounded transition-colors relative overflow-hidden group cursor-pointer text-xs sm:text-sm ${
                    activeFilters.some(
                      (filter) =>
                        filter.type === "location" &&
                        typeof filter.value === "object" &&
                        "city" in filter.value &&
                        filter.value.city === location.city &&
                        filter.value.country === location.country
                    )
                      ? "bg-blue-500/20"
                      : ""
                  }`}
                  onClick={() =>
                    handleLocationFilterClick(
                      "location",
                      { city: location.city, country: location.country },
                      `${location.city}, ${location.country}`
                    )
                  }
                >
                  <div
                    className="absolute inset-0 bg-blue-500/40 transition-transform origin-left group-hover:bg-blue-500/20"
                    style={{
                      transform: `scaleX(${
                        location.visitors / maxLocationVisitors
                      })`,
                    }}
                  />
                  <span className="text-white flex items-center gap-2 relative">
                    <span className="w-6 h-4 relative">
                      {getCountryFlagUrl(location.country) && (
                        <Image
                          src={getCountryFlagUrl(location.country)!}
                          alt={`${location.country} flag`}
                          fill
                          className="object-cover rounded-sm"
                        />
                      )}
                    </span>
                    <span className="truncate">
                      {location.city}, {location.country}
                    </span>
                  </span>
                  <span className="text-white ml-2 relative">
                    {location.visitors}
                  </span>
                </div>
              ))}
          </div>
        </div>
      </div>
    </div>
  );
}

export default LocationModal; 