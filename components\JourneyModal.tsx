import React, { RefObject, useMemo } from "react";
import Image from "next/image";
import { useCountryCode } from "@/hooks/useCountryCode";

type JourneyModalProps = {
  selectedJourney: {
    eventName: string;
    visitorId: string;
    timestamp: string;
  } | null;
  journeyEvents: any[];
  setShowJourneyModal: (show: boolean) => void;
  isLoadingJourney: boolean;
  journeyModalRef: RefObject<HTMLDivElement | null>;
};

// New interface to represent processed events with grouping
interface ProcessedEvent {
  type: string;
  originalEvent: any;
  timestamp: string;
  count?: number;
  isGrouped?: boolean;
  groupedEvents?: any[];
  referrer?: string;
  isGoal?: boolean;
}

function JourneyModal({
  selectedJourney,
  journeyEvents,
  setShowJourneyModal,
  isLoadingJourney,
  journeyModalRef,
}: JourneyModalProps) {
  const { getCountryFlagUrl } = useCountryCode();

  // Get domain for favicon
  const getFaviconDomain = (url: string) => {
    try {
      // Make sure URL has a protocol
      const fullUrl = url.startsWith('http') ? url : `https://${url}`;
      // Extract just the hostname (domain) part
      const hostname = new URL(fullUrl).hostname;
      // Remove www. prefix if present
      return hostname.replace(/^www\./, '');
    } catch (e) {
      // If URL parsing fails, try to extract domain manually
      const domainMatch = url.match(/^(?:https?:\/\/)?(?:www\.)?([^\/\?]+)/i);
      return domainMatch ? domainMatch[1] : url;
    }
  };

  // Process and group events
  const processedEvents = useMemo(() => {
    if (!journeyEvents || journeyEvents.length === 0) return [];

    // Find selected event to use as a goal
    let selectedEventTimestamp: Date | null = null;

    // When we have a selected journey (from a custom event), find its timestamp
    if (selectedJourney?.eventName) {
      // Find the event that matches the selected journey
      const matchingEvent = journeyEvents.find(
        (event) =>
          ((event.type === "custom_update" || event.type === "signup") &&
            event.extraData?.eventName === selectedJourney.eventName) ||
          event.extraData?.description
            ?.toLowerCase()
            .includes(selectedJourney.eventName.toLowerCase())
      );

      if (matchingEvent) {
        selectedEventTimestamp = new Date(matchingEvent.timestamp);
      }
    }

    // Filter for relevant events
    const filteredEvents = journeyEvents.filter((event) => {
      // Basic event type filtering
      const isRelevantType =
        event.type === "pageview_update" ||
        (event.type === "custom_update" && event.extraData?.eventName) ||
        event.type === "signup" ||
        event.type === "external_link" ||
        event.type === "external_link_update" ||
        (event.type !== "pageview" &&
          event.type !== "custom" &&
          event.type !== "pageview_update" &&
          event.type !== "custom_update");

      // If we have a selected event timestamp, filter out events after it
      if (selectedEventTimestamp && isRelevantType) {
        const eventTime = new Date(event.timestamp);
        return eventTime <= selectedEventTimestamp;
      }

      return isRelevantType;
    });

    // Keep track of seen signup events to only show the first one
    let hasProcessedSignup = false;

    // Process events in reverse chronological order for the timeline
    const result: ProcessedEvent[] = [...filteredEvents]
      .reverse()
      .filter((event) => {
        // Filter out duplicate signup events, keeping only the first one
        if (event.type === "signup") {
          if (!hasProcessedSignup) {
            hasProcessedSignup = true;
            return true;
          }
          return false;
        }
        return true;
      })
      .map((event) => {
        return {
          type: event.type,
          originalEvent: event,
          timestamp: event.timestamp,
          referrer: event.referrer,
          // Mark if this is the goal event
          isGoal:
            !!selectedJourney?.eventName &&
            (event.type === "custom_update" || event.type === "signup") &&
            event.extraData?.eventName === selectedJourney.eventName,
        };
      });

    return result;
  }, [journeyEvents, selectedJourney]);

  // Extract unique referrers to show at the top
  const referrerInfo = useMemo(() => {
    if (!journeyEvents || journeyEvents.length === 0) return null;

    // Find unique referrers (excluding direct visits)
    const referrersSet = new Set<string>();
    journeyEvents.forEach((event) => {
      if (event.referrer) {
        try {
          const hostname = new URL(event.referrer).hostname;
          referrersSet.add(hostname);
        } catch (e) {
          // Invalid URL, ignore
        }
      }
    });

    const referrers = Array.from(referrersSet);

    if (referrers.length === 0) {
      return "👉 Found directly";
    } else if (referrers.length === 1) {
      return `🔗 Found through ${referrers[0]}`;
    } else {
      return `⛓️ Found through ${referrers.join(", ")}`;
    }
  }, [journeyEvents]);

  return (
    <div
      className="fixed inset-0 bg-black/50 flex items-center justify-center z-[60] p-4 overflow-hidden"
      onClick={(e) => e.stopPropagation()}
    >
      <div
        ref={journeyModalRef}
        className="bg-[#1C1C1C] rounded-lg shadow-xl w-full max-w-2xl max-h-[80vh] overflow-hidden"
      >
        <div className="p-4 border-b border-gray-800 flex flex-col sm:flex-row sm:items-center justify-between gap-4">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 sm:w-12 sm:h-12 rounded-full bg-[#2A2A2A] flex items-center justify-center flex-shrink-0">
              <svg
                className="w-5 h-5 sm:w-6 sm:h-6 text-gray-400"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={1.5}
                  d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                />
              </svg>
            </div>
            <div className="min-w-0">
              <h3 className="text-base sm:text-lg font-medium text-white truncate">
                Visitor Journey
              </h3>
              {journeyEvents.length > 0 && (
                <div className="flex flex-col gap-1">
                  {(() => {
                    const eventWithPlatform = journeyEvents.find(
                      (event) =>
                        event.type === "pageview" ||
                        event.type === "pageview_update"
                    );

                    if (eventWithPlatform?.userAgent) {
                      const ua = eventWithPlatform.userAgent.toLowerCase();
                      let deviceType = "Desktop";
                      let browser = "";
                      let os = "";

                      // Detect device type
                      if (
                        ua.includes("mobile") ||
                        ua.includes("iphone") ||
                        ua.includes("android")
                      ) {
                        deviceType = "Mobile";
                      } else if (ua.includes("ipad") || ua.includes("tablet")) {
                        deviceType = "Tablet";
                      }

                      // Detect browser
                      if (ua.includes("firefox")) {
                        browser = "Firefox";
                      } else if (ua.includes("chrome")) {
                        browser = "Chrome";
                      } else if (ua.includes("safari")) {
                        browser = "Safari";
                      } else if (ua.includes("edge")) {
                        browser = "Edge";
                      }

                      // Detect OS
                      if (ua.includes("iphone") || ua.includes("ipad") || ua.includes("ios")) {
                        os = "iOS";
                      } else if (ua.includes("android")) {
                        os = "Android";
                      } else if (ua.includes("windows")) {
                        os = "Windows";
                      } else if (ua.includes("mac os")) {
                        os = "macOS";
                      } else if (ua.includes("linux")) {
                        os = "Linux";
                      } else if (ua.includes("cros")) {
                        os = "Chrome OS";
                      }

                      const platformInfo = [deviceType, os, browser]
                        .filter(Boolean)
                        .join(" • ");

                      return (
                        <p className="text-sm text-gray-400">
                          {platformInfo}
                        </p>
                      );
                    }
                    return null;
                  })()}
                  {(() => {
                    const eventWithLocation = journeyEvents.find(
                      (event) => event.location?.city && event.location?.country
                    );
                    if (eventWithLocation?.location) {
                      return (
                        <p className="text-sm text-gray-400 flex items-center gap-1">
                          <span className="w-6 h-4 relative">
                            {getCountryFlagUrl(
                              eventWithLocation.location.country
                            ) && (
                              <Image
                                src={
                                  getCountryFlagUrl(
                                    eventWithLocation.location.country
                                  )!
                                }
                                alt={`${eventWithLocation.location.country} flag`}
                                fill
                                className="object-cover rounded-sm"
                              />
                            )}
                          </span>
                          <span className="truncate max-w-[300px]">
                            {eventWithLocation.location.city},{" "}
                            {eventWithLocation.location.country}
                            {eventWithLocation.location.region &&
                              ` (${eventWithLocation.location.region})`}
                          </span>
                        </p>
                      );
                    }
                    return null;
                  })()}
                  {journeyEvents.length > 1 && (
                    <p className="text-sm text-gray-400 mt-1">
                      Time to complete:{" "}
                      {(() => {
                        const firstEvent =
                          journeyEvents[journeyEvents.length - 1];
                        const lastEvent = journeyEvents[0];
                        const duration =
                          new Date(lastEvent.timestamp).getTime() -
                          new Date(firstEvent.timestamp).getTime();

                        const months = Math.floor(
                          duration / (1000 * 60 * 60 * 24 * 30)
                        );
                        const days = Math.floor(
                          (duration % (1000 * 60 * 60 * 24 * 30)) /
                            (1000 * 60 * 60 * 24)
                        );
                        const hours = Math.floor(
                          (duration % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)
                        );
                        const minutes = Math.floor(
                          (duration % (1000 * 60 * 60)) / (1000 * 60)
                        );
                        const seconds = Math.floor(
                          (duration % (1000 * 60)) / 1000
                        );

                        const parts = [];
                        if (months > 0) parts.push(`${months}m`);
                        if (days > 0) parts.push(`${days}d`);
                        if (hours > 0) parts.push(`${hours}h`);
                        if (minutes > 0) parts.push(`${minutes}min`);
                        if (seconds > 0 || parts.length === 0)
                          parts.push(`${seconds}s`);

                        return parts.join(" ");
                      })()}
                    </p>
                  )}
                </div>
              )}
            </div>
          </div>
          <div className="flex flex-row sm:flex-col items-center sm:items-end gap-2 sm:gap-1 justify-between sm:justify-start">
            <button
              onClick={(e) => {
                e.stopPropagation();
                setShowJourneyModal(false);
              }}
              className="text-gray-500 hover:text-gray-400 transition-colors"
            >
              <svg
                className="w-5 h-5"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>
          </div>
        </div>
        <div className="p-4 overflow-y-auto max-h-[calc(80vh-5rem)]">
          {isLoadingJourney ? (
            <div className="flex items-center justify-center py-12">
              <div className="w-8 h-8 border-4 border-blue-500/30 border-t-blue-500 rounded-full animate-spin"></div>
            </div>
          ) : journeyEvents.length > 0 ? (
            <div className="space-y-1">
              {/* Referrer information at the top */}
              <div className="bg-gray-800/30 rounded-lg px-4 py-3 mb-4">
                <div className="flex items-center gap-2">
                  <span className="text-gray-300 text-sm">{referrerInfo}</span>
                </div>
              </div>

              {processedEvents.map((event, index, array) => {
                const eventDate = new Date(event.timestamp);
                const showDate =
                  index === 0 ||
                  new Date(array[index - 1].timestamp).toDateString() !==
                    eventDate.toDateString();

                const originalEvent = event.originalEvent;

                return (
                  <div key={index}>
                    {showDate && (
                      <div className="text-sm text-gray-500 py-3">
                        {eventDate.toLocaleDateString("en-US", {
                          weekday: "long",
                          month: "long",
                          day: "numeric",
                          year: "numeric",
                        })}
                      </div>
                    )}
                    <div
                      className="flex items-center group hover:bg-[#2A2A2A] rounded px-3 py-2 cursor-default transition-colors"
                      style={{
                        background: event.isGoal
                          ? "rgba(59, 130, 246, 0.1)"
                          : undefined,
                        borderLeft: event.isGoal
                          ? "3px solid #3b82f6"
                          : undefined,
                      }}
                    >
                      <div className="flex-1 flex items-center gap-3 min-w-0">
                        <div className="w-5 h-5 rounded-full flex items-center justify-center flex-shrink-0">
                          {event.type === "pageview_update" ? (
                            <svg
                              className="w-4 h-4 text-gray-400"
                              fill="none"
                              stroke="currentColor"
                              viewBox="0 0 24 24"
                            >
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                              />
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                              />
                            </svg>
                          ) : event.type === "custom_update" ? (
                            <svg
                              className="w-4 h-4 text-blue-400"
                              fill="none"
                              stroke="currentColor"
                              viewBox="0 0 24 24"
                            >
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M13 10V3L4 14h7v7l9-11h-7z"
                              />
                            </svg>
                          ) : event.type === "signup" ? (
                            <svg
                              className="w-4 h-4 text-green-400"
                              fill="none"
                              stroke="currentColor"
                              viewBox="0 0 24 24"
                            >
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z"
                              />
                            </svg>
                          ) : event.type === "external_link" || event.type === "external_link_update" ? (
                            <svg
                              className="w-4 h-4 text-blue-400"
                              fill="none"
                              stroke="currentColor"
                              viewBox="0 0 24 24"
                            >
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"
                              />
                            </svg>
                          ) : (
                            <svg
                              className="w-4 h-4 text-gray-400"
                              fill="none"
                              stroke="currentColor"
                              viewBox="0 0 24 24"
                            >
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                              />
                            </svg>
                          )}
                        </div>
                        <div className="min-w-0 flex-1">
                          <div className="flex items-center gap-2">
                            <span className="font-medium text-sm text-white truncate">
                              {event.type === "pageview_update" ? (
                                <>
                                  Viewed {(() => {
                                    try {
                                      return new URL(originalEvent.href).pathname;
                                    } catch (error) {
                                      // If href is not a valid URL, use it as-is
                                      return originalEvent.href.split('?')[0].split('#')[0];
                                    }
                                  })()}
                                  {originalEvent.count &&
                                    originalEvent.count > 1 && (
                                      <span className="text-gray-500 ml-1">
                                        ({originalEvent.count}x)
                                      </span>
                                    )}
                                </>
                              ) : event.type === "custom_update" ? (
                                <>
                                  <span
                                    className={
                                      event.isGoal ? "text-blue-400" : ""
                                    }
                                  >
                                    {originalEvent.extraData?.description ||
                                      originalEvent.extraData?.eventName ||
                                      "Custom Event"}
                                  </span>
                                  {event.isGoal && (
                                    <span className="bg-blue-400/10 text-blue-400 text-xs rounded-full px-2 py-0.5 ml-2">
                                      Goal
                                    </span>
                                  )}
                                </>
                              ) : event.type === "signup" ? (
                                <>
                                  <span
                                    className={
                                      event.isGoal
                                        ? "text-blue-400"
                                        : "text-green-400"
                                    }
                                  >
                                    User signup
                                  </span>
                                  {event.isGoal && (
                                    <span className="bg-blue-400/10 text-blue-400 text-xs rounded-full px-2 py-0.5 ml-2">
                                      Goal
                                    </span>
                                  )}
                                </>
                              ) : event.type === "external_link" || event.type === "external_link_update" ? (
                                <div className="flex items-center gap-2">
                                  <img
                                    src={`https://www.google.com/s2/favicons?domain=${getFaviconDomain(originalEvent.extraData?.url)}&sz=32`}
                                    alt=""
                                    className="w-4 h-4 flex-shrink-0"
                                    onError={(e) => {
                                      const target = e.target as HTMLImageElement;
                                      target.onerror = null; // Prevent infinite loop
                                      
                                      // Try DuckDuckGo as first fallback
                                      const domain = getFaviconDomain(originalEvent.extraData?.url);
                                      target.src = `https://icons.duckduckgo.com/ip3/${domain}.ico`;
                                      
                                      // Add another fallback in case DuckDuckGo fails
                                      target.onerror = () => {
                                        target.onerror = null;
                                        target.src = "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='16' height='16'%3E%3Cpath fill='none' stroke='%234B5563' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14'%3E%3C/path%3E%3C/svg%3E";
                                      };
                                    }}
                                  />
                                  <span className="truncate">
                                    Exited to {getFaviconDomain(originalEvent.extraData?.url)}
                                  </span>
                                  <button
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      window.open(originalEvent.extraData?.url, "_blank");
                                    }}
                                    className="ml-2 p-1 hover:bg-blue-500/20 rounded opacity-0 group-hover:opacity-100 transition-opacity"
                                    title="Open link"
                                  >
                                    <svg
                                      className="w-4 h-4 text-blue-400"
                                      fill="none"
                                      stroke="currentColor"
                                      viewBox="0 0 24 24"
                                    >
                                      <path
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        strokeWidth={2}
                                        d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"
                                      />
                                    </svg>
                                  </button>
                                </div>
                              ) : originalEvent.type !== "pageview" &&
                                originalEvent.type !== "custom" ? (
                                originalEvent.type.replace(/_/g, " ")
                              ) : null}
                            </span>
                          </div>
                          {originalEvent.extraData?.email &&
                            event.type === "signup" && (
                              <div className="text-xs text-blue-400 truncate">
                                {originalEvent.extraData.email}
                              </div>
                            )}
                        </div>
                      </div>
                      <div className="text-right flex-shrink-0 ml-3">
                        <div className="text-sm text-gray-400 whitespace-nowrap">
                          {eventDate.toLocaleTimeString("en-US", {
                            hour: "numeric",
                            minute: "numeric",
                            hour12: true,
                          })}
                        </div>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          ) : (
            <div className="text-center py-12 text-gray-500">
              <svg
                className="w-12 h-12 mx-auto mb-4 text-gray-600"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={1.5}
                  d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"
                />
              </svg>
              <p>No journey events found for this visitor</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
export default JourneyModal;
