import { useState, useEffect, useCallback } from 'react';
import { Period, CustomPeriod } from '@/lib/types';

export interface RevenueMetrics {
  totalRevenue: {
    current: number;
    previous: number;
    delta: number;
    currency: string;
  };
  averageOrderValue: {
    current: number;
    previous: number;
    delta: number;
    currency: string;
  };
  conversionRate: {
    current: number;
    totalVisitors: number;
    totalOrders: number;
  };
  revenuePerVisitor: {
    current: number;
    currency: string;
  };
  orderCount: {
    current: number;
    previous: number;
    delta: number;
  };
  customerMetrics: {
    totalCustomers: number;
    newCustomers: number;
    returningCustomers: number;
    newCustomerRate: number;
  };
  revenueByType: {
    oneTime: number;
    subscription: number;
    breakdown: Array<{ type: string; amount: number }>;
  };
  period: {
    start: Date;
    end: Date;
    label: string;
  };
}

export interface RevenueFilters {
  amountRange: {
    min: number | null;
    max: number | null;
  };
  customerType: "all" | "new" | "returning";
  paymentMethod: "all" | "card" | "bank_transfer" | "other";
  paymentType: "all" | "one_time" | "subscription" | "renewal";
  currency: string;
  dateRange: {
    start: Date | null;
    end: Date | null;
  };
}

export function useRevenueAnalytics(
  websiteId: string,
  selectedPeriod: Period,
  customPeriod?: CustomPeriod,
  filters?: {
    referrer?: string;
    location?: string;
    system?: {
      type: "os" | "browser" | "device";
      value: string;
    };
  },
  revenueFilters?: RevenueFilters
) {
  const [revenueData, setRevenueData] = useState<RevenueMetrics | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isRevenueEnabled, setIsRevenueEnabled] = useState(false);

  const fetchRevenueData = useCallback(async () => {
    if (!websiteId) return;

    try {
      setIsLoading(true);
      setError(null);

      const searchParams = new URLSearchParams({
        websiteId,
        period: selectedPeriod,
      });

      // Add custom date range if provided
      if (customPeriod) {
        searchParams.set('startDate', customPeriod.startDate.toISOString());
        searchParams.set('endDate', customPeriod.endDate.toISOString());
      }

      // Add filters if provided
      if (filters?.referrer) {
        searchParams.set('referrer', filters.referrer);
      }
      if (filters?.location) {
        searchParams.set('location', filters.location);
      }
      // Add system filters
      if (filters?.system) {
        searchParams.set('system', filters.system.value);
        searchParams.set('systemType', filters.system.type);
      }

      // Add revenue-specific filters
      if (revenueFilters) {
        if (revenueFilters.amountRange.min !== null) {
          searchParams.set('minAmount', revenueFilters.amountRange.min.toString());
        }
        if (revenueFilters.amountRange.max !== null) {
          searchParams.set('maxAmount', revenueFilters.amountRange.max.toString());
        }
        if (revenueFilters.customerType !== 'all') {
          searchParams.set('customerType', revenueFilters.customerType);
        }
        if (revenueFilters.paymentMethod !== 'all') {
          searchParams.set('paymentMethod', revenueFilters.paymentMethod);
        }
        if (revenueFilters.paymentType !== 'all') {
          searchParams.set('paymentType', revenueFilters.paymentType);
        }
        if (revenueFilters.currency !== 'all') {
          searchParams.set('currency', revenueFilters.currency);
        }
        if (revenueFilters.dateRange.start) {
          searchParams.set('revenueStartDate', revenueFilters.dateRange.start.toISOString());
        }
        if (revenueFilters.dateRange.end) {
          searchParams.set('revenueEndDate', revenueFilters.dateRange.end.toISOString());
        }
      }

      const response = await fetch(`/api/analytics/revenue?${searchParams.toString()}`);
      
      if (response.status === 404) {
        // Revenue attribution not enabled for this website
        setIsRevenueEnabled(false);
        setRevenueData(null);
        return;
      }

      if (!response.ok) {
        throw new Error(`Failed to fetch revenue data: ${response.status}`);
      }

      const data = await response.json();
      setRevenueData(data);
      setIsRevenueEnabled(true);

    } catch (err: any) {
      setError(err.message || 'Failed to fetch revenue data');
      setIsRevenueEnabled(false);
      setRevenueData(null);
    } finally {
      setIsLoading(false);
    }
  }, [
    websiteId, 
    selectedPeriod, 
    customPeriod, 
    // Stringify filters and revenueFilters to prevent unnecessary re-renders
    filters ? JSON.stringify(filters) : null,
    revenueFilters ? JSON.stringify(revenueFilters) : null
  ]);

  useEffect(() => {
    fetchRevenueData();
  }, [fetchRevenueData]);

  const formatCurrency = useCallback((amount: number, currency = 'usd') => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency.toUpperCase(),
      minimumFractionDigits: amount < 1 ? 2 : 0,
      maximumFractionDigits: 2,
    }).format(amount);
  }, []);

  const formatPercentage = useCallback((value: number, showSign = true) => {
    const formatted = Math.abs(value).toFixed(1);
    const sign = showSign && value !== 0 ? (value > 0 ? '+' : '-') : '';
    return `${sign}${formatted}%`;
  }, []);

  return {
    revenueData,
    isLoading,
    error,
    isRevenueEnabled,
    refetch: fetchRevenueData,
    formatCurrency,
    formatPercentage,
  };
} 