import { useQuery } from "@tanstack/react-query";
import { Event, Period, CustomPeriod } from "@/lib/types";

interface FetchEventsParams {
  websiteId: string;
  from: string;
  to: string;
}

// Fetch events from API with React Query caching
const fetchEvents = async ({ websiteId, from, to }: FetchEventsParams): Promise<Event[]> => {
  const response = await fetch(
    `/api/events?websiteId=${websiteId}&from=${from}&to=${to}`
  );
  
  if (!response.ok) {
    throw new Error("Failed to fetch events");
  }
  
  return response.json();
};

/**
 * Helper function to calculate date range from period
 */
function getDateRangeFromPeriod(period: Period, customPeriod?: CustomPeriod): { from: Date; to: Date } {
  const now = new Date();
  let to = new Date(now);
  to.setHours(23, 59, 59, 999);
  let from = new Date(now);
  from.setHours(0, 0, 0, 0);

  switch (period) {
    case "today":
      // from is already set to today at 00:00
      break;
    case "yesterday":
      from.setDate(from.getDate() - 1);
      to.setDate(to.getDate() - 1);
      break;
    case "last7d":
      from.setDate(from.getDate() - 6);
      from.setHours(0, 0, 0, 0);
      break;
    case "last30d":
      from.setDate(from.getDate() - 29);
      from.setHours(0, 0, 0, 0);
      break;
    case "mtd": // month to date
      from.setDate(1);
      break;
    case "wtd": // week to date
      // Get to first day of week (Monday)
      const day = from.getDay();
      const diff = from.getDate() - day + (day === 0 ? -6 : 1);
      from.setDate(diff);
      break;
    case "ytd": // year to date
      from.setMonth(0, 1); // January 1st
      break;
    case "last24h":
      from = new Date(now.getTime() - 24 * 60 * 60 * 1000);
      to = now;
      break;
    case "custom":
      if (customPeriod?.startDate) {
        from = new Date(customPeriod.startDate);
        from.setHours(0, 0, 0, 0);
      }
      if (customPeriod?.endDate) {
        to = new Date(customPeriod.endDate);
        to.setHours(23, 59, 59, 999);
      }
      break;
    case "all":
      // Set from to the beginning of time for the app
      from = new Date(2023, 0, 1);
      break;
  }

  return { from, to };
}

/**
 * Hook for fetching analytics data with React Query caching
 */
export function useAnalyticsApi(
  websiteId: string,
  selectedPeriod: Period,
  customPeriod?: CustomPeriod
) {
  // Helper function to get date range based on period
  const getDateRange = (period: Period) => {
    const range = getDateRangeFromPeriod(period, customPeriod);
    return {
      from: range.from.toISOString(),
      to: range.to.toISOString()
    };
  };

  // Get date range based on selected period
  const { from, to } = getDateRange(selectedPeriod);
  
  // Calculate previous period range
  const getPreviousPeriodRange = (currentFrom: string, currentTo: string) => {
    const fromDate = new Date(currentFrom);
    const toDate = new Date(currentTo);
    const duration = toDate.getTime() - fromDate.getTime();
    
    const prevFromDate = new Date(fromDate.getTime() - duration);
    const prevToDate = new Date(fromDate.getTime() - 1);
    
    return {
      from: prevFromDate.toISOString(),
      to: prevToDate.toISOString()
    };
  };
  
  const { from: prevFrom, to: prevTo } = getPreviousPeriodRange(from, to);

  // Current period events
  const currentPeriodQuery = useQuery({
    queryKey: ['events', websiteId, from, to],
    queryFn: () => fetchEvents({ websiteId, from, to }),
    staleTime: 5 * 60 * 1000, // 5 minutes
    // For historical data, cache longer
    gcTime: from && new Date(from) < new Date(Date.now() - 24 * 60 * 60 * 1000) 
      ? 60 * 60 * 1000  // 1 hour for historical data
      : 5 * 60 * 1000,  // 5 minutes for recent data
  });

  // Previous period events
  const prevPeriodQuery = useQuery({
    queryKey: ['events', websiteId, prevFrom, prevTo],
    queryFn: () => fetchEvents({ websiteId, from: prevFrom, to: prevTo }),
    staleTime: 10 * 60 * 1000, // 10 minutes for previous period (less critical)
    gcTime: 30 * 60 * 1000, // 30 minutes
  });

  return {
    events: currentPeriodQuery.data || [],
    prevEvents: prevPeriodQuery.data || [],
    isLoading: currentPeriodQuery.isLoading || prevPeriodQuery.isLoading,
    isError: currentPeriodQuery.isError || prevPeriodQuery.isError,
    error: currentPeriodQuery.error || prevPeriodQuery.error,
    refetch: () => {
      currentPeriodQuery.refetch();
      prevPeriodQuery.refetch();
    }
  };
} 