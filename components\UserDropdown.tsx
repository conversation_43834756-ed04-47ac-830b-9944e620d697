"use client";
import React, { useState } from "react";
import { useUser, useClerk } from "@clerk/nextjs";
import Image from "next/image";
import Link from "next/link";
import {
  Settings,
  CreditCard,
  MessageSquare,
  LifeBuoy,
  ExternalLink,
  LogOut,
  Book,
} from "lucide-react";
import config from "@/config";

export default function UserDropdown() {
  const { user } = useUser();
  const { signOut } = useClerk();
  const [isOpen, setIsOpen] = useState(false);

  if (!user) return null;

  // <PERSON>le sign out with custom function
  const handleSignOut = async () => {
    setIsOpen(false);
    await signOut();
  };

  const menuItems = [
    {
      label: "Docs",
      icon: <Book size={16} />,
      href: "https://versatailor.com/docs",
      external: true,
    },
    {
      label: "Feedback",
      icon: <MessageSquare size={16} />,
      href: "https://versatailor.featurebase.app",
      external: true,
    },
    {
      label: "Support",
      icon: <LifeBuoy size={16} />,
      href: `mailto:${config.emails.supportEmail}`,
    },
    {
      type: "divider",
    },
    {
      label: "Billing",
      icon: <CreditCard size={16} />,
      href: "/billing",
    },
    {
      type: "divider",
    },
    {
      label: "Sign Out",
      icon: <LogOut size={16} />,
      onClick: handleSignOut,
      type: "button",
    },
  ];

  return (
    <div className="relative">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center gap-2 rounded-full hover:opacity-80 transition-opacity"
      >
        <Image
          src={user.imageUrl}
          alt={user.fullName || "User"}
          width={32}
          height={32}
          className="rounded-full"
        />
      </button>

      {isOpen && (
        <>
          <div
            className="fixed inset-0 z-30"
            onClick={() => setIsOpen(false)}
          ></div>
          <div className="absolute right-0 mt-2 min-w-[240px] z-40 bg-[#1C1C1C] border border-gray-700 rounded-lg shadow-lg">
            <div className="px-4 py-3 border-b border-gray-700 flex flex-col items-center">
              <div className="font-medium truncate text-sm">
                {user.fullName || "User"}
              </div>
              <div className="text-xs text-gray-400 truncate mt-0.5">
                {user.primaryEmailAddress?.emailAddress || ""}
              </div>
            </div>
            <div className="py-1.5">
              {menuItems.map((item, index) => {
                if (item.type === "divider") {
                  return (
                    <div
                      key={`divider-${index}`}
                      className="h-px bg-gray-700 my-1.5"
                    />
                  );
                }

                if (item.type === "button") {
                  return (
                    <button
                      key={`button-${index}`}
                      onClick={item.onClick}
                      className="flex items-center gap-3 px-4 py-2.5 text-sm text-gray-300 hover:bg-gray-800 hover:text-white transition-colors w-full text-left"
                    >
                      {item.icon}
                      {item.label}
                    </button>
                  );
                }

                return (
                  <Link
                    key={`link-${index}`}
                    href={item.href || "#"}
                    className="flex items-center gap-3 px-4 py-2.5 text-sm text-gray-300 hover:bg-gray-800 hover:text-white transition-colors"
                    onClick={() => setIsOpen(false)}
                    {...(item.external
                      ? { target: "_blank", rel: "noopener noreferrer" }
                      : {})}
                  >
                    {item.icon}
                    {item.label}
                    {item.external && (
                      <ExternalLink size={16} className="ml-auto" />
                    )}
                  </Link>
                );
              })}
            </div>
          </div>
        </>
      )}
    </div>
  );
}
