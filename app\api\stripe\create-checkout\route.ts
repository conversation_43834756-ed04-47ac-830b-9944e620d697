import { stripe } from "@/lib/stripe";
import { auth } from "@clerk/nextjs/server";
import { NextRequest, NextResponse } from "next/server";
import User from "@/models/user";
import connect from "@/util/db";

export async function POST(req: NextRequest) {
  try {
    await connect();
    const { priceId } = await req.json();
    const { userId } = await auth();

    if (!userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    if (!priceId) {
      return NextResponse.json(
        { error: "Price ID is required" },
        { status: 400 }
      );
    }

    // Fetch user data
    const user = await User.findOne({ clerkId: userId });

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    const trialEnd = Math.max(
      Math.floor(user.accessUntil?.getTime() / 1000) ||
        Math.floor(Date.now() / 1000),
      Math.floor(Date.now() / 1000 + 14 * 24 * 60 * 60)
    );

    const session = await stripe.checkout.sessions.create({
      mode: "subscription",
      line_items: [
        {
          price: priceId,
          quantity: 1,
        },
      ],
      success_url: `${req.headers.get("origin")}/dashboard?success=true`,
      cancel_url:
        process.env.NODE_ENV === "development"
          ? `${req.headers.get("origin")}/billing`
          : `${req.headers.get("origin")}/dashboard?canceled=true`,
      metadata: {
        userId: userId,
      },
      subscription_data: {
        trial_end: trialEnd,
      },
      customer_email: user.email,
    });

    return NextResponse.json({ url: session.url });
  } catch (error) {
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
