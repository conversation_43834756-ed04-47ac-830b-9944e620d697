import { Metadata } from 'next';
import { getMetadata } from '@/lib/docs-metadata';
import Link from "next/link";

export const metadata: Metadata = getMetadata('/docs');

export default function DocsWelcomePage() {
  return (
    <div className="max-w-4xl mx-auto">
      {/* Page title */}
      <h1 className="text-4xl font-bold mb-8 text-white">
        Welcome to Versatailor Docs
      </h1>

      {/* Main content */}
      <div className="space-y-8">
        <p className="text-lg text-gray-200 leading-relaxed">
          Versatailor is the ultimate web analytics tool for online businesses.
          We offer typical web analytics features as well as hour-by-hour
          analysis and filtering out internal traffic.
        </p>

        <p className="text-gray-300 leading-relaxed">
          We have a public roadmap you can find
          <Link
            className="text-blue-400 hover:text-blue-300 transition-colors font-medium"
            href={"https://versatailor.featurebase.app/"}
            target="_blank"
          >
            {" "}
            here
          </Link>
          . We always keep an eye on it and will be actively updated as
          Versatailor evolves.
        </p>

        <p className="text-gray-300 leading-relaxed">
          Can&apos;t find what you&apos;re looking for? Please do{" "}
          <Link
            href="mailto:<EMAIL>"
            className="text-blue-400 hover:text-blue-300 transition-colors font-medium"
            target="_blank"
          >
            contact us
          </Link>{" "}
          for assistance.
        </p>

        <p className="text-lg text-gray-200 leading-relaxed">
          We&apos;re excited to have you on board!
        </p>

        {/* Getting started section */}
        <div className="mt-16">
          <h2 className="text-2xl font-bold mb-6 text-white">
            What would you like to do next?
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Link
              href="/docs/set-up-your-account"
              className="group block p-6 rounded-xl border border-gray-800/50 bg-customGrayLight/70 hover:bg-customGrayLight transition-colors duration-200 shadow-sm hover:shadow"
            >
              <h3 className="text-lg font-semibold mb-3 text-white group-hover:text-blue-300 transition-colors">
                Set up your account
              </h3>
              <p className="text-gray-400 leading-relaxed">
                Get started by creating your account and installing the tracking
                script.
              </p>
            </Link>

            <Link
              href="/docs/track-events"
              className="group block p-6 rounded-xl border border-gray-800/50 bg-customGrayLight/70 hover:bg-customGrayLight transition-colors duration-200 shadow-sm hover:shadow"
            >
              <h3 className="text-lg font-semibold mb-3 text-white group-hover:text-blue-300 transition-colors">
                Track Custom Events
              </h3>
              <p className="text-gray-400 leading-relaxed">
                Learn how to track specific user interactions and custom events
                on your website.
              </p>
            </Link>

            <Link
              href="/docs/filter-settings"
              className="group block p-6 rounded-xl border border-gray-800/50 bg-customGrayLight/70 hover:bg-customGrayLight transition-colors duration-200 shadow-sm hover:shadow"
            >
              <h3 className="text-lg font-semibold mb-3 text-white group-hover:text-blue-300 transition-colors">
                Filter Settings
              </h3>
              <p className="text-gray-400 leading-relaxed">
                Configure filters to exclude internal traffic and focus on real
                user analytics.
              </p>
            </Link>

            <Link
              href="/docs/import-your-data"
              className="group block p-6 rounded-xl border border-gray-800/50 bg-customGrayLight/70 hover:bg-customGrayLight transition-colors duration-200 shadow-sm hover:shadow"
            >
              <h3 className="text-lg font-semibold mb-3 text-white group-hover:text-blue-300 transition-colors">
                Import Your Data
              </h3>
              <p className="text-gray-400 leading-relaxed">
                Learn how to import your existing analytics data from other
                platforms.
              </p>
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
