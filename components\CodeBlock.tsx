"use client";

import { useState } from "react";
import { Copy, Check } from "lucide-react";
import { Button } from "@/components/ui/button";
import toast from "react-hot-toast";

interface CodeBlockProps {
  code: string;
  language?: string; // Optional language prop for potential future syntax highlighting
}

export default function CodeBlock({ code, language }: CodeBlockProps) {
  const [isCopied, setIsCopied] = useState(false);

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(code);
      setIsCopied(true);
      toast.success("Copied to clipboard!");
      setTimeout(() => setIsCopied(false), 2000); // Reset icon after 2 seconds
    } catch (err) {
      console.error("Failed to copy text: ", err);
      toast.error("Failed to copy to clipboard.");
    }
  };

  return (
    <div className="relative group bg-gray-900 rounded-lg border border-gray-700/50 overflow-hidden my-4">
      <pre
        className={`p-4 text-sm overflow-x-auto whitespace-pre-wrap break-all ${
          language ? `language-${language}` : ""
        }`}
      >
        <code className="font-mono text-gray-300">{code}</code>
      </pre>
      <Button
        variant="ghost"
        size="icon"
        // Temporarily removed opacity-0 group-hover:opacity-100 to make button always visible
        className="absolute top-2 right-2 h-7 w-7 text-gray-400 hover:text-white hover:bg-gray-700 transition-opacity"
        onClick={handleCopy}
        aria-label="Copy code to clipboard"
      >
        {isCopied ? (
          <Check className="h-4 w-4 text-green-500" />
        ) : (
          <Copy className="h-4 w-4" />
        )}
      </Button>
    </div>
  );
}
