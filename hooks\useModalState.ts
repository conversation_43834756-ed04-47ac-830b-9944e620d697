import { useState, useRef, useEffect } from 'react';

export function useModalState() {
  // Modal visibility states
  const [showReferrerModal, setShowReferrerModal] = useState(false);
  const [showPageModal, setShowPageModal] = useState(false);
  const [showLocationModal, setShowLocationModal] = useState(false);
  const [showSystemModal, setShowSystemModal] = useState(false);
  const [showJourneyModal, setShowJourneyModal] = useState(false);
  const [showJourneyListModal, setShowJourneyListModal] = useState(false);

  // Modal refs for click outside detection
  const referrerModalRef = useRef<HTMLDivElement>(null);
  const pageModalRef = useRef<HTMLDivElement>(null);
  const locationModalRef = useRef<HTMLDivElement>(null);
  const systemModalRef = useRef<HTMLDivElement>(null);
  const journeyModalRef = useRef<HTMLDivElement>(null);
  const journeyListModalRef = useRef<HTMLDivElement>(null);

  // View type states
  const [locationView, setLocationView] = useState<'country' | 'region' | 'city'>('country');
  const [systemView, setSystemView] = useState<'browser' | 'os' | 'device'>('browser');
  const [referrerView, setReferrerView] = useState<'referrer' | 'campaign' | 'utm'>('referrer');

  // Handle click outside to close modals
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (
        showReferrerModal &&
        referrerModalRef.current &&
        !referrerModalRef.current.contains(event.target as Node)
      ) {
        setShowReferrerModal(false);
      }
      if (
        showPageModal &&
        pageModalRef.current &&
        !pageModalRef.current.contains(event.target as Node)
      ) {
        setShowPageModal(false);
      }
      if (
        showLocationModal &&
        locationModalRef.current &&
        !locationModalRef.current.contains(event.target as Node)
      ) {
        setShowLocationModal(false);
      }
      if (
        showSystemModal &&
        systemModalRef.current &&
        !systemModalRef.current.contains(event.target as Node)
      ) {
        setShowSystemModal(false);
      }
      if (
        showJourneyModal &&
        journeyModalRef.current &&
        !journeyModalRef.current.contains(event.target as Node)
      ) {
        setShowJourneyModal(false);
      }
      if (
        showJourneyListModal &&
        journeyListModalRef.current &&
        !journeyListModalRef.current.contains(event.target as Node)
      ) {
        setShowJourneyListModal(false);
      }
    }

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [
    showReferrerModal,
    showPageModal,
    showLocationModal,
    showSystemModal,
    showJourneyModal,
    showJourneyListModal,
  ]);

  return {
    // Modal visibility states
    showReferrerModal,
    setShowReferrerModal,
    showPageModal,
    setShowPageModal,
    showLocationModal,
    setShowLocationModal,
    showSystemModal,
    setShowSystemModal,
    showJourneyModal,
    setShowJourneyModal,
    showJourneyListModal,
    setShowJourneyListModal,
    
    // Modal refs
    referrerModalRef,
    pageModalRef,
    locationModalRef,
    systemModalRef,
    journeyModalRef,
    journeyListModalRef,
    
    // View type states
    locationView,
    setLocationView,
    systemView,
    setSystemView,
    referrerView,
    setReferrerView
  };
} 