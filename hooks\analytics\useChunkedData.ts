import { useMemo } from "react";
import { ChunkedAnalytics, CustomPeriod, Filter, Period } from "./types";
import { useAnalyticsData } from "../useAnalyticsData";

export function useChunkedAnalytics(
  websiteId: string,
  selectedPeriod: Period,
  activeFilters: Filter[],
  customPeriod?: CustomPeriod
): ChunkedAnalytics {
  // Reuse the existing analytics data
  const analytics = useAnalyticsData({
    websiteId,
    selectedPeriod,
    activeFilters,
    customPeriod,
  });

  // Split data into chunks with individual memoization
  const basic = useMemo(
    () => ({
      isLoading: analytics.isLoading,
      uniqueVisitors: analytics.uniqueVisitors,
      currentVisitors: analytics.currentVisitors,
      totalPageviews: analytics.totalPageviews,
    }),
    [
      analytics.isLoading,
      analytics.uniqueVisitors,
      analytics.currentVisitors,
      analytics.totalPageviews,
    ]
  );

  const metrics = useMemo(
    () => ({
      bounceRate: analytics.bounceRate,
      avgVisitTime: analytics.avgVisitTime,
      visitorsDelta: analytics.visitorsDelta,
      pageviewsDelta: analytics.pageviewsDelta,
    }),
    [
      analytics.bounceRate,
      analytics.avgVisitTime,
      analytics.visitorsDelta,
      analytics.pageviewsDelta,
    ]
  );

  const deltas = useMemo(
    () => ({
      visitorsDeltaPercentage: analytics.visitorsDeltaPercentage,
      bounceRateDelta: analytics.bounceRateDelta,
      avgVisitTimeDeltaPercentage: analytics.avgVisitTimeDeltaPercentage,
      avgVisitTimeDelta: analytics.avgVisitTimeDelta,
    }),
    [
      analytics.visitorsDeltaPercentage,
      analytics.bounceRateDelta,
      analytics.avgVisitTimeDeltaPercentage,
      analytics.avgVisitTimeDelta,
    ]
  );

  const charts = useMemo(
    () => ({
      chartData: analytics.chartData,
      visitorsSeries: analytics.visitorsSeries,
    }),
    [analytics.chartData, analytics.visitorsSeries]
  );

  const details = useMemo(
    () => ({
      topPages: analytics.topPages,
      entryPages: analytics.entryPages,
      exitPages: analytics.exitPages,
      topReferrers: analytics.topReferrers,
      operatingSystems: analytics.operatingSystems,
      browsers: analytics.browsers,
      devices: analytics.devices,
      topLocations: analytics.topLocations,
    }),
    [
      analytics.topPages,
      analytics.entryPages,
      analytics.exitPages,
      analytics.topReferrers,
      analytics.operatingSystems,
      analytics.browsers,
      analytics.devices,
      analytics.topLocations,
    ]
  );

  const events = useMemo(
    () => ({
      events: analytics.events,
      allEvents: analytics.allEvents,
    }),
    [analytics.events, analytics.allEvents]
  );

  return {
    basic,
    metrics,
    deltas,
    charts,
    details,
    events,
  };
}
