const COUNTRY_NAME_TO_CODE: { [key: string]: string } = {
  "afghanistan": "AF",
  "albania": "AL",
  "algeria": "DZ",
  "andorra": "AD",
  "angola": "AO",
  "antigua and barbuda": "AG",
  "argentina": "AR",
  "armenia": "AM",
  "australia": "AU",
  "austria": "AT",
  "azerbaijan": "AZ",
  "bahamas": "BS",
  "bahrain": "BH",
  "bangladesh": "BD",
  "barbados": "BB",
  "belarus": "BY",
  "belgium": "BE",
  "belize": "BZ",
  "benin": "BJ",
  "bhutan": "BT",
  "bolivia": "BO",
  "bosnia and herzegovina": "BA",
  "botswana": "BW",
  "brazil": "BR",
  "brunei": "BN",
  "bulgaria": "BG",
  "burkina faso": "BF",
  "burundi": "BI",
  "cambodia": "KH",
  "cameroon": "CM",
  "canada": "CA",
  "cape verde": "CV",
  "central african republic": "CF",
  "chad": "TD",
  "chile": "CL",
  "china": "CN",
  "colombia": "CO",
  "comoros": "KM",
  "congo": "CG",
  "costa rica": "CR",
  "croatia": "HR",
  "cuba": "CU",
  "cyprus": "CY",
  "czech republic": "CZ",
  "czechia": "CZ",
  "democratic republic of the congo": "CD",
  "denmark": "DK",
  "djibouti": "DJ",
  "dominica": "DM",
  "dominican republic": "DO",
  "east timor": "TL",
  "ecuador": "EC",
  "egypt": "EG",
  "el salvador": "SV",
  "equatorial guinea": "GQ",
  "eritrea": "ER",
  "estonia": "EE",
  "eswatini": "SZ",
  "ethiopia": "ET",
  "fiji": "FJ",
  "finland": "FI",
  "france": "FR",
  "gabon": "GA",
  "gambia": "GM",
  "georgia": "GE",
  "germany": "DE",
  "ghana": "GH",
  "greece": "GR",
  "grenada": "GD",
  "guatemala": "GT",
  "guinea": "GN",
  "guinea-bissau": "GW",
  "guyana": "GY",
  "haiti": "HT",
  "honduras": "HN",
  "hong kong": "HK",
  "hungary": "HU",
  "iceland": "IS",
  "india": "IN",
  "indonesia": "ID",
  "iran": "IR",
  "iraq": "IQ",
  "ireland": "IE",
  "israel": "IL",
  "italy": "IT",
  "ivory coast": "CI",
  "jamaica": "JM",
  "japan": "JP",
  "jordan": "JO",
  "kazakhstan": "KZ",
  "kenya": "KE",
  "kiribati": "KI",
  "kuwait": "KW",
  "kyrgyzstan": "KG",
  "laos": "LA",
  "latvia": "LV",
  "lebanon": "LB",
  "lesotho": "LS",
  "liberia": "LR",
  "libya": "LY",
  "liechtenstein": "LI",
  "lithuania": "LT",
  "luxembourg": "LU",
  "madagascar": "MG",
  "malawi": "MW",
  "malaysia": "MY",
  "maldives": "MV",
  "mali": "ML",
  "malta": "MT",
  "marshall islands": "MH",
  "mauritania": "MR",
  "mauritius": "MU",
  "mexico": "MX",
  "micronesia": "FM",
  "moldova": "MD",
  "monaco": "MC",
  "mongolia": "MN",
  "montenegro": "ME",
  "morocco": "MA",
  "mozambique": "MZ",
  "myanmar": "MM",
  "namibia": "NA",
  "nauru": "NR",
  "nepal": "NP",
  "netherlands": "NL",
  "the netherlands": "NL",
  "new zealand": "NZ",
  "nicaragua": "NI",
  "niger": "NE",
  "nigeria": "NG",
  "north korea": "KP",
  "north macedonia": "MK",
  "norway": "NO",
  "oman": "OM",
  "pakistan": "PK",
  "palau": "PW",
  "palestine": "PS",
  "panama": "PA",
  "papua new guinea": "PG",
  "paraguay": "PY",
  "peru": "PE",
  "philippines": "PH",
  "poland": "PL",
  "portugal": "PT",
  "qatar": "QA",
  "romania": "RO",
  "russia": "RU",
  "rwanda": "RW",
  "saint kitts and nevis": "KN",
  "saint lucia": "LC",
  "saint vincent and the grenadines": "VC",
  "samoa": "WS",
  "san marino": "SM",
  "sao tome and principe": "ST",
  "saudi arabia": "SA",
  "senegal": "SN",
  "serbia": "RS",
  "seychelles": "SC",
  "sierra leone": "SL",
  "singapore": "SG",
  "slovakia": "SK",
  "slovenia": "SI",
  "solomon islands": "SB",
  "somalia": "SO",
  "south africa": "ZA",
  "south korea": "KR",
  "south sudan": "SS",
  "spain": "ES",
  "sri lanka": "LK",
  "sudan": "SD",
  "suriname": "SR",
  "sweden": "SE",
  "switzerland": "CH",
  "syria": "SY",
  "taiwan": "TW",
  "tajikistan": "TJ",
  "tanzania": "TZ",
  "thailand": "TH",
  "togo": "TG",
  "tonga": "TO",
  "trinidad and tobago": "TT",
  "tunisia": "TN",
  "turkey": "TR",
  "turkmenistan": "TM",
  "tuvalu": "TV",
  "uganda": "UG",
  "ukraine": "UA",
  "united arab emirates": "AE",
  "uae": "AE",
  "united kingdom": "GB",
  "uk": "GB",
  "great britain": "GB",
  "united states": "US",
  "usa": "US",
  "united states of america": "US",
  "uruguay": "UY",
  "uzbekistan": "UZ",
  "vanuatu": "VU",
  "vatican city": "VA",
  "venezuela": "VE",
  "vietnam": "VN",
  "yemen": "YE",
  "zambia": "ZM",
  "zimbabwe": "ZW"
};

export function useCountryCode() {
  // Function to get country code from country name
  const getCountryCode = (countryName: string) => {
    if (!countryName) return undefined;
    
    // Normalize the country name
    const normalizedName = countryName.toLowerCase().trim();
    
    // Direct lookup in our mapping
    if (COUNTRY_NAME_TO_CODE[normalizedName]) {
      return COUNTRY_NAME_TO_CODE[normalizedName];
    }
    
    // Try to find a partial match
    const matchingKey = Object.keys(COUNTRY_NAME_TO_CODE).find(key => 
      normalizedName.includes(key) || key.includes(normalizedName)
    );
    
    if (matchingKey) {
      return COUNTRY_NAME_TO_CODE[matchingKey];
    }
    
    return undefined;
  };

  // Function to get country flag URL from country name
  const getCountryFlagUrl = (countryName: string) => {
    const countryCode = getCountryCode(countryName);
    return countryCode 
      ? `https://purecatamphetamine.github.io/country-flag-icons/3x2/${countryCode}.svg`
      : undefined;
  };

  return {
    getCountryCode,
    getCountryFlagUrl
  };
} 