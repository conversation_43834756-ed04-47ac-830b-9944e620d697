import { CustomPeriod, Period } from "./types";

// Helper function to get previous period date range
export function getPreviousPeriodRange(
  period: Period,
  currentFrom: Date,
  currentTo: Date
): { from: Date; to: Date } {
  let prevFrom: Date;
  let prevTo: Date;

  switch (period) {
    case "today":
      // Previous period is yesterday
      prevTo = new Date(currentFrom); // Start of today
      prevTo.setDate(prevTo.getDate() - 1); // Go to yesterday
      prevTo.setHours(23, 59, 59, 999); // End of yesterday
      prevFrom = new Date(prevTo);
      prevFrom.setHours(0, 0, 0, 0); // Start of yesterday
      break;

    case "wtd":
      // Previous period is the full previous week (Mon-Sun)
      prevFrom = new Date(currentFrom); // Start of current week (Monday)
      prevFrom.setDate(prevFrom.getDate() - 7); // Go to previous Monday
      prevTo = new Date(prevFrom);
      prevTo.setDate(prevTo.getDate() + 6); // Go to previous Sunday
      prevTo.setHours(23, 59, 59, 999); // End of previous Sunday
      break;

    case "mtd":
      // Previous period is the full previous month
      prevFrom = new Date(currentFrom); // Start of current month (1st)
      prevFrom.setMonth(prevFrom.getMonth() - 1); // Go to 1st of previous month
      // End of previous month is 1ms before the start of the current month
      prevTo = new Date(currentFrom.getTime() - 1);
      break;

    case "ytd":
      // Previous period is the full previous year
      prevFrom = new Date(currentFrom); // Start of current year (Jan 1st)
      prevFrom.setFullYear(prevFrom.getFullYear() - 1); // Go to Jan 1st of previous year
      prevTo = new Date(currentFrom.getTime() - 1); // End of previous year (Dec 31st 23:59:59.999)
      break;

    case "all":
      // No previous period for "all time"
      prevFrom = new Date(0);
      prevTo = new Date(0);
      break;

    // Default handles fixed periods (last7d, last30d, last24h) and custom
    // Assumes the previous period should have the same duration as the current one
    default:
      const duration = currentTo.getTime() - currentFrom.getTime();
      // Ensure prevTo is exactly 1ms before currentFrom, respecting timezones
      prevTo = new Date(currentFrom.getTime() - 1);
      prevFrom = new Date(prevTo.getTime() - duration);
      // Adjust prevFrom to the start of its day for day-based periods
      if (!["last24h", "custom"].includes(period)) {
         prevFrom.setHours(0, 0, 0, 0);
      }
      break;
  }

  return { from: prevFrom, to: prevTo };
}

// Helper function to get date range based on period
export function getDateRangeFromPeriod(
  period: Period,
  customPeriod?: CustomPeriod,
  customPeriodParam?: CustomPeriod
): { from: Date; to: Date } {
  const now = new Date();
  let to = new Date(now);
  let from = new Date(now);

  switch (period) {
    case "today":
      from.setHours(0, 0, 0, 0);
      break;
    case "yesterday":
      from.setDate(from.getDate() - 1);
      from.setHours(0, 0, 0, 0);
      to.setDate(to.getDate() - 1);
      to.setHours(23, 59, 59, 999);
      break;
    case "last24h":
      from = new Date(now.getTime() - 24 * 60 * 60 * 1000);
      to = new Date(now);
      break;
    case "last7d":
      from = new Date(now);
      from.setDate(from.getDate() - 6);
      from.setHours(0, 0, 0, 0);
      break;
    case "last30d":
      from.setDate(from.getDate() - 29);
      from.setHours(0, 0, 0, 0);
      break;
    case "wtd":
      // Week to date (starting Monday) in local time
      // Get the current day in local time (0 = Sunday, 1 = Monday, ..., 6 = Saturday)
      const currentDay = now.getDay();

      // Calculate days to subtract to get to the most recent Monday
      // If today is Sunday (0), go back 6 days to the previous Monday
      // Otherwise go back to the most recent Monday (current day - 1)
      const daysToSubtract = currentDay === 0 ? 6 : currentDay - 1;

      // Set the from date to the beginning of the most recent Monday in local time
      from.setDate(from.getDate() - daysToSubtract);
      from.setHours(0, 0, 0, 0);

      break;
    case "mtd":
      from.setDate(1);
      from.setHours(0, 0, 0, 0);
      break;
    case "ytd":
      from.setFullYear(now.getFullYear()); // Add this line to set the year
      from.setMonth(0, 1);
      from.setHours(0, 0, 0, 0);
      break;
    case "all":
      from = new Date(0); // Set to beginning of time (January 1, 1970)
      break;
    case "custom":
      // Use the provided customPeriod parameter if available, otherwise fall back to the hook parameter
      const customPeriodToUse = customPeriodParam || customPeriod;

      if (customPeriodToUse) {
        // Create new Date objects to avoid modifying the original dates
        from = new Date(customPeriodToUse.startDate);
        to = new Date(customPeriodToUse.endDate);

        // Set the times correctly
        from.setHours(0, 0, 0, 0);
        to.setHours(23, 59, 59, 999);

        // Ensure we're working with the local timezone
        from = new Date(
          from.getFullYear(),
          from.getMonth(),
          from.getDate(),
          0,
          0,
          0,
          0
        );
        to = new Date(
          to.getFullYear(),
          to.getMonth(),
          to.getDate(),
          23,
          59,
          59,
          999
        );
      } else {
        console.error("Custom period selected but no dates provided");
        // Fallback to last 7 days if custom period is missing data
        from.setDate(from.getDate() - 7);
        from.setHours(0, 0, 0, 0);
      }
      break;
  }

  return { from, to };
}
