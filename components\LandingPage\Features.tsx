import { <PERSON><PERSON><PERSON>, <PERSON>, Zap } from "lucide-react";
import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardContent } from "../ui/card";

function Features() {
  return (
    <section
      id="features"
      className="w-full py-12 md:py-24 lg:py-32 bg-customGrayLight"
    >
      <div className="container mx-auto px-4 md:px-6 flex flex-col items-center justify-center">
        <div className="flex flex-col items-center justify-center space-y-4 text-center max-w-5xl">
          <div className="space-y-2">
            <div className="inline-block rounded-lg bg-primary px-3 py-1 text-sm text-white-foreground">
              Features
            </div>
            <h2 className="text-3xl font-bold tracking-tighter md:text-4xl/tight">
              Powerful Analytics for Landing Page Optimization
            </h2>
            <p className="max-w-[900px] text-gray-300 md:text-xl/relaxed lg:text-base/relaxed xl:text-xl/relaxed">
              Versatailor provides targeted analytics tools designed to help you understand and optimize your landing page performance.
            </p>
          </div>
        </div>
        <div className="w-full grid max-w-5xl items-center justify-center gap-6 py-12 md:grid-cols-2 lg:grid-cols-3 lg:gap-12">
          <Card className="bg-customGrayLight border-gray-700/50">
            <CardHeader>
              <LineChart className="h-10 w-10 text-white" />
              <CardTitle className="mt-4 text-white">Hour-by-Hour Analysis</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-300">
                Granular time-based filtering lets you identify traffic patterns and engagement spikes throughout the day, helping you optimize content timing.
              </p>
            </CardContent>
          </Card>
          <Card className="bg-customGrayLight border-gray-700/50">
            <CardHeader>
              <Users className="h-10 w-10 text-white" />
              <CardTitle className="mt-4 text-white">Conversion Analytics</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-300">
                Filter out authenticated visits to get a clear view of your landing page&apos;s true conversion rate and understand how new visitors interact with your site.
              </p>
            </CardContent>
          </Card>
          <Card className="bg-customGrayLight border-gray-700/50">
            <CardHeader>
              <Zap className="h-10 w-10 text-white" />
              <CardTitle className="mt-4 text-white">Journey Tracking</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-300">
                Track visitor paths through your landing page to identify engagement patterns, drop-off points, and optimize your conversion funnel.
              </p>
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  );
}

export default Features;
