import mongoose from "mongoose";

const eventSchema = new mongoose.Schema({
  id: {
    type: String,
    required: true,
    unique: true,
    default: () => crypto.randomUUID(),
  },
  type: { type: String, required: true },
  websiteId: { type: String, required: true },
  domain: { type: String, required: true },
  userId: { 
    type: String, 
    required: false,
    index: true  // Add an index for better query performance
  },
  href: { type: String, required: true },
  referrer: { type: String },
  timestamp: { type: Date, required: true },
  viewport: {
    width: { type: Number },
    height: { type: Number },
  },
  visitorId: { type: String, required: true },
  sessionId: { type: String, required: true },
  location: {
    city: { type: String },
    country: { type: String },
    region: { type: String },
  },
  extraData: { type: mongoose.Schema.Types.Mixed },
  userAgent: {
    type: String,
    required: false,
    default: "unknown",
  },
  isBot: {
    type: Boolean,
    required: true,
    default: function (
      this: mongoose.Document & {
        location?: { country?: string; city?: string };
        viewport?: { width?: number; height?: number };
        userAgent?: string;
      }
    ) {
      if (!this.location?.country && !this.location?.city) {
        const suspiciousIndicators = [
          !this.viewport?.width,
          !this.viewport?.height,
          !this.userAgent || this.userAgent === "unknown",
        ];

        return suspiciousIndicators.filter(Boolean).length >= 2;
      }
      return false;
    },
  },
  botInfo: {
    detected: { type: Boolean, default: false },
    reason: { type: String },
    pattern: { type: String },
  },
  clientInfo: {
    browser: { type: String },
    os: { type: String },
    device: { type: String },
  },
  ip: {
    type: String,
    required: false,
    select: false,
  },
  headers: {
    type: Map,
    of: String,
    required: false,
    select: false,
  },
  isAuthVisitor: {
    type: Boolean,
    default: false,
  },
  // Revenue Attribution Data (for payment events)
  revenueData: {
    type: {
      amount: { type: Number }, // Amount in cents
      currency: { type: String, default: 'usd' },
      stripeChargeId: { type: String },
      stripeCustomerId: { type: String },
      stripeSessionId: { type: String },
      customerEmail: { type: String },
      isNewCustomer: { type: Boolean },
      isRecurring: { type: Boolean, default: false },
      subscriptionId: { type: String },
      productNames: [{ type: String }],
      paymentType: { type: String }, // 'one_time', 'subscription', 'renewal'
    },
    required: false,
  },
});

eventSchema.index({ websiteId: 1, timestamp: -1 });
eventSchema.index({ visitorId: 1, timestamp: -1 });
eventSchema.index({ domain: 1, timestamp: -1 });
eventSchema.index({ isBot: 1 });
eventSchema.index({
  "location.country": 1,
  "location.city": 1,
  isBot: 1,
  timestamp: -1,
});

// Add this index for queries that might filter by userId
eventSchema.index({ userId: 1, timestamp: -1 });

// Revenue-specific indexes for performance
eventSchema.index({ 
  websiteId: 1, 
  type: 1, 
  timestamp: -1 
}); // For filtering payment events by website
eventSchema.index({ 
  "revenueData.stripeChargeId": 1 
}); // For webhook idempotency
eventSchema.index({ 
  "revenueData.stripeCustomerId": 1, 
  timestamp: -1 
}); // For customer analytics

export interface Event extends mongoose.Document {
  id: string;
  type: string;
  websiteId: string;
  domain: string;
  href: string;
  referrer: string | null;
  timestamp: Date;
  viewport: {
    width: number | null;
    height: number | null;
  } | null;
  location: {
    city: string | null;
    country: string | null;
    region: string | null;
  } | null;
  visitorId: string;
  sessionId: string;
  extraData?: any;
  userAgent?: string;
  isBot: boolean;
  botInfo?: {
    detected: boolean;
    reason?: string;
    pattern?: string;
  };
  clientInfo?: {
    browser?: string;
    os?: string;
    device?: string;
  };
  ip?: string;
  headers?: Map<string, string>;
  isAuthVisitor: boolean;
  revenueData?: {
    amount?: number;
    currency?: string;
    stripeChargeId?: string;
    stripeCustomerId?: string;
    stripeSessionId?: string;
    customerEmail?: string;
    isNewCustomer?: boolean;
    isRecurring?: boolean;
    subscriptionId?: string;
    productNames?: string[];
    paymentType?: string;
  };
}

// Create a pre-save middleware to ensure sensitive data is handled properly
eventSchema.pre("save", function (next) {
  // Ensure IP addresses are anonymized if needed
  if (this.ip) {
    // Example: Remove last octet for IPv4 or last 64 bits for IPv6
    this.ip = this.ip.replace(/\.\d+$/, ".0");
  }

  // Clean up headers to remove sensitive information
  if (this.headers) {
    const sensitiveHeaders = ["cookie", "authorization", "proxy-authorization"];
    sensitiveHeaders.forEach((header) => {
      this.headers?.delete(header);
    });
  }

  next();
});

const Event =
  mongoose.models.Event || mongoose.model<Event>("Event", eventSchema);
export default Event;
