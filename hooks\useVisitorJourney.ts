import { useState } from 'react';

export interface JourneyEvent {
  type: string;
  timestamp: string;
  visitorId: string;
  href?: string;
  referrer?: string;
  extraData?: {
    eventName?: string;
    [key: string]: any;
  };
  location?: {
    city: string;
    country: string;
    region?: string;
  };
}

export function useVisitorJourney(websiteId: string) {
  const [selectedJourney, setSelectedJourney] = useState<{
    eventName: string;
    visitorId: string;
    timestamp: string;
  } | null>(null);
  const [journeyEvents, setJourneyEvents] = useState<JourneyEvent[]>([]);
  const [isLoadingJourney, setIsLoadingJourney] = useState(false);

  // Function to fetch visitor journey
  const fetchVisitorJourney = async (
    visitorId: string,
    eventTimestamp: string
  ) => {
    try {
      setIsLoadingJourney(true);
      const response = await fetch(
        `/api/events?websiteId=${websiteId}&visitorId=${visitorId}&endTime=${eventTimestamp}`
      );
      if (!response.ok) throw new Error("Failed to fetch journey");
      const events = await response.json();
      setJourneyEvents(events);
    } catch (error) {
      console.error("Error fetching journey:", error);
      setJourneyEvents([]);
    } finally {
      setIsLoadingJourney(false);
    }
  };

  // Function to handle journey click
  const handleJourneyClick = (
    eventName: string,
    visitorId: string,
    timestamp: string
  ) => {
    setSelectedJourney({ eventName, visitorId, timestamp });
    fetchVisitorJourney(visitorId, timestamp);
  };

  return {
    selectedJourney,
    setSelectedJourney,
    journeyEvents,
    isLoadingJourney,
    handleJourneyClick
  };
} 