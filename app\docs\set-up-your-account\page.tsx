import { Metadata } from "next";
import { getMetadata } from "@/lib/docs-metadata";
import { Website } from "@/models/website";
import Link from "next/link";
import CodeBlock from "@/components/CodeBlock";

export const metadata: Metadata = getMetadata("/docs/set-up-your-account");

export default function SetupAccountPage() {
  function getScriptCode() {
    return `<script
  defer
  src="https://versatailor.com/script.js"
  data-website-id="YOUR_WEBSITE_ID"
  data-domain="YOUR_WEBSITE_DOMAIN"></script>`;
  }

  return (
    <div className="max-w-3xl mx-auto py-8 px-4">
      <h1 className="text-3xl font-bold mb-8">Set up your account</h1>

      <div className="space-y-6">
        <p className="text-lg">
          Versatailor is built to be a straightforward tool packed with
          actionable data you can use to grow your online business.
        </p>

        <section className="space-y-4">
          <h2 className="text-2xl font-semibold">1) Sign up for free</h2>
          <p>
            Go to{" "}
            <Link
              href={"/"}
              target="_blank"
              className="text-blue-400 hover:underline"
            >
              https://versatailor.com
            </Link>{" "}
            and click on &quot;Get Started&quot; to create your account with your
            preferred authentication method.
          </p>

          <h2 className="text-2xl font-semibold mt-8">2) Add your first website</h2>
          <p>
            If you aren&apos;t already, navigate to the{" "}
            <Link
              href={"/dashboard"}
              target="_blank"
              className="text-blue-400 hover:underline"
            >
              dashboard
            </Link>
            . Click on the &quot;+ Add Website&quot; button in the top right and
            add your domain name.
          </p>
          <p>
            Your website name will be generated automatically, feel free to change
            it if you would like.
          </p>

          <h2 className="text-2xl font-semibold mt-8">3) Install Versatailor script</h2>
          <CodeBlock code={getScriptCode()} language="typescript" />
          <div className="bg-blue-500/10 border border-blue-500/20 p-4 rounded-lg mt-4">
            <p className="text-blue-300/90">
              Make sure to replace YOUR_WEBSITE_DOMAIN and YOUR_WEBSITE_ID with the
              correct values that were assigned to your website.
            </p>
          </div>
        </section>
      </div>
    </div>
  );
}
