import { NextRequest, NextResponse } from "next/server";
import { auth } from "@clerk/nextjs/server";

/**
 * Validates if a website has the tracking script properly installed
 * 
 * POST /api/validate/script
 * Body: { url: string, websiteId: string }
 */
export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Parse request body
    const body = await req.json();
    const { url, websiteId } = body;

    // Validate required parameters
    if (!url) {
      return NextResponse.json(
        { error: "URL is required" },
        { status: 400 }
      );
    }

    if (!websiteId) {
      return NextResponse.json(
        { error: "Website ID is required" },
        { status: 400 }
      );
    }

    // Ensure the URL has a protocol
    let formattedUrl = url;
    if (!url.startsWith("http://") && !url.startsWith("https://")) {
      formattedUrl = `https://${url}`;
    }

    try {
      // Fetch the website's HTML
      const response = await fetch(formattedUrl, {
        headers: {
          "User-Agent": "VersaTailor Script Validator/1.0",
        },
      });

      if (!response.ok) {
        return NextResponse.json(
          { 
            error: "Failed to access website", 
            status: response.status,
            message: `Status: ${response.status} ${response.statusText}` 
          },
          { status: 400 }
        );
      }

      // Get the HTML content
      const html = await response.text();

      // Check if our script is in the HTML
      // Look for any script tag with the data-website-id attribute matching our websiteId
      const scriptPattern = new RegExp(
        `<script[^>]*data-website-id=["']${websiteId}["'][^>]*>`, 
        "i"
      );
      
      const isInstalled = scriptPattern.test(html);
      
      // Check if it's in the head section
      const headPattern = new RegExp(
        `<head[^>]*>(?:(?!</head>).)*data-website-id=["']${websiteId}["'].*?</head>`, 
        "is"
      );
      const isInHead = headPattern.test(html);

      return NextResponse.json({
        url: formattedUrl,
        isInstalled,
        isInHead,
        isValid: isInstalled && isInHead
      });
    } catch (error: any) {
      console.error("Validation error:", error.message);
      return NextResponse.json(
        { 
          error: "Failed to validate script", 
          message: error.message 
        },
        { status: 500 }
      );
    }
  } catch (error: any) {
    console.error("Server error:", error);
    return NextResponse.json(
      { error: "Internal server error", message: error.message },
      { status: 500 }
    );
  }
} 