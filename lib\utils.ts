import { type ClassValue, clsx } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export const sliderValues = [
  {
    value: 0,
    label: "10k",
    price: "9",
    testPriceId: "price_1QxSnGKHseo214m6T8QOwLqW",
  },
  {
    value: 12.5,
    label: "100k",
    price: "19",
    testPriceId: "price_1QxSoOKHseo214m6Pn2ATdGd",
  },
  { value: 25, label: "200k", price: "29" },
  { value: 37.5, label: "500k", price: "49" },
  { value: 50, label: "1M", price: "69" },
  { value: 62.5, label: "2M", price: "89" },
  { value: 75, label: "5M", price: "129" },
  { value: 87.5, label: "10M", price: "169" },
  { value: 100, label: "10M+", price: "199" },
];

// Helper function to get friendly names for common referrers
export const getFriendlyReferrerName = (url: string | null): string | null => {
  if (!url) return null;

  // Remove protocol and www. prefix for matching
  const domain = url.replace(/^(https?:\/\/)?(www\.)?/, "").split("/")[0].toLowerCase();

  const referrerMap: Record<string, string> = {
    "t.co": "X", // Added t.co mapping
    "x.com": "X", // Changed Twitter to X
    "facebook.com": "Facebook",
    "instagram.com": "Instagram",
    "linkedin.com": "LinkedIn",
    "reddit.com": "Reddit",
    "youtube.com": "YouTube",
    "tiktok.com": "TikTok",
    "pinterest.com": "Pinterest",
    "google.com": "Google",
    "bing.com": "Bing",
    "yahoo.com": "Yahoo",
    "duckduckgo.com": "DuckDuckGo",
    "github.com": "GitHub",
    "medium.com": "Medium",
    "quora.com": "Quora",
    "baidu.com": "Baidu",
    "yandex.com": "Yandex"
  };

  return referrerMap[domain] || null;
};
