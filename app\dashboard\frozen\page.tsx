"use client";
import React from "react";
import Link from "next/link";
import { LockClosedIcon } from "@heroicons/react/24/solid";

export default function FrozenDashboard() {
  return (
    <div className="flex min-h-screen flex-col items-center bg-[#1C1C1C] text-gray-100">
      <div className="flex justify-center"> {/* Removed flex-1 */}
        <div className="p-8 bg-[#2A2A2A] rounded-lg shadow-md text-center max-w-md w-full mx-4 mt-10"> {/* Added mt-10 here for spacing */}
          <div className="flex justify-center mb-6">
            <LockClosedIcon className="w-12 h-12 text-red-500" />
          </div>
          <h2 className="text-2xl font-bold text-red-500 mb-4">
            Dashboard Locked
          </h2>
          <p className="text-gray-300 mb-6">Your access has expired</p>
          <div className="border-t border-gray-700 pt-6">
            <p className="text-sm text-gray-400 mb-4">
              Please renew your subscription to continue accessing the dashboard
            </p>
            <Link 
              href="/billing" 
              className="inline-flex items-center px-6 py-3 bg-red-500 hover:bg-red-600 transition-colors rounded-lg font-medium text-white"
            >
              Renew Subscription
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
