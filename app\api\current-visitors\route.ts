import { NextRequest, NextResponse } from "next/server";
import connectToDatabase from "@/util/db";
import EventModel from "@/models/event";
import mongoose from "mongoose";

export async function GET(req: NextRequest) {
  try {
    await connectToDatabase();

    const { searchParams } = new URL(req.url);
    const websiteId = searchParams.get("websiteId");

    if (!websiteId) {
      return NextResponse.json(
        { message: "Website ID is required" },
        { status: 400 }
      );
    }

    // Removed ObjectId validation as websiteId seems to be a UUID string

    const fiveMinutesAgo = new Date();
    fiveMinutesAgo.setMinutes(fiveMinutesAgo.getMinutes() - 5);

    // Aggregate pipeline to count distinct visitorIds in the last 5 minutes
    const result = await EventModel.aggregate([
      {
        $match: {
          websiteId: websiteId, // Use the websiteId string directly
          timestamp: { $gte: fiveMinutesAgo },
          // Optionally filter by type if needed, e.g., 'pageview' or specific interaction
          // type: 'pageview' 
        },
      },
      {
        $group: {
          _id: "$visitorId", // Group by visitorId to get unique visitors
        },
      },
      {
        $count: "currentVisitors", // Count the number of unique groups (visitors)
      },
    ]);

    // The result is an array, potentially empty or with one object like [{ currentVisitors: 10 }]
    const currentVisitorsCount = result.length > 0 ? result[0].currentVisitors : 0;

    return NextResponse.json({ count: currentVisitorsCount }, { status: 200 });

  } catch (error) {
    console.error("Error fetching current visitors:", error);
    return NextResponse.json(
      { message: "Internal Server Error" },
      { status: 500 }
    );
  }
}
