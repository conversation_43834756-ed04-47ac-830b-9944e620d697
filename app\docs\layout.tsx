import { Metadata } from 'next';
import { getMetadata } from '@/lib/docs-metadata';
import DocsLayoutClient from './DocsLayoutClient';

export async function generateMetadata(): Promise<Metadata> {
  const metadata = getMetadata('/docs');
  return {
    title: metadata.title,
    description: metadata.description,
    keywords: metadata.keywords?.join(', '),
    openGraph: {
      title: metadata.title,
      description: metadata.description,
      siteName: metadata.siteName,
      url: `${metadata.baseUrl}/docs`,
      type: 'website',
    },
    twitter: {
      card: 'summary_large_image',
      title: metadata.title,
      description: metadata.description,
      creator: metadata.twitterHandle,
    },
  };
}

export default function DocsLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return <DocsLayoutClient>{children}</DocsLayoutClient>;
}
