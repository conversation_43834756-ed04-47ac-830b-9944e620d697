import Image from "next/image";
import Link from "next/link";
import React from "react";

function FilterSettingsDocumentation() {
  return (
    <div className="max-w-3xl mx-auto py-8 px-4">
      <h1 className="text-3xl font-bold mb-8">Exclue Your Own Visits</h1>

      <div className="space-y-6">
        <section className="space-y-4">
          <ol className="list-decimal list-inside space-y-3">
            <li>
              Go to your{" "}
              <Link
                href="/dashboard"
                target="_blank"
                className="text-blue-400 hover:underline"
              >
                dashboard
              </Link>{" "}
              and select the website you wish exclude your own visits from.
            </li>
            <li>
              Click on the dropdown menu for your website in the top left of
              your dashboard
            </li>
            <Image
              src={"/docs_assets/site_settings.png"}
              width={1200}
              height={600}
              alt="Site Settings screenshot"
            />
            <li>Click on &quot;Site Settings&quot;</li>
            <li>
              Under &quot;Filter&quot; tab, find the &quot;Device Exclusion&quot; section.
            </li>
            <Image
            src={"/docs_assets/exclude_own.png"}
            width={1200}
            height={600}
            alt="Site Settings screenshot"
          />
            <li>Click the &quot;Exclude this device&quot; button</li>
            <li>
              Your device&apos;s information is stored locally. Future visits
              from this device won&apos;t be counted.
            </li>
            <li>
              You can undo this at any time by clicking the &quot;Undo&quot;
              button.
            </li>
          </ol>
        </section>

        <div className="max-w-3xl mx-auto py-8 px-4">

          <section>
        

            <div className="bg-red-500/10 border border-red-500/20 p-4 rounded-lg">
              <h3 className="text-red-400 font-semibold mb-2">Note:</h3>
              <ul className="list-disc ml-6 space-y-2 text-red-300/90">
                <li>Device exclusion is browser-specific</li>
                <li>Clearing browser data will reset device exclusion</li>
                <li>Each website has its own device exclusion list</li>
              </ul>
            </div>
          </section>
        </div>
      </div>
    </div>
  );
}

export default FilterSettingsDocumentation;
