// Production-safe logging utility
export const logger = {
  debug: (...args: any[]) => {
    // Always log in development
    if (process.env.NODE_ENV === 'development') {
      console.log(...args);
      return;
    }
    
    // In production, only log if debug is enabled
    if (typeof window !== 'undefined') {
      // Check for a debug flag in localStorage
      const debugEnabled = localStorage.getItem('debug_analytics') === 'true';
      if (debugEnabled) {
        console.log('[PROD DEBUG]', ...args);
      }
    }
  },
  
  error: (...args: any[]) => {
    // Always log errors in both dev and prod
    console.error(...args);
  }
}; 