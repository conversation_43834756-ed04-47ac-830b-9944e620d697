import { ChartDataPoint, CustomPeriod, Event, Period } from "./types";
import { logger } from "./utils/logger";

// Helper function to convert UTC to local time consistently
function toLocalTime(date: Date): Date {
  // Create a new date that correctly represents the local time
  const localDate = new Date(date.toLocaleString());
  return localDate;
}

// Helper function to process events into chart data
export function processEventsIntoChartData(
  events: Event[],
  period: Period,
  customPeriod: CustomPeriod
): ChartDataPoint[] {
  // Add debug logs for timezone investigation
  logger.debug('Debug - Timezone Investigation:', {
    timezoneOffset: new Date().getTimezoneOffset(),
    browserTimezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
    environment: process.env.NODE_ENV
  });
  
  const template = generateEmptyChartData(period, events, customPeriod);
  if (!events.length) return template;

  const now = toLocalTime(new Date());
  logger.debug('Debug - Current time:', {
    localTime: now.toLocaleString(),
    utcTime: now.toUTCString(),
    isoTime: now.toISOString(),
    convertedTime: now.toString()
  });

  const eventCounts = new Map<string, Set<string>>();

  // Initialize the Map with Sets for each time key
  template.forEach((point) => {
    eventCounts.set(point.time, new Set());
  });

  // Sort events by timestamp to ensure correct processing
  const sortedEvents = [...events].sort(
    (a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
  );

  // Helper function to format hour consistently
  const formatHour = (hour: number): string => {
    if (hour === 0) return "12am";
    if (hour < 12) return `${hour}am`;
    if (hour === 12) return "12pm";
    return `${hour - 12}pm`;
  };

  // For last7d period, calculate the start date
  const last7dStart = 
    period === "last7d"
      ? (() => {
          const start = new Date(now);
          start.setDate(start.getDate() - 6);
          start.setHours(0, 0, 0, 0);
          return start;
        })()
      : null;
  // For last24h period, calculate the start time
  const last24hStart =
    period === "last24h" ? new Date(now.getTime() - 24 * 60 * 60 * 1000) : null;
  // For last30d period, calculate the start date
  const last30dStart =
    period === "last30d"
      ? new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
      : null;

  // Create a map of formatted dates to template indices for last30d
  const dateToIndexMap = new Map<string, number>();
  if (period === "last30d") {
    template.forEach((point, index) => {
      dateToIndexMap.set(point.time, index);
    });
  }

  sortedEvents.forEach((event) => {
    const date = toLocalTime(new Date(event.timestamp));
    logger.debug('Debug - Event timestamp processing:', {
      originalTimestamp: event.timestamp,
      parsedLocalTime: date.toLocaleString(),
      parsedUTCTime: date.toUTCString(),
      parsedISOTime: date.toISOString(),
      convertedTime: date.toString(),
      period
    });
    
    let timeKey = "";

    switch (period) {
      case "custom":
        if (customPeriod) {
          const start = new Date(customPeriod.startDate);
          start.setHours(0, 0, 0, 0);
          const end = new Date(customPeriod.endDate);
          end.setHours(23, 59, 59, 999);

          const diffTime = end.getTime() - start.getTime();
          const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

          if (diffDays < 3) {
            // For 1-2 days, show hourly data
            timeKey = formatHour(date.getHours());
          } else {
            // For 3 or more days, show daily data
            timeKey = date.toLocaleDateString([], {
              month: "short",
              day: "numeric",
            });
          }
        }
        break;
      case "today":
        // Only process events from today
        const todayStart = new Date();
        todayStart.setHours(0, 0, 0, 0);
        const todayEnd = new Date();
        todayEnd.setHours(23, 59, 59, 999);

        // Convert dates to UTC for consistent comparison
        const eventDate = new Date(date);
        const eventDateStart = new Date(eventDate);
        eventDateStart.setHours(0, 0, 0, 0);
        const eventDateEnd = new Date(eventDate);
        eventDateEnd.setHours(23, 59, 59, 999);

        // Compare dates using local time boundaries
        if (eventDateStart.toDateString() === todayStart.toDateString()) {
          timeKey = formatHour(eventDate.getHours());
        }
        break;
      case "yesterday":
        // Only process events from yesterday
        const yesterdayStart = new Date();
        yesterdayStart.setDate(yesterdayStart.getDate() - 1);
        yesterdayStart.setHours(0, 0, 0, 0);
        const yesterdayEnd = new Date(yesterdayStart);
        yesterdayEnd.setHours(23, 59, 59, 999);

        if (date >= yesterdayStart && date <= yesterdayEnd) {
          timeKey = formatHour(date.getHours());
        }
        break;
      case "last24h":
        // For last24h, we need to match the hour format in the template
        // but ensure we're only counting events in the last 24 hours
        if (last24hStart && date >= last24hStart && date <= now) {
          // Find the corresponding hour slot in our template
          // We need to map each event to one of our 24 hourly slots
          const hourIndex = Math.floor(
            (date.getTime() - last24hStart.getTime()) / (60 * 60 * 1000)
          );
          // Get the corresponding hour from our template
          if (hourIndex >= 0 && hourIndex < template.length) {
            timeKey = template[hourIndex].time;
          }
        }
        break;
      case "last7d":
        // For last7d, we want all events from the past 7 days including today
        if (last7dStart && date >= last7dStart && date <= now) {
          // Format the date to match the chart data point format
          timeKey = date.toLocaleDateString([], { weekday: "short" });

          // Find the matching template time key by comparing day of week
          // This ensures events are mapped to the correct day slot
          const dayIndex = Math.floor(
            (date.getTime() - last7dStart.getTime()) / (24 * 60 * 60 * 1000)
          );
          if (dayIndex >= 0 && dayIndex < template.length) {
            timeKey = template[dayIndex].time;
          }
        }
        break;
      case "last30d":
        // For last30d, ensure we're only counting events in the last 30 days
        if (last30dStart && date >= last30dStart && date <= now) {
          // Use a consistent date formatting function
          const formattedDate = date.toLocaleDateString([], {
            month: "short",
            day: "numeric",
          });

          // Try to find the matching template entry by formatted date string
          if (dateToIndexMap.has(formattedDate)) {
            timeKey = formattedDate;
          } else {
            // If no direct match, find the closest date in the template
            // This handles edge cases with timezone differences
            const daysSinceStart = Math.floor(
              (date.getTime() - last30dStart.getTime()) / (24 * 60 * 60 * 1000)
            );
            if (daysSinceStart >= 0 && daysSinceStart < template.length) {
              timeKey = template[daysSinceStart].time;
            }
          }
        }
        break;
      case "wtd":
        timeKey = date.toLocaleDateString([], { weekday: "short" });
        break;
      case "mtd":
        timeKey = date.toLocaleDateString([], {
          month: "short",
          day: "numeric",
        });
        break;
      case "ytd":
        timeKey = date.toLocaleDateString([], { month: "short" });
        break;
      case "all":
        if (events.length > 0) {
          const sortedEvents = [...events].sort(
            (a, b) =>
              new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
          );

          const firstDate = new Date(sortedEvents[0].timestamp);
          const lastDate = new Date(
            sortedEvents[sortedEvents.length - 1].timestamp
          );
          const totalDays = Math.ceil(
            (lastDate.getTime() - firstDate.getTime()) / (1000 * 60 * 60 * 24)
          );

          if (totalDays <= 90) {
            // Daily format for up to 90 days
            const eventDate = new Date(date);
            eventDate.setHours(0, 0, 0, 0);
            const isoDate = eventDate.toISOString().split("T")[0];

            // Find matching template item using the ISO date key
            const matchingItem = template.find((t) => t._dateKey === isoDate);
            if (matchingItem) {
              timeKey = matchingItem.time;
            }
          } else {
            // Monthly format for > 90 days
            const eventDate = new Date(date);
            const isoMonth = `${eventDate.getFullYear()}-${String(
              eventDate.getMonth() + 1
            ).padStart(2, "0")}`;

            // Find matching template item using the ISO month key
            const matchingItem = template.find((t) => t._dateKey === isoMonth);
            if (matchingItem) {
              timeKey = matchingItem.time;
            }
          }
        }
        break;
    }

    // Only process events that have a valid time key
    if (timeKey) {
      // Find the matching template time key
      const templateKey = template.find((t) => t.time === timeKey)?.time;
      if (templateKey) {
        const set = eventCounts.get(templateKey);
        if (set) {
          set.add(event.visitorId);
        }
      }
    }
  });

  // Convert the Map to the final chart data format
  const result = template.map((point) => {
    const uniqueVisitors = eventCounts.get(point.time)?.size || 0;

    // For last24h, ensure we're showing the correct pulse indicator
    if (
      period === "last24h" &&
      point.time === template[template.length - 1].time
    ) {
      return {
        time: point.time,
        visitors: uniqueVisitors,
        pulse: 0, // Show pulse on the most recent hour
      };
    }

    return {
      time: point.time,
      visitors: point.visitors === null ? null : uniqueVisitors,
      pulse: point.pulse === null ? null : uniqueVisitors,
    };
  });

  // For custom periods, ensure we're showing the correct range of data
  let finalResult = result;
  if (period === "custom" && customPeriod) {
    const start = new Date(customPeriod.startDate);
    const end = new Date(customPeriod.endDate);
    const diffTime = end.getTime() - start.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    // For single day view, ensure we show all 24 hours
    if (diffDays === 1) {
      finalResult = result.slice(0, 24);
    }
  }

  // Verify that the total unique visitors in the chart matches the expected count
  if (period === "last30d") {
    const totalInChart = finalResult.reduce(
      (sum, point) => sum + (point.visitors || 0),
      0
    );

    // If there's a significant discrepancy, recalculate using a different approach
    if (totalInChart === 0 || events.length / totalInChart > 5) {
      // Create a map to track unique visitors per day
      const visitorsByDay = new Map<string, Set<string>>();

      // Initialize the map with empty sets for each day
      finalResult.forEach((point) => {
        visitorsByDay.set(point.time, new Set());
      });

      // Process each event to count unique visitors per day
      events.forEach((event) => {
        if (
          last30dStart &&
          new Date(event.timestamp) >= last30dStart &&
          new Date(event.timestamp) <= now
        ) {
          const eventDate = new Date(event.timestamp);
          const formattedDate = eventDate.toLocaleDateString([], {
            month: "short",
            day: "numeric",
          });

          // Find the matching day in our map
          if (visitorsByDay.has(formattedDate)) {
            // Add this visitor ID to the set for this day
            visitorsByDay.get(formattedDate)?.add(event.visitorId);
          }
        }
      });

      // Update the chart data with deduplicated counts
      finalResult.forEach((point, index) => {
        const visitors = visitorsByDay.get(point.time)?.size || 0;
        finalResult[index].visitors = visitors;
      });
    }
  }

  return finalResult;
}

// Helper function to generate empty chart data
function generateEmptyChartData(
  period: Period,
  events: Event[],
  customPeriod?: CustomPeriod
): ChartDataPoint[] {
  const now = toLocalTime(new Date());
  let dataPoints: ChartDataPoint[] = [];

  switch (period) {
    case "today":
      // 24 hour format with current hour handling
      const currentHour = now.getHours();
      dataPoints = Array.from({ length: 24 }, (_, i) => {
        const hour =
          i === 0
            ? "12am"
            : i < 12
            ? `${i}am`
            : i === 12
            ? "12pm"
            : `${i - 12}pm`;
        return {
          time: hour,
          visitors: i <= currentHour ? 0 : null,
          pulse: i === currentHour ? 0 : null,
        };
      });
      break;

    case "yesterday":
      dataPoints = Array.from({ length: 24 }, (_, i) => {
        const hour =
          i === 0
            ? "12am"
            : i < 12
            ? `${i}am`
            : i === 12
            ? "12pm"
            : `${i - 12}pm`;
        return {
          time: hour,
          visitors: 0,
          pulse: null,
        };
      });
      break;

    case "last24h":
      // Create 24 hourly data points for the last 24 hours
      dataPoints = Array.from({ length: 24 }, (_, i) => {
        // Calculate the hour this data point represents (from 24 hours ago to now)
        const hourOffset = 23 - i; // 23 for the oldest hour, 0 for the current hour
        const hourDate = new Date(now.getTime() - hourOffset * 60 * 60 * 1000);

        const formattedHour =
          hourDate.getHours() === 0
            ? "12am"
            : hourDate.getHours() < 12
            ? `${hourDate.getHours()}am`
            : hourDate.getHours() === 12
            ? "12pm"
            : `${hourDate.getHours() - 12}pm`;

        return {
          time: formattedHour,
          visitors: 0,
          pulse: i === 23 ? 0 : null, // Pulse on the most recent hour
        };
      });
      break;

    case "last7d":
      dataPoints = Array.from({ length: 7 }, (_, i) => {
        const date = new Date(now);
        // Set to beginning of day for consistent day boundaries
        date.setDate(date.getDate() - (6 - i));
        date.setHours(0, 0, 0, 0);

        // Check if this day is today
        const isToday =
          date.getDate() === now.getDate() &&
          date.getMonth() === now.getMonth() &&
          date.getFullYear() === now.getFullYear();

        return {
          time: date.toLocaleDateString([], { weekday: "short" }),
          visitors: 0, // Always show data point for all days including today
          pulse: isToday ? 0 : null, // Only show pulse on today
        };
      });
      break;

    case "last30d":
      dataPoints = Array.from({ length: 30 }, (_, i) => {
        const date = new Date(now);
        date.setDate(date.getDate() - (29 - i));
        const isInFuture = date > now;

        // Use a consistent date formatting function
        const formattedDate = date.toLocaleDateString([], {
          month: "short",
          day: "numeric",
        });

        return {
          time: formattedDate,
          visitors: isInFuture ? null : 0,
          pulse: i === 29 && !isInFuture ? 0 : null,
        };
      });
      break;

    case "wtd":
      const startOfWeek = new Date(now);
      const dayOfWeek = startOfWeek.getDay();
      // If today is Sunday (0), go back 6 days to previous Monday
      // Otherwise go back to the most recent Monday
      const daysToSubtract = dayOfWeek === 0 ? 6 : dayOfWeek - 1;
      startOfWeek.setDate(startOfWeek.getDate() - daysToSubtract);

      // Calculate days from Monday to today
      const days = dayOfWeek === 0 ? 7 : dayOfWeek;

      dataPoints = Array.from({ length: days }, (_, i) => {
        const date = new Date(startOfWeek);
        date.setDate(date.getDate() + i);
        const isInFuture = date > now;
        return {
          time: date.toLocaleDateString([], { weekday: "short" }),
          visitors: isInFuture ? null : 0,
          pulse: isInFuture ? null : 0,
        };
      });
      break;

    case "mtd":
      const daysInMonth = now.getDate();
      dataPoints = Array.from({ length: daysInMonth }, (_, i) => {
        const date = new Date(now.getFullYear(), now.getMonth(), i + 1);
        const isInFuture = date > now;
        return {
          time: date.toLocaleDateString([], {
            month: "short",
            day: "numeric",
          }),
          visitors: isInFuture ? null : 0,
          pulse: i === daysInMonth - 1 && !isInFuture ? 0 : null,
        };
      });
      break;

    case "ytd":
      const monthsInYear = now.getMonth() + 1;
      dataPoints = Array.from({ length: monthsInYear }, (_, i) => {
        const date = new Date(now.getFullYear(), i, 1);
        return {
          time: date.toLocaleDateString([], { month: "short" }),
          visitors: 0,
          pulse: i === monthsInYear - 1 ? 0 : null,
        };
      });

      // Group events by month and count unique visitors
      const visitorsByMonth = new Map<string, Set<string>>();
      const sortedEvents = [...events].sort(
        (a, b) =>
          new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
      );
      sortedEvents.forEach((event: Event) => {
        const eventDate = new Date(event.timestamp);
        const monthKey = eventDate.toLocaleDateString([], {
          month: "short",
        });

        if (!visitorsByMonth.has(monthKey)) {
          visitorsByMonth.set(monthKey, new Set());
        }
        visitorsByMonth.get(monthKey)?.add(event.visitorId);
      });

      // Update dataPoints with visitor counts
      dataPoints.forEach((point) => {
        if (visitorsByMonth.has(point.time)) {
          point.visitors = visitorsByMonth.get(point.time)!.size;
        }
      });
      break;

    case "all":
      if (events.length > 0) {
        const sortedEvents = [...events].sort(
          (a, b) =>
            new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
        );

        const firstDate = new Date(sortedEvents[0].timestamp);
        const lastDate = new Date(
          sortedEvents[sortedEvents.length - 1].timestamp
        );

        // Calculate total days between first and last event
        const totalDays = Math.ceil(
          (lastDate.getTime() - firstDate.getTime()) / (1000 * 60 * 60 * 24)
        );

        if (totalDays <= 90) {
          // Show daily data for ranges up to 90 days
          dataPoints = Array.from({ length: totalDays + 1 }, (_, i) => {
            const date = new Date(firstDate);
            date.setDate(firstDate.getDate() + i);
            date.setHours(0, 0, 0, 0);
            const isLastDay = i === totalDays;

            // Format for display
            const displayFormat = date.toLocaleDateString([], {
              month: "short",
              day: "numeric",
            });

            // Use ISO string as internal key for consistent matching
            const isoDate = date.toISOString().split("T")[0];

            return {
              time: displayFormat,
              visitors: 0,
              pulse: isLastDay ? 0 : null,
              // Add a unique ISO-formatted date string for consistent matching
              _dateKey: isoDate,
            };
          });
        } else {
          // Monthly format for > 90 days
          const months =
            (lastDate.getFullYear() - firstDate.getFullYear()) * 12 +
            (lastDate.getMonth() - firstDate.getMonth()) +
            1;

          // Create a data point for each month between first and last date
          dataPoints = Array.from({ length: months }, (_, i) => {
            const date = new Date(firstDate);
            date.setMonth(firstDate.getMonth() + i);
            date.setDate(1); // First day of month
            date.setHours(0, 0, 0, 0);
            const isLastMonth = i === months - 1;

            // Format for display
            const displayFormat = date.toLocaleDateString([], {
              month: "short",
              year: "numeric",
            });

            // Use ISO month format for consistent matching
            const isoMonth = `${date.getFullYear()}-${String(
              date.getMonth() + 1
            ).padStart(2, "0")}`;

            return {
              time: displayFormat,
              visitors: 0,
              pulse: isLastMonth ? 0 : null,
              // Add a unique ISO-formatted month for consistent matching
              _dateKey: isoMonth,
            };
          });
        }
      } else {
        // If no events, show current month only
        const now = new Date();
        const isoMonth = `${now.getFullYear()}-${String(
          now.getMonth() + 1
        ).padStart(2, "0")}`;

        dataPoints = [
          {
            time: now.toLocaleDateString([], {
              month: "short",
              year: "numeric",
            }),
            visitors: 0,
            pulse: 0,
            _dateKey: isoMonth,
          },
        ];
      }
      break;

    case "custom":
      if (customPeriod) {
        const start = new Date(customPeriod.startDate);
        start.setHours(0, 0, 0, 0);
        const end = new Date(customPeriod.endDate);
        end.setHours(23, 59, 59, 999);

        const diffTime = end.getTime() - start.getTime();
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

        if (diffDays === 1) {
          // For single day, show hourly data with same format as today/yesterday
          dataPoints = Array.from({ length: 24 }, (_, i) => {
            const hour =
              i === 0
                ? "12am"
                : i < 12
                ? `${i}am`
                : i === 12
                ? "12pm"
                : `${i - 12}pm`;
            const date = new Date(start);
            date.setHours(i);
            const isToday = date.toDateString() === now.toDateString();
            const isFuture = date > now;
            return {
              time: hour,
              visitors: isFuture ? null : 0,
              pulse: isToday && !isFuture ? 0 : null,
            };
          });
        } else if (diffDays < 3) {
          // For 2 days, show hourly data
          const totalHours = diffDays === 1 ? 24 : 48; // Ensure we show 24 hours for single day
          dataPoints = Array.from({ length: totalHours }, (_, i) => {
            const date = new Date(start);
            date.setHours(date.getHours() + i);
            const hour = date.getHours();
            const isToday = date.toDateString() === now.toDateString();
            const isFuture = date > now;
            return {
              time:
                hour === 0
                  ? "12am"
                  : hour < 12
                  ? `${hour}am`
                  : hour === 12
                  ? "12pm"
                  : `${hour - 12}pm`,
              visitors: isFuture ? null : 0,
              pulse: isToday && !isFuture ? 0 : null,
            };
          });
        } else {
          // For 3 or more days, show daily data
          dataPoints = Array.from({ length: diffDays }, (_, i) => {
            const date = new Date(start);
            date.setDate(date.getDate() + i);
            const isToday = date.toDateString() === now.toDateString();
            const isFuture = date > now;
            return {
              time: date.toLocaleDateString([], {
                month: "short",
                day: "numeric",
              }),
              visitors: isFuture ? null : 0,
              pulse: isToday && !isFuture ? 0 : null,
            };
          });
        }
      }
      break;
  }

  return dataPoints;
}
