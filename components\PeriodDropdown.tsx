import { CustomPeriod, Period } from "@/lib/types";
import React, {
  Dispatch,
  RefObject,
  SetStateAction,
  useState,
  useMemo,
} from "react";
import CustomPeriodModal from "./CustomPeriodModal";

const PERIOD_OPTIONS = [
  { id: "today", label: "Today" },
  { id: "yesterday", label: "Yesterday" },
  { id: "last24h", label: "Last 24 hours" },
  { id: "last7d", label: "Last 7 days" },
  { id: "last30d", label: "Last 30 days" },
  { id: "wtd", label: "Week to date" },
  { id: "mtd", label: "Month to date" },
  { id: "ytd", label: "Year to date" },
  { id: "all", label: "All time" },
  { id: "custom", label: "Custom" },
] as const;

const getPeriodLabel = (
  period: Period,
  customPeriod?: CustomPeriod
): string => {
  if (period === "custom" && customPeriod) {
    const sameDay =
      customPeriod.startDate.toLocaleDateString() ===
      customPeriod.endDate.toLocaleDateString();
    return sameDay
      ? customPeriod.startDate.toLocaleDateString()
      : `${customPeriod.startDate.toLocaleDateString()} - ${customPeriod.endDate.toLocaleDateString()}`;
  }

  const option = PERIOD_OPTIONS.find((opt) => opt.id === period);
  return option?.label || "Custom";
};

type PeriodDropdownProps = {
  periodDropdownRef: RefObject<HTMLDivElement | null>;
  setIsPeriodDropdownOpen: Dispatch<SetStateAction<boolean>>;
  isPeriodDropdownOpen: boolean;
  selectedPeriod: Period;
  setSelectedPeriod: Dispatch<SetStateAction<Period>>;
  customPeriod?: CustomPeriod;
  onCustomPeriodChange?: (period: CustomPeriod) => void;
};

function PeriodDropdown({
  periodDropdownRef,
  setIsPeriodDropdownOpen,
  isPeriodDropdownOpen,
  selectedPeriod,
  setSelectedPeriod,
  customPeriod,
  onCustomPeriodChange,
}: PeriodDropdownProps) {
  const [isCustomPeriodModalOpen, setIsCustomPeriodModalOpen] = useState(false);
  const customPeriodModalRef = React.useRef<HTMLDivElement>(null);

  function handleCustomPeriodApply(period: CustomPeriod) {
    setSelectedPeriod("custom");
    onCustomPeriodChange?.(period);
    setIsPeriodDropdownOpen(false);
  }

  const periodLabel = useMemo(
    () => getPeriodLabel(selectedPeriod, customPeriod),
    [selectedPeriod, customPeriod]
  );

  function handlePeriodChange(id: string) {
    localStorage.setItem("selected_period", id);
  }
  return (
    <>
      <div className="relative" ref={periodDropdownRef}>
        <button
          onClick={() => setIsPeriodDropdownOpen(!isPeriodDropdownOpen)}
          className="text-gray-400 flex items-center gap-1 hover:text-white transition-colors group px-2 sm:px-3 py-1.5 rounded-lg text-xs sm:text-sm"
        >
          <svg
            className="w-3 h-3 sm:w-4 sm:h-4 flex-shrink-0"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
            />
          </svg>
          <span className="group-hover:text-white whitespace-nowrap truncate max-w-[70px] sm:max-w-[150px]">
            {periodLabel}
          </span>
          <svg
            className={`w-3 h-3 sm:w-4 sm:h-4 flex-shrink-0 transition-transform ${
              isPeriodDropdownOpen ? "rotate-180" : ""
            }`}
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M19 9l-7 7-7-7"
            />
          </svg>
        </button>

        {isPeriodDropdownOpen && (
          <div className="absolute top-full left-0 right-0 sm:left-auto sm:right-0 mt-1 w-48 bg-[#363636] rounded-lg border border-gray-700/50 shadow-lg py-1 z-10 max-w-[calc(100vw-2rem)]">
            {PERIOD_OPTIONS.map((period) => (
              <button
                key={period.id}
                onClick={() => {
                  if (period.id === "custom") {
                    setIsCustomPeriodModalOpen(true);
                    handlePeriodChange("custom");
                  } else {
                    setSelectedPeriod(period.id as Period);
                    setIsPeriodDropdownOpen(false);
                    handlePeriodChange(period.id);
                  }
                }}
                className={`w-full px-4 py-2.5 text-left hover:bg-[#404040] transition-colors whitespace-nowrap ${
                  period.id === selectedPeriod
                    ? "bg-[#404040] text-white"
                    : "text-gray-400"
                } text-xs md:text-base`}
              >
                {period.label}
              </button>
            ))}
          </div>
        )}
      </div>

      <CustomPeriodModal
        modalRef={customPeriodModalRef}
        isOpen={isCustomPeriodModalOpen}
        onClose={() => setIsCustomPeriodModalOpen(false)}
        onApply={handleCustomPeriodApply}
        initialStartDate={customPeriod?.startDate}
        initialEndDate={customPeriod?.endDate}
      />
    </>
  );
}

export default React.memo(PeriodDropdown);
