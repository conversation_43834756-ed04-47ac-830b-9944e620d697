import { FixedSizeList as List } from 'react-window';
import { useMemo } from 'react';

interface TopPagesProps {
  pages: Array<{ path: string; views: number }>;
}

export default function TopPages({ pages }: TopPagesProps) {
  const sortedPages = useMemo(() => 
    [...pages].sort((a, b) => b.views - a.views),
    [pages]
  );

  const Row = ({ index, style }: { index: number; style: React.CSSProperties }) => {
    const page = sortedPages[index];
    return (
      <div style={style} className="flex justify-between items-center px-4 py-2 hover:bg-gray-50">
        <span className="text-sm text-gray-600">{page.path}</span>
        <span className="text-sm font-medium">{page.views}</span>
      </div>
    );
  };

  return (
    <div className="bg-white rounded-lg shadow">
      <div className="p-4 border-b">
        <h3 className="text-lg font-medium">Top Pages</h3>
      </div>
      <List
        height={400}
        itemCount={sortedPages.length}
        itemSize={40}
        width="100%"
      >
        {Row}
      </List>
    </div>
  );
} 