"use server";

import connect from "@/util/db";
import Website from "@/models/website";

export async function getWebsiteById(id: string) {
  try {
    await connect();
  
    const website = await Website.findOne({ id });
    
    if (!website) {
      return null;
    }
    
    return website.toObject();
  } catch (error) {
    console.error("Error fetching website for metadata:", error);
    return null;
  }
}
