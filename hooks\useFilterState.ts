import { useState, useEffect } from 'react';
import { Filter, LocationFilter, OtherFilter } from '@/lib/types';

// Create a global state for filters that persists across component instances
let globalActiveFilters: Filter[] = [];
let globalListeners: Function[] = [];

// Function to notify all listeners when the filter state changes
const notifyListeners = () => {
  globalListeners.forEach(listener => listener(globalActiveFilters));
};

export function useFilterState() {
  const [activeFilters, setLocalActiveFilters] = useState<Filter[]>(globalActiveFilters);

  // Sync local state with global state
  useEffect(() => {
    // Add this component as a listener
    const listener = (filters: Filter[]) => {
      setLocalActiveFilters([...filters]);
    };
    
    globalListeners.push(listener);
    
    // Remove listener on cleanup
    return () => {
      globalListeners = globalListeners.filter(l => l !== listener);
    };
  }, []);

  // Function to update both local and global filter state
  const setActiveFilters = (filters: Filter[]) => {
    globalActiveFilters = filters;
    setLocalActiveFilters([...filters]);
    notifyListeners();
  };

  // Function to handle filter click
  const handleFilterClick = (
    type: Filter["type"],
    value: string | { city: string; country: string } | { region: string; country: string },
    label: string
  ) => {
    // Check if this filter already exists
    const existingFilterIndex = globalActiveFilters.findIndex(
      (filter) => {
        // Ensure exact match for system filters (OS, browser, device)
        if (filter.type === type) {
          if (typeof value === 'object') {
            return JSON.stringify(filter.value) === JSON.stringify(value);
          } else {
            // Make sure we're doing an exact match for system filters
            return filter.value === value;
          }
        }
        return false;
      }
    );

    if (existingFilterIndex >= 0) {
      // Remove the filter if it exists
      setActiveFilters(
        globalActiveFilters.filter((_, index) => index !== existingFilterIndex)
      );
    } else {
      // Add the new filter
      if (type === 'location') {
        const locationFilter: LocationFilter = { 
          type: 'location', 
          value: value as LocationFilter['value'], 
          label 
        };
        setActiveFilters([...globalActiveFilters, locationFilter]);
      } else {
        const otherFilter: OtherFilter = { 
          type: type as OtherFilter['type'], 
          value: value as string, 
          label 
        };
        setActiveFilters([...globalActiveFilters, otherFilter]);
      }
    }
  };

  // Function to clear all filters
  const clearAllFilters = () => {
    setActiveFilters([]);
  };

  return {
    activeFilters,
    setActiveFilters,
    handleFilterClick,
    clearAllFilters
  };
} 