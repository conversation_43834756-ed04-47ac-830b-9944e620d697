export type Theme =
  | "light"
  | "dark"
  | "cupcake"
  | "bumblebee"
  | "emerald"
  | "corporate"
  | "synthwave"
  | "retro"
  | "cyberpunk"
  | "valentine"
  | "halloween"
  | "garden"
  | "forest"
  | "aqua"
  | "lofi"
  | "pastel"
  | "fantasy"
  | "wireframe"
  | "black"
  | "luxury"
  | "dracula"
  | "";

export interface ConfigProps {
  appName: string;
  appDescription: string;
  domainName: string;
  stripe: {
    plans: {
      isFeatured?: boolean;
      priceId: string;
      name: string;
      description?: string;
      price?: number;
      priceAnchor?: number;
      payOnceAnchor?: number;
      payOncePrice?: number;
      frequency: "annual"|"monthly",
      type: "subscription" | "payOnce";
      features: {
        name: string;
      }[];
      link?: string;
    }[];
  };
  aws?: {
    bucket?: string;
    bucketUrl?: string;
    cdn?: string;
  };
  emails: {
    supportEmail: string;
  };
//   colors: {
//     theme: Theme;
//     main: string;
//   };
  auth: {
    loginUrl: string;
    callbackUrl: string;
  };
}

declare global {
  interface Window {
    datafast: (event: string, data: any) => void;
    versatailor: (event: string, data: any) => void;
  }
}