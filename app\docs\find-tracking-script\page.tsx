import Image from "next/image";
import Link from "next/link";
import React from "react";

function Page() {
  return (
    <div className="max-w-3xl mx-auto py-8 px-4">
      <h1 className="text-3xl font-bold mb-6">Find Your Tracking Script</h1>

      <div className="space-y-6">
        <section className="space-y-4">
          <ol className="list-decimal list-inside space-y-3">
            <li>
              Go to your{" "}
              <Link
                href="/dashboard"
                target="_blank"
                className="text-blue-400 hover:underline"
              >
                dashboard
              </Link>{" "}
              and select your website
            </li>
            <li>
              Click on the dropdown menu for your website in the top left of
              your dashboard
            </li>
            <Image
              src={"/docs_assets/site_settings.png"}
              width={1200}
              height={600}
              alt="Site Settings screenshot"
            />
            <li>Click on &quot;Site Settings&quot;</li>
            <li>
              Under &quot;General&quot; tab, you&apos;ll find your unique
              tracking code in a dark code block
            </li>
            <li>
              Use the &quot;Copy Code&quot; button to copy the script to your
              clipboard
            </li>
          </ol>
        </section>

        <section className="space-y-4">
          <h2 className="text-2xl font-semibold">Installing the Script</h2>
          <div className="space-y-3">
            <p>Add the tracking script to your website by:</p>
            <ol className="list-decimal list-inside space-y-3">
              <li>Opening your website&apos;s HTML file or template</li>
              <li>
                Locating the{" "}
                <code className="bg-gray-800 px-2 py-1 rounded">
                  &lt;head&gt;
                </code>{" "}
                section
              </li>
              <li>
                Pasting the copied script inside the{" "}
                <code className="bg-gray-800 px-2 py-1 rounded">
                  &lt;head&gt;
                </code>{" "}
                tags
              </li>
            </ol>
          </div>
        </section>

        <section className="space-y-4">
          <h2 className="text-2xl font-semibold">Verifying Installation</h2>
          <div className="space-y-3">
            <p>To ensure your tracking script is properly installed:</p>
            <ol className="list-decimal list-inside space-y-3">
              <li>Return to the Site Settings page</li>
              <Image
                src={"/docs_assets/verify.png"}
                width={1200}
                height={600}
                alt="Site Settings screenshot"
              />
              <li>Click the &quot;Verify Installation&quot; button</li>
              <li>Wait for the validation check to complete</li>
            </ol>
            <div className="bg-gray-800 p-4 rounded-lg mt-4">
              <h3 className="text-lg font-medium mb-2">Validation Results:</h3>
              <ul className="list-disc list-inside space-y-2">
                <li className="text-green-400">
                  ✓ Success: &quot;The script is correctly installed on your
                  website!&quot;
                </li>
                <li className="text-red-400">
                  ✗ Error: &quot;Could not find the tracking script&quot;
                </li>
              </ul>
            </div>
          </div>
        </section>
      </div>
    </div>
  );
}

export default Page;
