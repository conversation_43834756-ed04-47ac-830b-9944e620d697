import { NextRequest, NextResponse } from "next/server";
import { auth } from "@clerk/nextjs/server";
import connect from "@/util/db";
import Website from "@/models/website";
import Event from "@/models/event";

export async function GET(
  request: NextRequest,
  props: { params: Promise<{ id: string }> }
) {
  const params = await props.params;
  try {
    const { userId } = await auth();

    // Check if user is authenticated
    if (!userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    await connect();

    // Conditionally include userId in query based on environment
    const query =
      process.env.NODE_ENV === "production"
        ? { id: params.id, userId }
        : { id: params.id };

    const website = await Website.findOne(query);

    if (!website) {
      return NextResponse.json({ error: "Website not found" }, { status: 404 });
    }

    // Check if any events exist for this website
    const eventCount = await Event.countDocuments({ websiteId: params.id });
    const hasReceivedEvents = eventCount > 0;

    // Convert to plain object and add the flag
    const websiteObject = website.toObject();
    websiteObject.hasReceivedEvents = hasReceivedEvents;

    return NextResponse.json(websiteObject);
  } catch (error) {
    console.error("Error fetching website:", error);
    return NextResponse.json(
      { error: "Failed to fetch website" },
      { status: 500 }
    );
  }
}

export async function PATCH(
  request: NextRequest,
  props: { params: Promise<{ id: string }> }
) {
  const params = await props.params;
  try {
    const { userId } = await auth();

    // Check if user is authenticated
    if (!userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    await connect();

    // Conditionally include userId in query based on environment
    const query =
      process.env.NODE_ENV === "production"
        ? { id: params.id, userId }
        : { id: params.id };

    const website = await Website.findOne(query).select("+stripeApiKey +stripeWebhookSecret");

    if (!website) {
      return NextResponse.json({ error: "Website not found" }, { status: 404 });
    }

    const body = await request.json();
    const { 
      excludeAuthVisits, 
      privateRoutes, 
      revenueAttributionEnabled,
      stripeWebhookId 
    } = body;

    // Handle regular website settings
    if (excludeAuthVisits !== undefined) {
      website.excludeAuthVisits = excludeAuthVisits;
    }
    if (privateRoutes !== undefined) {
      website.privateRoutes = privateRoutes;
    }

    // Handle revenue attribution settings
    if (revenueAttributionEnabled !== undefined) {
      if (revenueAttributionEnabled === false && website.revenueAttributionEnabled === true) {
        // Disabling revenue attribution - cleanup webhooks
        if (website.stripeWebhookId && website.stripeApiKey) {
          try {
            // Import webhook cleanup functions
            const { deleteWebhookEndpoint, decryptApiKey } = await import("@/lib/stripe-utils");
            
            const decryptedApiKey = decryptApiKey(website.stripeApiKey);
            const deleteResult = await deleteWebhookEndpoint(decryptedApiKey, website.stripeWebhookId);
            
            if (!deleteResult.success) {
              console.error("Failed to delete webhook from Stripe:", deleteResult.error);
              // Continue anyway to clean up local data
            }
          } catch (error) {
            console.error("Error deleting webhook:", error);
            // Continue anyway to clean up local data
          }
        }

        // Clear webhook configuration
        website.stripeApiKey = undefined;
        website.stripeWebhookId = undefined;
        website.stripeWebhookSecret = undefined;
        website.revenueAttributionEnabled = false;
      } else {
        website.revenueAttributionEnabled = revenueAttributionEnabled;
      }
    }

    // Handle webhook ID updates (for when enabling revenue attribution)
    if (stripeWebhookId !== undefined) {
      website.stripeWebhookId = stripeWebhookId;
    }

    await website.save();

    // Return the updated website without sensitive fields
    const responseWebsite = await Website.findOne(query);
    return NextResponse.json(responseWebsite);
  } catch (error) {
    console.error("Error updating website:", error);
    return NextResponse.json(
      { error: "Failed to update website" },
      { status: 500 }
    );
  }
}
// FIXME: when deleting websites, append user tag to orphaned status
// FIXME: when deleting websites, delete all their events
export async function DELETE(
  request: NextRequest,
  props: { params: Promise<{ id: string }> }
) {
  const params = await props.params;
  try {
    const { userId } = await auth();

    // Check if user is authenticated
    if (!userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    await connect();

    // Always query by both id and userId
    const query = { id: params.id, userId };

    const website = await Website.findOne(query);

    if (!website) {
      // This now correctly handles cases where the website exists but doesn't belong to the user
      return NextResponse.json({ error: "Website not found or access denied" }, { status: 404 });
    }

    // Set the userId to an empty string instead of deleting
    website.userId = "__ORPHANED__";
    await website.save();

    // Return a success response or the modified object
    return NextResponse.json({ message: "Website user association removed successfully.", website });

  } catch (error) {
    console.error("Error updating website user association:", error);
    return NextResponse.json(
      { error: "Failed to update website user association" },
      { status: 500 }
    );
  }
}
