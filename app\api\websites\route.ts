import { NextRequest, NextResponse } from "next/server";
import { auth } from "@clerk/nextjs/server";
import connect from "@/util/db";
import Website from "@/models/website";

export async function POST(request: NextRequest) {
  try {
    const { userId } = await auth();

    // Check if user is authenticated
    if (!userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    await connect();
    const body = await request.json();

    const website = new Website({
      name: body.name,
      domain: body.domain,
      userId: userId,
    });

    await website.save();
    return NextResponse.json(website);
  } catch (error: any) {
    console.error("Error creating website:", error);
    
    // Check if this is a duplicate key error
    if (error.code === 11000) {
      return NextResponse.json(
        { error: "Website with this domain already exists." },
        { status: 409 }
      );
    }

    return NextResponse.json(
      { error: "Failed to create website" },
      { status: 500 }
    );
  }
}

export async function GET() {
  try {
    const { userId } = await auth();

    // Check if user is authenticated
    if (!userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    await connect();
    const websites = await Website.find({ userId }).sort({ createdAt: -1 });
    return NextResponse.json(websites);
  } catch (error) {
    console.error("Error fetching websites:", error);
    return NextResponse.json(
      { error: "Failed to fetch websites" },
      { status: 500 }
    );
  }
}
