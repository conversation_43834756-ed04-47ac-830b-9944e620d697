import { useFilterState } from "@/hooks/useFilterState";
import { GroupedReferrers } from "@/hooks/useGroupedData";
import { TopReferrer } from "@/lib/types";
import { getFriendlyReferrerName } from "@/lib/utils"; // Import shared function
import React, { Dispatch, SetStateAction, useState } from "react";

// Removed local getFriendlyReferrerName function

type ReferrerSectionProps = {
  topReferrers: TopReferrer[];
  maxReferrerVisitors: number;
  groupedReferrers: GroupedReferrers;
  setShowReferrerModal: Dispatch<SetStateAction<boolean>>;
  websiteDomain?: string;
};

function ReferrerSection({
  topReferrers,
  maxReferrerVisitors,
  groupedReferrers,
  setShowReferrerModal,
  websiteDomain,
}: ReferrerSectionProps) {
  const { handleFilterClick, activeFilters } = useFilterState();
  const [referrerView, setReferrerView] = useState<
    "referrer" | "campaign" | "utm"
  >("referrer");
  return (
    <div className="bg-[#2A2A2A] p-4 sm:p-6 rounded-lg">
      <div className="flex items-center justify-between mb-3">
        <h2 className="text-base sm:text-lg font-semibold text-white">
          Referrer
        </h2>
        <div className="flex rounded-lg p-1">
          <button
            onClick={() => setReferrerView("referrer")}
            className={`px-2 py-1 rounded-md text-xs sm:text-sm transition-colors ${referrerView === "referrer"
              ? "bg-blue-500 text-white"
              : "text-gray-400 hover:text-white"
              }`}
          >
            Referrer
          </button>
          <button
            onClick={() => setReferrerView("campaign")}
            className={`px-2 py-1 rounded-md text-xs sm:text-sm transition-colors ${referrerView === "campaign"
              ? "bg-blue-500 text-white"
              : "text-gray-400 hover:text-white"
              }`}
          >
            Campaign
          </button>
          <button
            onClick={() => setReferrerView("utm")}
            className={`px-2 py-1 rounded-md text-xs sm:text-sm transition-colors ${referrerView === "utm"
              ? "bg-blue-500 text-white"
              : "text-gray-400 hover:text-white"
              }`}
          >
            UTM
          </button>
        </div>
      </div>
      <div className="space-y-1 h-[250px] overflow-hidden relative">
        {referrerView === "referrer" &&
          topReferrers
            // First convert to displayReferrers
            .map((referrer: TopReferrer) => {
              let displayReferrer = { ...referrer };
              if (!referrer.url || referrer.url === '') {
                displayReferrer.url = null; // Mark as Direct/None if URL is empty
              } else if (referrer.url) {
                // Check for same domain, website domain, or Google Accounts to mark as Direct/None
                let isSameDomain = false;
                if (typeof window !== 'undefined') {
                  try {
                    // Compare hostname only
                    const referrerHostname = new URL(referrer.url.startsWith('http') ? referrer.url : `https://${referrer.url}`).hostname;
                    const currentHostname = window.location.hostname;
                    isSameDomain = referrerHostname === currentHostname;
                  } catch (e) {
                    console.error('Error parsing or comparing domains:', e);
                    // Keep the original URL if parsing fails
                    isSameDomain = false;
                  }
                }
                // Check if the referrer URL includes the website's domain
                const isWebsiteDomain = websiteDomain && referrer.url.includes(websiteDomain);
                // Check if the referrer is Google Accounts
                const isGoogleAccounts = referrer.url && referrer.url.includes('accounts.google.com');

                // Only nullify URL for Google Accounts, same domain, or website domain
                if (isGoogleAccounts || isSameDomain || isWebsiteDomain) {
                  displayReferrer.url = null; // This will make it show as Direct/None
                } else {
                  // Normalize the URL format for consistent grouping
                  try {
                    const url = new URL(referrer.url.startsWith('http') ? referrer.url : `https://${referrer.url}`);
                    // Store just the hostname for grouping purposes
                    displayReferrer.normalizedDomain = url.hostname.replace(/^www\./, "");
                  } catch (e) {
                    // If URL parsing fails, try a simpler normalization
                    displayReferrer.normalizedDomain = referrer.url.replace(/^(https?:\/\/)?(www\.)?/, "").split("/")[0];
                  }
                }
              }
              return displayReferrer;
            })
            // Then group and combine entries with the same domain
            .reduce((acc: TopReferrer[], curr: TopReferrer) => {
              if (!curr.url) {
                // This is a Direct/None entry
                const existingDirectNone = acc.find(r => !r.url);
                if (existingDirectNone) {
                  // Add to existing Direct/None
                  existingDirectNone.visitors += curr.visitors;
                  return acc;
                }
              } else if (curr.normalizedDomain) {
                // Look for existing entry with the same normalized domain
                const existingDomain = acc.find(r => 
                  r.normalizedDomain && r.normalizedDomain === curr.normalizedDomain);
                
                if (existingDomain) {
                  // Add visitors to the existing domain entry
                  existingDomain.visitors += curr.visitors;
                  
                  // Merge referral params if needed
                  if (curr.referralParams) {
                    existingDomain.referralParams = {
                      ...existingDomain.referralParams,
                      ...curr.referralParams
                    };
                  }
                  return acc;
                }
              }
              // Add as new entry
              acc.push(curr);
              return acc;
            }, [])
            .map((displayReferrer: TopReferrer, i) => {
              // Moved calculations inside the component return where displayReferrer.url is checked

              return (
                <div
                  key={i}
                  className={`flex items-center justify-between p-1 sm:p-2 rounded text-xs sm:text-sm relative overflow-hidden group cursor-pointer ${activeFilters.some(
                    (filter) =>
                      filter.type === "referrer" && 
                      ((!displayReferrer.url && filter.value === "direct") || 
                       (displayReferrer.normalizedDomain && 
                        filter.value === displayReferrer.normalizedDomain))
                  )
                    ? "bg-blue-500/20"
                    : ""
                    }`}
                  onClick={() => {
                    const cleanedUrl = displayReferrer.url 
                      ? displayReferrer.url.replace(/^(https?:\/\/)?(www\.)?/, "").replace(/\/$/, "") 
                      : "Direct/None";
                    // Use normalized domain for filtering if available, otherwise fall back to URL
                    const filterValue = !displayReferrer.url 
                      ? "direct" 
                      : displayReferrer.normalizedDomain || displayReferrer.url;
                    
                    handleFilterClick(
                      "referrer",
                      filterValue,
                      getFriendlyReferrerName(displayReferrer.url) || cleanedUrl
                    );
                  }}
                >
                  <div
                    className="absolute inset-0 bg-blue-500/40 transition-transform origin-left group-hover:bg-blue-500/20"
                    style={{
                      transform: `scaleX(${displayReferrer.visitors / maxReferrerVisitors
                        })`,
                    }}
                  />
                  <span className="text-white flex items-center gap-2 min-w-0 relative">
                    {displayReferrer.url ? (() => {
                      // Calculate these only when url is definitely a string
                      const friendlyName = getFriendlyReferrerName(displayReferrer.url);
                      const displayName = friendlyName || displayReferrer.url.replace(/^(https?:\/\/)?(www\.)?/, "").replace(/\/$/, "");
                      const faviconDomain = friendlyName === "X" ? "x.com" : displayReferrer.url;

                      return (
                        <>
                          <img
                            src={`https://www.google.com/s2/favicons?domain=${faviconDomain}&sz=32`}
                            alt=""
                            className="w-4 h-4 flex-shrink-0"
                            onError={(e) => { // Keep only the outer onError
                              const target = e.target as HTMLImageElement;
                              target.onerror = null; // Prevent infinite loop if fallback fails
                              target.src = "https://icons.duckduckgo.com/ip3/null.ico";
                            }}
                          />
                          <span className="truncate">
                            {displayName}
                            {displayReferrer.referralParams?.referral && (
                              <span className="text-blue-400 ml-1">
                                ({displayReferrer.referralParams.referral})
                              </span>
                            )}
                          </span>
                        </>
                      );
                    })() : (
                      <>
                        <img
                          src="https://icons.duckduckgo.com/ip3/null.ico"
                          height={16}
                          width={16}
                          className="flex-shrink-0"
                        />
                        <span className="truncate">Direct/None</span>
                      </>
                    )}
                    {displayReferrer.url && (
                      <a
                        href={displayReferrer.url.startsWith('http') ? displayReferrer.url : `https://${displayReferrer.url}`}
                        target="_blank"
                        rel="noopener noreferrer"
                        className=" bg-blue-500 hover:bg-blue-600 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity z-10"
                        onClick={(e) => e.stopPropagation()}
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                        </svg>
                      </a>
                    )}
                  </span>
                  <span className="text-white ml-2 flex-shrink-0 relative">
                    {displayReferrer.visitors}
                  </span>
                </div>
              );
            })}
        {referrerView === "campaign" &&
          groupedReferrers.campaign.map((referrer, i) => (
            <div
              key={i}
              className={`flex items-center justify-between p-1 sm:p-2 rounded text-xs sm:text-sm relative overflow-hidden group cursor-pointer ${activeFilters.some(
                (filter) =>
                  filter.type === "campaign" &&
                  filter.value === referrer.campaign
              )
                ? "bg-blue-500/20"
                : ""
                }`}
              onClick={() =>
                handleFilterClick(
                  "campaign",
                  referrer.campaign,
                  referrer.campaign
                )
              }
            >
              <div
                className="absolute inset-0 bg-blue-500/40 transition-transform origin-left group-hover:bg-blue-500/20"
                style={{
                  transform: `scaleX(${referrer.visitors / maxReferrerVisitors
                    })`,
                }}
              />
              <span className="text-white flex items-center gap-2 min-w-0 relative">
                <svg
                  className="w-4 h-4"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M11 4a2 2 0 114 0v1a1 1 0 001 1h3a1 1 0 011 1v3a1 1 0 01-1 1h-1a2 2 0 100 4h1a1 1 0 011 1v3a1 1 0 01-1 1h-3a1 1 0 01-1-1v-1a2 2 0 10-4 0v1a1 1 0 01-1 1H7a1 1 0 01-1-1v-3a1 1 0 00-1-1H4a2 2 0 110-4h1a1 1 0 001-1V7a1 1 0 011-1h3a1 1 0 001-1V4z"
                  />
                </svg>
                {/* Always display the campaign name in the campaign tab */}
                <span className="truncate">{referrer.campaign}</span>
              </span>
              <span className="text-white ml-2 flex-shrink-0 relative">
                {referrer.visitors}
              </span>
            </div>
          ))}
        {referrerView === "utm" &&
          groupedReferrers.utm.map((referrer, i) => (
            <div
              key={i}
              className={`flex items-center justify-between p-1 sm:p-2 rounded text-xs sm:text-sm relative overflow-hidden group cursor-pointer ${activeFilters.some(
                (filter) =>
                  filter.type === "utm" &&
                  filter.value ===
                  `${referrer.source}${referrer.medium ? ` / ${referrer.medium}` : ""
                  }`
              )
                ? "bg-blue-500/20"
                : ""
                }`}
              onClick={() =>
                handleFilterClick(
                  "utm",
                  `${referrer.source}${referrer.medium ? ` / ${referrer.medium}` : ""
                  }`,
                  referrer.source
                    ? `${referrer.source}${referrer.medium ? ` / ${referrer.medium}` : ""
                    }`
                    : "No UTM"
                )
              }
            >
              <div
                className="absolute inset-0 bg-blue-500/40 transition-transform origin-left group-hover:bg-blue-500/20"
                style={{
                  transform: `scaleX(${referrer.visitors / maxReferrerVisitors
                    })`,
                }}
              />
              <span className="text-white flex items-center gap-2 min-w-0 relative">
                <svg
                  className="w-4 h-4"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M13 10V3L4 14h7v7l9-11h-7z"
                  />
                </svg>
                <span className="truncate">
                  {referrer.source
                    ? `${referrer.source}${referrer.medium ? ` / ${referrer.medium}` : ""
                    }`
                    : "No UTM"}
                </span>
              </span>
              <span className="text-white ml-2 flex-shrink-0 relative">
                {referrer.visitors}
              </span>
            </div>
          ))}
        {(() => {
          // Get the filtered and grouped referrers for the current view
          const displayReferrers = referrerView === "referrer" 
            ? topReferrers
                .map((referrer: TopReferrer) => {
                  let displayReferrer = { ...referrer };
                  if (!referrer.url || referrer.url === '') {
                    displayReferrer.url = null;
                  } else if (referrer.url) {
                    let isSameDomain = false;
                    if (typeof window !== 'undefined') {
                      try {
                        const referrerHostname = new URL(referrer.url.startsWith('http') ? referrer.url : `https://${referrer.url}`).hostname;
                        const currentHostname = window.location.hostname;
                        isSameDomain = referrerHostname === currentHostname;
                      } catch (e) {
                        console.error('Error parsing or comparing domains:', e);
                        isSameDomain = false;
                      }
                    }
                    const isWebsiteDomain = websiteDomain && referrer.url.includes(websiteDomain);
                    const isGoogleAccounts = referrer.url && referrer.url.includes('accounts.google.com');

                    // Apply the same logic as above for consistency in the "Show more" button logic
                    if (isGoogleAccounts || isSameDomain || isWebsiteDomain) {
                      displayReferrer.url = null;
                    } else {
                      // Normalize the URL format for consistent grouping
                      try {
                        const url = new URL(referrer.url.startsWith('http') ? referrer.url : `https://${referrer.url}`);
                        // Store just the hostname for grouping purposes
                        displayReferrer.normalizedDomain = url.hostname.replace(/^www\./, "");
                      } catch (e) {
                        // If URL parsing fails, try a simpler normalization
                        displayReferrer.normalizedDomain = referrer.url.replace(/^(https?:\/\/)?(www\.)?/, "").split("/")[0];
                      }
                    }
                  }
                  return displayReferrer;
                })
                .reduce((acc: TopReferrer[], curr: TopReferrer) => {
                  if (!curr.url) {
                    const existingDirectNone = acc.find(r => !r.url);
                    if (existingDirectNone) {
                      existingDirectNone.visitors += curr.visitors;
                      return acc;
                    }
                  } else if (curr.normalizedDomain) {
                    // Look for existing entry with the same normalized domain
                    const existingDomain = acc.find(r => 
                      r.normalizedDomain && r.normalizedDomain === curr.normalizedDomain);
                    
                    if (existingDomain) {
                      // Add visitors to the existing domain entry
                      existingDomain.visitors += curr.visitors;
                      
                      // Merge referral params if needed
                      if (curr.referralParams) {
                        existingDomain.referralParams = {
                          ...existingDomain.referralParams,
                          ...curr.referralParams
                        };
                      }
                      return acc;
                    }
                  }
                  acc.push(curr);
                  return acc;
                }, [])
            : referrerView === "campaign"
              ? groupedReferrers.campaign
              : groupedReferrers.utm;

          return displayReferrers.length > 5 && (
            <button
              onClick={() => setShowReferrerModal(true)}
              className="absolute bottom-0 left-0 right-0 text-center py-2 text-blue-500 hover:text-blue-400 bg-gradient-to-t from-[#2A2A2A] via-[#2A2A2A] to-transparent"
            >
              Show more
            </button>
          );
        })()}
      </div>
    </div>
  );
}
export default ReferrerSection;
