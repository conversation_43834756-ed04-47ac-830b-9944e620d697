"use client";
import Link from "next/link";
import { useState, useEffect } from "react";
import { usePathname } from "next/navigation";
import { UserButton } from "@clerk/nextjs";
import LogoComponent from "@/components/LogoComponent";
import DocsBreadcrumb from "@/components/DocsBreadcrumb";
import UserDropdown from "@/components/UserDropdown";
import Image from "next/image";

// Navigation sections for docs
const docsNavigation = [
  {
    title: "Get Started",
    items: [
      { name: "Set up your account", href: "/docs/set-up-your-account" },
      { name: "Import your data", href: "/docs/import-your-data" },
      { name: "Find your tracking script", href: "/docs/find-tracking-script" },
    ],
  },
  {
    title: "Website Settings",
    items: [
      {
        name: "Exclude authenticated visits",
        href: "/docs/exclude-auth-visits",
      },
      { name: "Exclue your own visits", href: "/docs/exclude-your-own-visits" },
    ],
  },
  {
    title: "Events",
    items: [
      {
        name: "Track your events",
        href: "/docs/track-events",
      },
    ],
  },
];

export default function DocsLayoutClient({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const pathname = usePathname();
  const [sidebarOpen, setSidebarOpen] = useState(false);

  // Close sidebar when pathname changes (navigation occurs)
  useEffect(() => {
    setSidebarOpen(false);
  }, [pathname]);

  // Close sidebar when escape key is pressed
  useEffect(() => {
    const handleEscKey = (event: KeyboardEvent) => {
      if (event.key === "Escape") {
        setSidebarOpen(false);
      }
    };

    window.addEventListener("keydown", handleEscKey);

    // Disable body scroll when sidebar is open on mobile
    if (sidebarOpen) {
      document.body.style.overflow = "hidden";
    } else {
      document.body.style.overflow = "";
    }

    return () => {
      window.removeEventListener("keydown", handleEscKey);
      document.body.style.overflow = "";
    };
  }, [sidebarOpen]);

  return (
    <div className="flex min-h-screen flex-col bg-customGray text-gray-100">
      {/* Header */}
      <header className="sticky top-0 z-40 backdrop-blur-md bg-customGray/90 border-b border-gray-800/30">
        <div className="flex h-16 items-center justify-between px-4 md:px-6 max-w-screen-2xl mx-auto">
          <div className="flex items-center gap-4">
            <Link
              href="/docs"
              className=" flex  gap-2 items-center text-lg font-medium text-gray-100 hover:text-white transition-colors"
            >
              <Image
                src="/icon.png"
                alt="Versatailor logo"
                width={32}
                height={32}
                className="rounded-full"
              />
              <p className="text-white text-xl">Versatailor</p>| DOCS
            </Link>
          </div>

          <div className="flex items-center space-x-4">
            <button
              type="button"
              className="md:hidden p-2 rounded-md text-gray-400 hover:text-white hover:bg-customGrayLight/50 transition-colors"
              onClick={() => setSidebarOpen(!sidebarOpen)}
              aria-label={sidebarOpen ? "Close sidebar" : "Open sidebar"}
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="20"
                height="20"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="h-5 w-5"
              >
                {sidebarOpen ? (
                  <>
                    <line x1="18" y1="6" x2="6" y2="18"></line>
                    <line x1="6" y1="6" x2="18" y2="18"></line>
                  </>
                ) : (
                  <>
                    <line x1="4" y1="12" x2="20" y2="12"></line>
                    <line x1="4" y1="6" x2="20" y2="6"></line>
                    <line x1="4" y1="18" x2="20" y2="18"></line>
                  </>
                )}
              </svg>
            </button>

            <UserDropdown />
          </div>
        </div>
      </header>

      <div className="flex flex-1 overflow-hidden max-w-screen-2xl w-full mx-auto">
        {/* Mobile backdrop - appears when sidebar is open */}
        {sidebarOpen && (
          <div
            className="fixed inset-0 bg-black/60 backdrop-blur-sm z-20 md:hidden transition-opacity"
            onClick={() => setSidebarOpen(false)}
            aria-hidden="true"
          />
        )}

        {/* Sidebar */}
        <div
          className={`${
            sidebarOpen
              ? "fixed inset-y-0 left-0 z-30 animate-slide-in-left pt-20"
              : "hidden"
          } md:relative md:block md:z-0 md:w-64 lg:w-72 md:pt-0 bg-customGray overflow-y-auto w-[75%] max-w-xs flex-shrink-0 transition-all duration-300 ease-in-out border-r border-gray-800/30`}
        >
          <div className="relative z-10 p-4 md:p-6 bg-customGray h-full">
            {/* Mobile close button - top right of sidebar */}
            <button
              type="button"
              className="absolute top-4 right-4 p-1 rounded-full text-gray-400 hover:text-white hover:bg-customGrayLight/50 md:hidden transition-colors"
              onClick={() => setSidebarOpen(false)}
              aria-label="Close sidebar"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="18"
                height="18"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <line x1="18" y1="6" x2="6" y2="18"></line>
                <line x1="6" y1="6" x2="18" y2="18"></line>
              </svg>
            </button>

            {docsNavigation.map((section) => (
              <div key={section.title} className="mb-8">
                <h2 className="mb-3 text-xs font-medium text-gray-400 uppercase tracking-wider">
                  {section.title}
                </h2>
                <ul className="space-y-1">
                  {section.items.map((item) => (
                    <li key={item.name}>
                      <Link
                        href={item.href}
                        className={`block px-3 py-2 rounded-md text-sm transition-all duration-200 ${
                          pathname === item.href
                            ? "bg-customGrayLight text-white font-medium"
                            : "text-gray-300 hover:text-white hover:bg-customGrayLight/40"
                        }`}
                      >
                        {item.name}
                      </Link>
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
        </div>

        {/* Main content area */}
        <main className="flex-1 overflow-y-auto border-none">
          {/* Main content container */}
          <div className="w-full max-w-4xl mx-auto px-4 md:px-8 pb-16 pt-4">
            {/* Position breadcrumbs right above the content without taking space */}
            <div className="sticky top-0 left-0 w-full z-10 bg-customGray/90 backdrop-blur-md py-3">
              <DocsBreadcrumb />
            </div>

            {/* Main content with subtle fade-in animation */}
            <div className="animate-fade-in pt-2">{children}</div>

            {/* Help section consistently at the bottom of every page */}
            <div className="mt-16 pt-6 border-t border-gray-800/30 flex justify-between">
              <p className="text-gray-400 text-sm">
                Need help?{" "}
                <Link
                  href="mailto:<EMAIL>"
                  className="text-blue-400 hover:text-blue-300 transition-colors"
                  target="_blank"
                >
                  Contact us
                </Link>{" "}
                for assistance.
              </p>
              <p className="text-gray-400 text-sm">
                Suggest features?{" "}
                <Link
                  href="https://versatailor.featurebase.app"
                  className="text-blue-400 hover:text-blue-300 transition-colors"
                  target="_blank"
                >
                  We&apos;d love your feedback
                </Link>{" "}
              </p>
            </div>
          </div>
        </main>
      </div>
    </div>
  );
}
