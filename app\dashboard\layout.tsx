import type { Metadata } from "next";
import TopBanner from "@/components/TopBanner";
import LogoComponent from "@/components/LogoComponent";
import UserDropdown from "@/components/UserDropdown";

export const metadata: Metadata = {
  title: "Websites | VersaTailor",
  description: "Websites",
};

export default function DashboardLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <>
      <TopBanner />
      <div className="min-h-screen bg-customGray text-gray-100 overflow-x-hidden">
        <div className="container mx-auto px-4 py-8 max-w-full sm:max-w-6xl">
          <div className="flex items-center justify-between mb-8">
            <div className="flex items-center gap-4">
              <LogoComponent />
            </div>
            <div className="flex items-center gap-4">
              <UserDropdown />
            </div>
          </div>
          {children}
        </div>
      </div>
    </>
  );
}
