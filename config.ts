import { ConfigProps } from "@/types/config";

const config = {
  // REQUIRED
  appName: "VersaTailor",
  // REQUIRED: a short description of your app for SEO tags (can be overwritten)
  appDescription:
    "VersaTailor is the ultimate web analytics tool for SaaS and only businesses.",
  // REQUIRED (no https://, not trialing slash at the end, just the naked domain)
  domainName: "versatailor.com",
  stripe: {
    // Create multiple plans in your Stripe dashboard, then add them here. You can add as many plans as you want, just make sure to add the priceId
    plans: [
      {
        priceId:
          process.env.NODE_ENV === "development"
            ? "price_1QxSnGKHseo214m6T8QOwLqW"
            : "price_1R8teVKHseo214m6ljEMRGg0",
        name: "Plus",
        price: 9,
        frequency: "monthly",
        type: "subscription",
        features: [
          {
            name: "10k Monthly",
          },
        ],
      },
      {
        priceId:
          process.env.NODE_ENV === "development"
            ? "price_1QxSnGKHseo214m6OrH8iED8"
            : "price_1R8teVKHseo214m6voxJSm5h",
        name: "Plus",
        price: 90,
        frequency: "annual",
        type: "subscription",
        features: [
          {
            name: "10k Yearly",
          },
        ],
      },
      {
        priceId:
          process.env.NODE_ENV === "development"
            ? "price_1QxSoOKHseo214m6Pn2ATdGd"
            : "price_1R8tg7KHseo214m6MITyllNR",
        name: "Plus",
        price: 19,
        frequency: "monthly",
        type: "subscription",
        features: [
          {
            name: "100k Monthly",
          },
        ],
      },
      {
        priceId:
          process.env.NODE_ENV === "development"
            ? "price_1QxSoeKHseo214m65wNNqLBH"
            : "price_1R8tg7KHseo214m6Apy8X1A2",
        name: "Plus",
        price: 190,
        frequency: "annual",
        type: "subscription",
        features: [
          {
            name: "100k Yearly",
          },
        ],
      },
      {
        priceId:
          process.env.NODE_ENV === "development"
            ? "price_1R8sk1KHseo214m6cRHEqtS1"
            : "price_1R8tiBKHseo214m6fi9fTJzJ",
        name: "Plus",
        price: 29,
        frequency: "monthly",
        type: "subscription",
        features: [
          {
            name: "200k Monthly",
          },
        ],
      },
      {
        priceId:
          process.env.NODE_ENV === "development"
            ? "price_1R8spMKHseo214m6vXEEPFMU"
            : "price_1R8tiAKHseo214m6nXkfDJEo",
        name: "Plus",
        price: 290,
        frequency: "annual",
        type: "subscription",
        features: [
          {
            name: "200k Yearly",
          },
        ],
      },

      {
        priceId:
          process.env.NODE_ENV === "development"
            ? "price_1R8srVKHseo214m6nvjB1mvW"
            : "price_1R8tk5KHseo214m60ne8bEgX",
        name: "Plus",
        price: 49,
        frequency: "monthly",
        type: "subscription",
        features: [
          {
            name: "500k Monthly",
          },
        ],
      },
      {
        priceId:
          process.env.NODE_ENV === "development"
            ? "price_1R8sruKHseo214m63D7vcwMS"
            : "price_1R8tk5KHseo214m6CYkWLQMA",
        name: "Plus",
        price: 490,
        frequency: "annual",
        type: "subscription",
        features: [
          {
            name: "500k Yearly",
          },
        ],
      },
      {
        priceId:
          process.env.NODE_ENV === "development"
            ? "price_1R8stRKHseo214m6OBGp5w4Z"
            : "price_1R8tkaKHseo214m6cFBNyn1w",
        name: "Plus",
        price: 69,
        frequency: "monthly",
        type: "subscription",
        features: [
          {
            name: "1M Monthly",
          },
        ],
      },
      {
        priceId:
          process.env.NODE_ENV === "development"
            ? "price_1R8stuKHseo214m62TqE2fNc"
            : "price_1R8tkaKHseo214m6nwFAOgK3",
        name: "Plus",
        price: 690,
        frequency: "annual",
        type: "subscription",
        features: [
          {
            name: "1M Yearly",
          },
        ],
      },
      {
        priceId:
          process.env.NODE_ENV === "development"
            ? "price_1R8svhKHseo214m6eheKzHfd"
            : "price_1R8tlTKHseo214m64Rbmiwo5",
        name: "Plus",
        price: 89,
        frequency: "monthly",
        type: "subscription",
        features: [
          {
            name: "2M Monthly",
          },
        ],
      },
      {
        priceId:
          process.env.NODE_ENV === "development"
            ? "price_1R8tDqKHseo214m6A1M3nVVj"
            : "price_1R8tlTKHseo214m6bhhDAj19",
        name: "Plus",
        price: 890,
        frequency: "annual",
        type: "subscription",
        features: [
          {
            name: "2M Yearly",
          },
        ],
      },
      {
        priceId:
          process.env.NODE_ENV === "development"
            ? "price_1R8sznKHseo214m6wnScCHIN"
            : "price_1R8tmgKHseo214m6ouLxtPB2",
        name: "Plus",
        price: 129,
        frequency: "monthly",
        type: "subscription",
        features: [
          {
            name: "5M Monthly",
          },
        ],
      },
      {
        priceId:
          process.env.NODE_ENV === "development"
            ? "price_1R8sznKHseo214m6U5OlRhZE"
            : "price_1R8tmgKHseo214m6sNvXrVpn",
        name: "Plus",
        price: 1290,
        frequency: "annual",
        type: "subscription",
        features: [
          {
            name: "5M Yearly",
          },
        ],
      },
      {
        priceId:
          process.env.NODE_ENV === "development"
            ? "price_1R8t0qKHseo214m65aA7veNO"
            : "price_1R8tnGKHseo214m6TBeC8sfz",
        name: "Plus",
        price: 169,
        frequency: "monthly",
        type: "subscription",
        features: [
          {
            name: "10M Monthly",
          },
        ],
      },
      {
        priceId:
          process.env.NODE_ENV === "development"
            ? "price_1R8t15KHseo214m6hz25lGvn"
            : "price_1R8tnGKHseo214m6mPQ8DZMr",
        name: "Plus",
        price: 1690,
        frequency: "annual",
        type: "subscription",
        features: [
          {
            name: "10M Yearly",
          },
        ],
      },
      {
        priceId:
          process.env.NODE_ENV === "development"
            ? "price_1R8t26KHseo214m6JIRgnUpd"
            : "price_1R8tnfKHseo214m6iOTj0W6n",
        name: "Plus",
        price: 199,
        frequency: "monthly",
        type: "subscription",
        features: [
          {
            name: "10M+ Monthly",
          },
        ],
      },
      {
        priceId:
          process.env.NODE_ENV === "development"
            ? "price_1R8t2LKHseo214m6BzF9Izeu"
            : "price_1R8tnfKHseo214m6efY50iOy",
        name: "Plus",
        price: 1990,
        frequency: "annual",
        type: "subscription",
        features: [
          {
            name: "10M+ Yearly",
          },
        ],
      },
    ],
  },
  emails: {
    supportEmail: "<EMAIL>",
  },
} as ConfigProps;

export default config;
