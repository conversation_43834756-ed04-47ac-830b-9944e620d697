import Features from "@/components/LandingPage/Features";
import Hero from "@/components/LandingPage/Hero";
import Header from "@/components/LandingPage/Header";
import { getSEOTags } from "@/lib/seo";
import FeaturesListicle from "@/components/LandingPage/FeaturesListicle";
import Pricing from "@/components/LandingPage/Pricing";
import PricingSection from "@/components/LandingPage/PricingSection";
import FAQ from "@/components/LandingPage/FAQ";
import CTA from "@/components/LandingPage/CTA";
import Footer from "@/components/LandingPage/Footer";
import ProductHuntBanner from "@/components/LandingPage/ProductHuntBanner";
export const metadata = getSEOTags({
  title: "Scale your business with data-backed decisions",
  description:
    "The ultimate web analytics tool for SaaS and online businesses. Track visitors, analyze behavior, and make data-driven decisions.",
  canonicalUrlRelative: "/",
});

export default function LandingPage() {
  return (
    <div className="flex min-h-screen flex-col bg-customGray text-gray-100">
      <ProductHuntBanner />
      <Header />
      <main className="flex-1">
        <Hero />
        <FeaturesListicle />
        <PricingSection />
        <FAQ />
        <CTA />
      </main>
      <Footer />
    </div>
  );
}
