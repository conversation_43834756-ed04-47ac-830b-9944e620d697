import React from "react";
import config from "@/config";
import {
  FaWandMagicSparkles,
  FaThumbsUp,
  FaFlag,
  FaChartPie,
  FaHeadset,
  FaCodeBranch,
} from "react-icons/fa6";
import ScrollButton from "./ScrollButton";

const WithWithout = () => {
  const painPoints = [
    "Spending hours manually entering job details into spreadsheets",
    "Missing important details when a recruiter calls unexpectedly",
    "Struggling to set and track meaningful job search goals",
    "Wasting time on job boards that don't yield results",
    "Feeling isolated and unsupported during the job search process",
    "Falling behind on job search tasks due to lack of structure",
  ];

  const solutions = [
    "AI-powered extraction organizes job details instantly",
    "Access your entire job search history anytime, anywhere",
    "Set achievable targets with progress tracking and reminders",
    "Identify best-performing job boards with comprehensive analytics",
    "24/7 customer support to assist you throughout your journey",
    "Regular updates and new features to keep your search on track",
  ];

  const icons = [
    <FaWandMagicSparkles key="globe" />,
    <FaThumbsUp key="thumbsUp" />,
    <FaFlag key="flag" />,
    <FaChartPie key="graphicCircle" />,
    <FaHeadset key="hash" />,
    <FaCodeBranch key="branch" />,
  ];

  return (
    <section className="bg-base-200 py-16 md:py-24">
      <div className="max-w-5xl mx-auto px-8">
        <h2 className="text-center font-extrabold text-3xl md:text-5xl tracking-tight mb-12 md:mb-20">
          Transform Your Job Search with {config.appName}
        </h2>

        <div className="grid md:grid-cols-2 gap-8 md:gap-12">
          <div className="bg-base-100 p-8 rounded-lg shadow-lg">
            <h3 className="font-bold text-xl mb-6 text-error">
              Job hunting without {config.appName}
            </h3>

            <ul className="space-y-4">
              {painPoints.map((item, index) => (
                <li key={index} className="flex items-start gap-3">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                    className="w-5 h-5 text-error flex-shrink-0 mt-1"
                  >
                    <path d="M6.28 5.22a.75.75 0 00-1.06 1.06L8.94 10l-3.72 3.72a.75.75 0 101.06 1.06L10 11.06l3.72 3.72a.75.75 0 101.06-1.06L11.06 10l3.72-3.72a.75.75 0 00-1.06-1.06L10 8.94 6.28 5.22z" />
                  </svg>
                  <span>{item}</span>
                </li>
              ))}
            </ul>
          </div>

          <div className="bg-base-100 p-8 rounded-lg shadow-lg">
            <h3 className="font-bold text-xl mb-6 text-success">
              Job hunting with {config.appName}
            </h3>

            <ul className="space-y-4">
              {solutions.map((item, index) => (
                <li key={index} className="flex items-start gap-3">
                  <span className="text-success flex-shrink-0 mt-1">
                    {icons[index]}
                  </span>
                  <span>{item}</span>
                </li>
              ))}
            </ul>
          </div>
        </div>

        <div className="mt-12 text-center">
          <p className="text-lg mb-6">
            Ready to revolutionize your job search experience?
          </p>
          <ScrollButton
            title="Get Started →"
            type="secondary"
            eventName="get_started_withwithout"
            eventDesc="User clicked on get started button on withwithout section"
          />
        </div>
      </div>
    </section>
  );
};

export default WithWithout;
