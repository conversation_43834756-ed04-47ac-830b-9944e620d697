"use client";

import { useState, useEffect, useRef } from "react";
import { useRouter, useParams } from "next/navigation";
import Link from "next/link";
import Image from "next/image";
import WebsiteIcon from "@/components/WebsiteIcon";
import { UserButton } from "@clerk/nextjs";
import { Website } from "@/lib/types";
import GeneralSettingsTab from "@/components/GeneralSettingsTab";
import FilterSettingsTab from "@/components/FilterSettingsTab";
import ImportSettingsTab from "@/components/ImportSettingsTab";
import RevenueSettingsTab from "@/components/RevenueSettingsTab";
import { FaCog, FaFilter, FaFileImport, FaDollarSign } from "react-icons/fa";
import UserDropdown from "@/components/UserDropdown";

export default function SettingsPage() {
  const { id: websiteId } = useParams() as { id: string };
  const [website, setWebsite] = useState<Website | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<"general" | "import" | "filters" | "revenue">(
    "general"
  );
  const router = useRouter();
  const [excludeAuthVisits, setExcludeAuthVisits] = useState(false);
  const [privateRoutes, setPrivateRoutes] = useState<string[]>([]);

  // Function to handle website updates
  const handleWebsiteUpdate = (updatedWebsite: Website) => {
    setWebsite(updatedWebsite);
  };

  // Fetch website data
  useEffect(() => {
    async function fetchWebsite() {
      try {
        setIsLoading(true);
        const response = await fetch(`/api/websites/${websiteId}`);

        if (!response.ok) {
          if (response.status === 404) {
            router.push("/dashboard");
            return;
          }
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        setWebsite(data);
        setExcludeAuthVisits(data.excludeAuthVisits || false);
        setPrivateRoutes(data.privateRoutes || []);
      } catch (error) {
        console.error("Failed to fetch website:", error);
      } finally {
        setIsLoading(false);
      }
    }

    fetchWebsite();
  }, [websiteId, router]);

  if (isLoading) {
    return (
      <div className="min-h-screen bg-[#1C1C1C] text-gray-100 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  if (!website) {
    return (
      <div className="min-h-screen bg-[#1C1C1C] text-gray-100 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">Website not found</h1>
          <Link href="/dashboard" className="text-blue-400 hover:underline">
            Return to Dashboard
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-[#1C1C1C] text-gray-100">
      <div className="container mx-auto px-4 py-8 max-w-full sm:max-w-6xl">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center gap-4">
            <Link
              href="/dashboard"
              className="text-2xl sm:text-3xl font-bold bg-gradient-to-r from-blue-500 to-blue-700 bg-clip-text text-transparent flex items-center gap-2"
            >
              <Image
                src="/icon.png"
                alt="VersaTailor logo"
                width={32}
                height={32}
                className="rounded-full"
              />
              <p className="text-white text-xl">Versatailor</p>
            </Link>
          </div>
          <div className="flex items-center gap-4">
            <UserDropdown />
          </div>
        </div>

        {/* Back to dashboard and title */}
        <div className="mb-8">
          <Link
            href={`/dashboard/${website.id}`}
            className="text-blue-400 hover:text-blue-300 flex items-center gap-1 mb-4"
          >
            <svg
              className="w-4 h-4"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M10 19l-7-7m0 0l7-7m-7 7h18"
              />
            </svg>
            Back to Dashboard
          </Link>
          <div className="flex items-center gap-3">
            <WebsiteIcon
              domain={website.domain}
              name={website.name}
              size={24}
            />
            <h1 className="text-2xl font-bold">{website.domain} Settings</h1>
          </div>
        </div>

        {/* Tabs */}
        <div className="mb-6 border-b border-gray-700">
          <div className="flex space-x-8">
            <button
              onClick={() => setActiveTab("general")}
              className={`py-2 px-1 font-medium text-sm border-b-2 transition-colors duration-300 flex items-center gap-2 ${
                activeTab === "general"
                  ? "border-blue-500 text-white"
                  : "border-transparent text-gray-400 hover:text-gray-300"
              }`}
            >
              <FaCog className="h-4 w-4" />
              General
            </button>
            <button
              onClick={() => setActiveTab("filters")}
              className={`py-2 px-1 font-medium text-sm border-b-2 transition-colors duration-300 flex items-center gap-2 ${
                activeTab === "filters"
                  ? "border-blue-500 text-white"
                  : "border-transparent text-gray-400 hover:text-gray-300"
              }`}
            >
              <FaFilter className="h-4 w-4" />
              Filter
            </button>
            <button
              onClick={() => setActiveTab("import")}
              className={`py-2 px-1 font-medium text-sm border-b-2 transition-colors duration-300 flex items-center gap-2 ${
                activeTab === "import"
                  ? "border-blue-500 text-white"
                  : "border-transparent text-gray-400 hover:text-gray-300"
              }`}
            >
              <FaFileImport className="h-4 w-4" />
              Import
            </button>
            <button
              onClick={() => setActiveTab("revenue")}
              className={`py-2 px-1 font-medium text-sm border-b-2 transition-colors duration-300 flex items-center gap-2 ${
                activeTab === "revenue"
                  ? "border-blue-500 text-white"
                  : "border-transparent text-gray-400 hover:text-gray-300"
              }`}
            >
              <FaDollarSign className="h-4 w-4" />
              Revenue
            </button>
          </div>
        </div>

        {/* Tab content */}
        <div className="bg-[#2A2A2A] rounded-lg border border-gray-700/50 overflow-hidden">
          {activeTab === "general" && <GeneralSettingsTab website={website} />}
          {/* Filter Tab */}
          {activeTab === "filters" && (
            <FilterSettingsTab
              privateRoutes={privateRoutes}
              setPrivateRoutes={setPrivateRoutes}
              excludeAuthVisits={excludeAuthVisits}
              websiteId={websiteId}
              setExcludeAuthVisits={setExcludeAuthVisits}
            />
          )}
          {/* Import Tab */}
          {activeTab === "import" && <ImportSettingsTab website={website} />}
          {/* Revenue Tab */}
          {activeTab === "revenue" && <RevenueSettingsTab website={website} onUpdate={handleWebsiteUpdate} />}
        </div>
      </div>
    </div>
  );
}
