import { useCountryCode } from "@/hooks/useCountryCode";
import { useFilterState } from "@/hooks/useFilterState";
import { GroupedLocations } from "@/hooks/useGroupedData";
import { Location } from "@/lib/types";
import React, { Dispatch, SetStateAction } from "react";
import Image from "next/image";

type LocationSectionProps = {
  topLocations: Location[];
  groupedLocations: GroupedLocations;
  getMaxLocationVisitors: (view: "country" | "region" | "city") => number;
  setShowLocationModal: Dispatch<SetStateAction<boolean>>;
  locationView: "country" | "region" | "city";
  setLocationView: Dispatch<SetStateAction<"country" | "region" | "city">>;
};

function LocationSection({
  topLocations,
  groupedLocations,
  getMaxLocationVisitors,
  setShowLocationModal,
  locationView,
  setLocationView,
}: LocationSectionProps) {
  const { getCountryFlagUrl } = useCountryCode();
  const { handleFilterClick, activeFilters } = useFilterState();

  return (
    <div className="bg-[#2A2A2A] p-4 sm:p-6 rounded-lg">
      <div className="flex items-center justify-between mb-3">
        <h2 className="text-base sm:text-lg font-semibold text-white">
          Location
        </h2>
        <div className="flex rounded-lg p-1">
          <button
            onClick={() => setLocationView("country")}
            className={`px-2 py-1 rounded-md text-xs sm:text-sm transition-colors ${locationView === "country"
              ? "bg-blue-500 text-white"
              : "text-gray-400 hover:text-white"
              }`}
          >
            Country
          </button>
          <button
            onClick={() => setLocationView("region")}
            className={`px-2 py-1 rounded-md text-xs sm:text-sm transition-colors ${locationView === "region"
              ? "bg-blue-500 text-white"
              : "text-gray-400 hover:text-white"
              }`}
          >
            Region
          </button>
          <button
            onClick={() => setLocationView("city")}
            className={`px-2 py-1 rounded-md text-xs sm:text-sm transition-colors ${locationView === "city"
              ? "bg-blue-500 text-white"
              : "text-gray-400 hover:text-white"
              }`}
          >
            City
          </button>
        </div>
      </div>
      <div className="space-y-1 h-[250px] overflow-hidden relative">
        {locationView === "country" &&
          groupedLocations.country.map((location, i) => (
            <div
              key={i}
              className={`flex items-center justify-between p-1 sm:p-2 rounded transition-colors relative overflow-hidden group cursor-pointer text-xs sm:text-sm ${activeFilters.some(
                (filter) =>
                  filter.type === "location" &&
                  filter.value === location.country
              )
                ? "bg-blue-500/20"
                : ""
                }`}
              onClick={() =>
                handleFilterClick(
                  "location",
                  location.country,
                  location.country
                )
              }
            >
              <div
                className="absolute inset-0 bg-blue-500/40 transition-transform origin-left group-hover:bg-blue-500/20"
                style={{
                  transform: `scaleX(${location.visitors / getMaxLocationVisitors("country")
                    })`,
                }}
              />
              <span className="text-white flex items-center gap-2 relative">
                <span className="w-6 h-4 relative">
                  {getCountryFlagUrl(location.country) && (
                    <Image
                      src={getCountryFlagUrl(location.country)!}
                      alt={`${location.country} flag`}
                      fill
                      className="object-cover rounded-sm"
                    />
                  )}
                </span>
                <span className="truncate">{location.country}</span>
              </span>
              <span className="text-white ml-2 relative">
                {location.visitors}
              </span>
            </div>
          ))}
        {locationView === "region" &&
          groupedLocations.region.map((location, i) => (
            <div
              key={i}
              className={`flex items-center justify-between p-1 sm:p-2 rounded transition-colors relative overflow-hidden group cursor-pointer text-xs sm:text-sm ${activeFilters.some(
                (filter) =>
                  filter.type === "location" &&
                  typeof filter.value === "object" &&
                  "region" in filter.value &&
                  filter.value.region === location.region &&
                  filter.value.country === location.country
              )
                ? "bg-blue-500/20"
                : ""
                }`}
              onClick={() =>
                handleFilterClick(
                  "location",
                  { region: location.region, country: location.country },
                  `${location.region}, ${location.country}`
                )
              }
            >
              <div
                className="absolute inset-0 bg-blue-500/40 transition-transform origin-left group-hover:bg-blue-500/20"
                style={{
                  transform: `scaleX(${location.visitors / getMaxLocationVisitors("region")
                    })`,
                }}
              />
              <span className="text-white flex items-center gap-2 relative">
                <span className="w-6 h-4 relative">
                  {getCountryFlagUrl(location.country) && (
                    <Image
                      src={getCountryFlagUrl(location.country)!}
                      alt={`${location.country} flag`}
                      fill
                      className="object-cover rounded-sm"
                    />
                  )}
                </span>
                <span className="truncate">
                  {location.region}, {location.country}
                </span>
              </span>
              <span className="text-white ml-2 relative">
                {location.visitors}
              </span>
            </div>
          ))}
        {locationView === "city" &&
          groupedLocations.city.map((location, i) => (
            <div
              key={i}
              className={`flex items-center justify-between p-1 sm:p-2 rounded transition-colors relative overflow-hidden group cursor-pointer text-xs sm:text-sm ${activeFilters.some(
                (filter) =>
                  filter.type === "location" &&
                  typeof filter.value === "object" &&
                  "city" in filter.value &&
                  filter.value.city === location.city &&
                  filter.value.country === location.country
              )
                ? "bg-blue-500/20"
                : ""
                }`}
              onClick={() =>
                handleFilterClick(
                  "location",
                  { city: location.city ?? "Unknown", country: location.country },
                  `${location.city}, ${location.country}`
                )
              }
            >
              <div
                className="absolute inset-0 bg-blue-500/40 transition-transform origin-left group-hover:bg-blue-500/20"
                style={{
                  transform: `scaleX(${location.visitors / getMaxLocationVisitors("city")
                    })`,
                }}
              />
              <span className="text-white flex items-center gap-2 relative">
                <span className="w-6 h-4 relative">
                  {getCountryFlagUrl(location.country) && (
                    <Image
                      src={getCountryFlagUrl(location.country)!}
                      alt={`${location.country} flag`}
                      fill
                      className="object-cover rounded-sm"
                    />
                  )}
                </span>
                <span className="truncate">
                  {location.city ?? "Unknown"}, {location.country}
                </span>
              </span>
              <span className="text-white ml-2 relative">
                {location.visitors}
              </span>
            </div>
          ))}
        {((locationView === "country" && groupedLocations.country.length > 5) ||
          (locationView === "region" && groupedLocations.region.length > 5) ||
          (locationView === "city" && groupedLocations.city.length > 5)) && (
            <button
              onClick={() => setShowLocationModal(true)}
              className="absolute bottom-0 left-0 right-0 text-center py-2 text-blue-500 hover:text-blue-400 bg-gradient-to-t from-[#2A2A2A] via-[#2A2A2A] to-transparent"
            >
              Show more
            </button>
          )}
      </div>
    </div>
  );
}
export default LocationSection;
