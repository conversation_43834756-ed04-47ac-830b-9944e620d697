'use client';
import { useState, useEffect } from 'react';
import Image from 'next/image';

interface WebsiteIconProps {
  domain: string;
  name: string;
  size?: number;
  className?: string;
}

interface CachedIcon {
  url: string;
  timestamp: number;
  domain: string;
}

const CACHE_DURATION = 7 * 24 * 60 * 60 * 1000; // 7 days in milliseconds

export default function WebsiteIcon({ domain, name, size = 20, className = '' }: WebsiteIconProps) {
  const [currentSourceIndex, setCurrentSourceIndex] = useState(0);
  const [error, setError] = useState(false);
  const [iconUrl, setIconUrl] = useState<string | null>(null);

  // List of favicon sources to try in order
  const faviconSources = [
    // Try direct favicon.ico first
    `https://${domain}/favicon.ico`,
    // Then try Google's service
    `https://www.google.com/s2/favicons?domain=${domain}&sz=32`,
    // Finally try DuckDuckGo's service
    `https://icons.duckduckgo.com/ip3/${domain}.ico`
  ];

  useEffect(() => {
    const getCachedIcon = () => {
      try {
        const cached = localStorage.getItem(`favicon-${domain}`);
        if (cached) {
          const parsedCache: CachedIcon = JSON.parse(cached);
          const isExpired = Date.now() - parsedCache.timestamp > CACHE_DURATION;
          
          if (!isExpired && parsedCache.domain === domain) {
            return parsedCache.url;
          }
          // Remove expired cache
          localStorage.removeItem(`favicon-${domain}`);
        }
      } catch (error) {
        console.error('Error reading from cache:', error);
      }
      return null;
    };

    const cachedUrl = getCachedIcon();
    if (cachedUrl) {
      setIconUrl(cachedUrl);
    } else {
      setIconUrl(faviconSources[0]);
    }
  }, [domain]);

  const cacheIconUrl = (url: string) => {
    try {
      const cacheData: CachedIcon = {
        url,
        timestamp: Date.now(),
        domain
      };
      localStorage.setItem(`favicon-${domain}`, JSON.stringify(cacheData));
    } catch (error) {
      console.error('Error caching favicon:', error);
    }
  };

  if (error) {
    return (
      <div 
        className={`bg-blue-500 rounded flex items-center justify-center ${className}`}
        style={{ width: size, height: size }}
      >
        <span className="text-white font-medium" style={{ fontSize: Math.max(size * 0.5, 12) }}>
          {name.charAt(0).toUpperCase()}
        </span>
      </div>
    );
  }

  const handleError = () => {
    if (currentSourceIndex < faviconSources.length - 1) {
      // Try next source
      const nextUrl = faviconSources[currentSourceIndex + 1];
      setCurrentSourceIndex(currentSourceIndex + 1);
      setIconUrl(nextUrl);
    } else {
      // If all sources failed, show fallback
      setError(true);
    }
  };

  const handleLoad = () => {
    if (iconUrl) {
      cacheIconUrl(iconUrl);
    }
  };

  return (
    <Image
      src={iconUrl || faviconSources[0]}
      alt={`${name} favicon`}
      width={size}
      height={size}
      className={`rounded ${className}`}
      onError={handleError}
      onLoad={handleLoad}
      unoptimized
    />
  );
} 