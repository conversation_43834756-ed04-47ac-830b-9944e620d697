"use client"
import React from "react";
import { <PERSON><PERSON> } from "../ui/button";
import { ChevronRight } from "lucide-react";
import Link from "next/link";

function CTA() {
  function handleGoDashboard() {
    window?.versatailor("pressed_get_started_cta", {
      description: "User pressed the Get Started Button in the cta section.",
    });
  }
  return (
    <section className="w-full py-12 md:py-24 lg:py-32 flex items-center justify-center">
      <div className="container mx-auto px-4 md:px-6">
        <div className="flex flex-col items-center justify-center space-y-4 text-center max-w-3xl mx-auto">
          <div className="space-y-2">
            <h2 className="text-3xl font-bold tracking-tighter md:text-4xl/tight">
              Ready to understand your users better?
            </h2>
            <p className="max-w-[600px] text-gray-300 md:text-xl/relaxed lg:text-base/relaxed xl:text-xl/relaxed">
              Join businesses that use Versatailor to make
              data-driven decisions.
            </p>
          </div>
          <div className="flex flex-col gap-2 min-[400px]:flex-row justify-center">
            <Link href={"/dashboard"}>
              <Button size="lg" className="gap-1" onClick={handleGoDashboard}>
                Start your free trial <ChevronRight className="h-4 w-4" />
              </Button>
            </Link>
          </div>
          <p className="text-xs text-gray-400">
            No credit card required. 14-day free trial.
          </p>
        </div>
      </div>
    </section>
  );
}

export default CTA;
