import { NextRequest, NextResponse } from "next/server";
import { ipAddress } from "@vercel/functions";
import connect from "@/util/db";
import Event from "@/models/event";
import WebsiteModel from "@/models/website";
import User from "@/models/user";
import { Website } from "@/lib/types";
import {
  extractReferralParams,
  isBot,
  validateLocation,
} from "@/util/events_helpers";
import { useAuth } from "@clerk/nextjs";
import { auth } from "@clerk/nextjs/server";

// Helper function to handle CORS
function corsResponse(response: NextResponse) {
  // Set CORS headers
  response.headers.set("Access-Control-Allow-Origin", "*");
  response.headers.set(
    "Access-Control-Allow-Methods",
    "GET, POST, PUT, DELETE, OPTIONS"
  );
  response.headers.set(
    "Access-Control-Allow-Headers",
    "Content-Type, Authorization"
  );
  
  // Return the modified response
  return response;
}

// Helper function to extract referral parameters

// Add a more detailed OPTIONS handler
export async function OPTIONS(request: NextRequest) {
  const response = new NextResponse(null, { status: 200 });
  return corsResponse(response); 
}
export async function POST(request: NextRequest) {
  try {
    if (isBot(request)) {
      return corsResponse(
        NextResponse.json({ success: true, message: "Bot request ignored" })
      );
    }

    await connect();
    const body = await request.json();

    // Validate request body
    if (!body) {
      return corsResponse(
        NextResponse.json({ error: "Missing request body" }, { status: 400 })
      );
    }

    // Update required fields to include new mandatory fields
    const requiredFields = [
      "type",
      "websiteId",
      "domain",
      "href",
      "visitorId",
      "sessionId",
      "timestamp",
      "viewport",
    ];

    // Add email validation for signup events
    if (body.type === "signup") {
      if (!body.extraData?.email) {
        return corsResponse(
          NextResponse.json(
            { error: "Email is required for signup events" },
            { status: 400 }
          )
        );
      }
      // Basic email validation
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(body.extraData.email)) {
        return corsResponse(
          NextResponse.json(
            { error: "Invalid email format" },
            { status: 400 }
          )
        );
      }
    }

    const missingFields = requiredFields.filter((field) => !body[field]);

    if (missingFields.length > 0) {
      console.error("Missing required fields:", missingFields);
      return corsResponse(
        NextResponse.json(
          { error: `Missing required fields: ${missingFields.join(", ")}` },
          { status: 400 }
        )
      );
    }

    // Get website settings using the renamed model import
    const website = await WebsiteModel.findOne({ id: body.websiteId });
    if (!website) {
      return corsResponse(
        NextResponse.json({ error: "Website not found" }, { status: 404 })
      );
    }

    // Check if the website owner still has access
    const websiteOwner = await User.findOne({ clerkId: website.userId });
    if (!websiteOwner) {
      return corsResponse(
        NextResponse.json({ error: "Website owner not found" }, { status: 404 })
      );
    }

    // Check if the user's access has expired
    if (websiteOwner.accessUntil < new Date()) {
      return corsResponse(
        NextResponse.json({ 
          success: true, 
          message: "Website owner's access has expired" 
        })
      );
    }

    // Check if this visitor should be excluded
    if (website.excludeAuthVisits) {
      // First check if this visitor has been marked as authenticated before
      const existingAuthEvent = await Event.findOne({
        websiteId: body.websiteId,
        visitorId: body.visitorId,
        isAuthVisitor: true,
      });

      if (existingAuthEvent) {
        // This visitor has been identified as authenticated before, exclude their events
        return corsResponse(
          NextResponse.json({ success: true, message: "Auth visitor excluded" })
        );
      }

      // Check if current path matches any private routes
      if (website.privateRoutes?.length > 0) {
        const currentPath = new URL(body.href).pathname;
        
        // Helper function to match paths with wildcards
        const matchesWildcardPath = (pattern: string, path: string) => {
          // Normalize the pattern - ensure it starts with '/'
          const normalizedPattern = pattern.startsWith('/') ? pattern : '/' + pattern;
          
          // If pattern ends with "/**", it's a wildcard match
          if (normalizedPattern.endsWith('/**')) {
            const basePath = normalizedPattern.slice(0, -3); // Remove the "/**" part
            return path === basePath || path.startsWith(basePath + '/');
          }
          
          // Otherwise do exact match or direct child match
          return path === normalizedPattern || path.startsWith(normalizedPattern + '/');
        };
        
        const isPrivateRoute = website.privateRoutes.some((route: string) => 
          matchesWildcardPath(route, currentPath)
        );

        if (isPrivateRoute) {
          // Mark this visitor as authenticated for future reference
          body.isAuthVisitor = true;
        }
      }
    }

    // Validate location data - but don't reject if missing
    const location = validateLocation(body.location);

    // If location data is missing, try to backfill from previous events in the same session
    let finalLocation = location;
    const hasValidLocation = location && (location.country || location.city || location.region);
    
    if (!hasValidLocation) {
      console.log(`Attempting to backfill location for ${body.type} event. Current location:`, location);
      console.log(`VisitorId: ${body.visitorId}, SessionId: ${body.sessionId}`);
      
      try {
        // Look for the most recent event with the same visitorId and sessionId that has location data
        const previousEventWithLocation = await Event.findOne({
          visitorId: body.visitorId,
          sessionId: body.sessionId,
          $or: [
            { 'location.country': { $exists: true, $ne: null } },
            { 'location.city': { $exists: true, $ne: null } },
            { 'location.region': { $exists: true, $ne: null } }
          ]
        })
        .sort({ timestamp: -1 }) // Get the most recent one
        .lean() as any;

        console.log(`Found previous event with location:`, previousEventWithLocation?.location);

        if (previousEventWithLocation && 
            typeof previousEventWithLocation === 'object' && 
            'location' in previousEventWithLocation && 
            previousEventWithLocation.location) {
          finalLocation = previousEventWithLocation.location;
          console.log(`Successfully backfilled location data for ${body.type} event:`, finalLocation);
        } else {
          console.log(`No previous event with valid location found for visitor ${body.visitorId} in session ${body.sessionId}`);
        }
      } catch (error) {
        console.error("Error backfilling location data:", error);
        // Continue with original location (or lack thereof) if backfill fails
      }
    } else {
      console.log(`Event ${body.type} already has valid location data:`, location);
    }

    // Extract referral parameters from URL
    const referralParams = extractReferralParams(body.href);

    // Create new event with additional data
    const event = new Event({
      ...body,
      timestamp: new Date(body.timestamp),
      userAgent: request.headers.get("user-agent") || "unknown",
      referralParams,
      location: finalLocation, // Use the backfilled location if available
      ip:
        request.headers.get("x-forwarded-for")?.split(",")[0] ||
        ipAddress(request) ||
        null,
      // Ensure extraData is properly structured for signup events
      extraData: body.type === "signup" ? {
        ...body.extraData,
        eventName: "signup",
        email: body.extraData?.email
      } : body.extraData
    });

    await event.save();
    return corsResponse(NextResponse.json({ success: true }));
  } catch (error) {
    console.error("Error processing event:", error);
    return corsResponse(
      NextResponse.json(
        { error: "Internal server error" },
        { status: 500 }
      )
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    await connect();
    const { searchParams } = new URL(request.url);

    const query: any = {};

    // Add more filter options
    const websiteId = searchParams.get("websiteId");
    const domain = searchParams.get("domain");
    const startDate = searchParams.get("startDate");
    const endDate = searchParams.get("endDate");
    const from = searchParams.get("from");
    const to = searchParams.get("to");
    const type = searchParams.get("type");
    const country = searchParams.get("country");
    const city = searchParams.get("city");
    const visitorId = searchParams.get("visitorId");
    const endTime = searchParams.get("endTime");
    const countOnly = searchParams.get("countOnly") === "true";

    if (websiteId) query.websiteId = websiteId;
    if (domain) query.domain = domain;
    if (type) query.type = type;
    if (country) query["location.country"] = country;
    if (city) query["location.city"] = city;

    // Fetch website settings to check excludeAuthVisits
    let excludeAuth = false;
    if (websiteId) {
      // Use the model for query, cast the lean result to the Website type
      const website = await WebsiteModel.findOne({ id: websiteId }).lean<Website | null>(); 
      if (website && website.excludeAuthVisits) {
        excludeAuth = true;
      }
    }

    // Apply the filter if excludeAuthVisits is true
    if (excludeAuth) {
      query.isAuthVisitor = { $ne: true };
    }

    // Handle visitor journey query
    if (visitorId && endTime) {
      query.visitorId = visitorId;
      query.timestamp = { $lte: new Date(endTime) };
    }
    // Handle date range filtering - support both from/to and startDate/endDate
    else if (from || to || startDate || endDate) {
      query.timestamp = {};
      if (from) query.timestamp.$gte = new Date(from);
      else if (startDate) query.timestamp.$gte = new Date(startDate);

      if (to) query.timestamp.$lte = new Date(to);
      else if (endDate) query.timestamp.$lte = new Date(endDate);
    }

    // Determine if we should set caching headers
    // For historical data (not real-time), we can cache aggressively
    // For real-time data (e.g., last 5 minutes), don't cache at all
    let cacheMaxAge = 0; // Default to no caching
    
    // Check if this is a real-time polling request (last 5 minutes)
    const isRealtimePolling = from && !to && 
      new Date(from) > new Date(Date.now() - 5 * 60 * 1000);
      
    // Check if this is historical data (more than 24 hours old)
    const isHistoricalData = from && to && 
      new Date(to) < new Date(Date.now() - 24 * 60 * 60 * 1000);
    
    // Set caching strategy based on data type
    if (isRealtimePolling) {
      cacheMaxAge = 0; // No caching for real-time polling
    } else if (isHistoricalData) {
      cacheMaxAge = 60 * 60; // 1 hour for historical data
    } else {
      cacheMaxAge = 60; // 1 minute for recent data
    }
    
    // If countOnly is true, just return the count
    if (countOnly) {
      const count = await Event.countDocuments(query);
      const response = NextResponse.json({ count });
      
      // Add cache headers
      response.headers.set('Cache-Control', `public, max-age=${cacheMaxAge}, stale-while-revalidate=60`);
      return corsResponse(response);
    }

    // Apply sorting
    const sortField = searchParams.get("sort") || "timestamp";
    const sortOrder = searchParams.get("order") === "asc" ? 1 : -1;
    const sort: any = {};
    sort[sortField] = sortOrder;

    // Apply pagination
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "10000");
    const skip = (page - 1) * limit;

    // Execute the query
    const events = await Event.find(query)
      .sort(sort)
      .skip(skip)
      .limit(limit)
      .lean();

    // Return results with appropriate cache headers
    const response = NextResponse.json(events);
    response.headers.set('Cache-Control', `public, max-age=${cacheMaxAge}, stale-while-revalidate=60`);
    return corsResponse(response);
  } catch (error) {
    console.error("Error fetching events:", error);
    return NextResponse.json(
      { error: "Failed to fetch events" },
      { status: 500 }
    );
  }
}
