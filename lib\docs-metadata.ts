// Metadata configuration for documentation pages

export type DocsMetadata = {
  title: string;
  description: string;
  keywords?: string[];
};

const baseMetadata = {
  siteName: "VersaTailor Documentation",
  baseUrl: "https://versatailor.com",
  twitterHandle: "@versatailor",
};

export const docsMetadata: Record<string, DocsMetadata> = {
  "/docs": {
    title: "VersaTailor Documentation - Analytics & Event Tracking Guide",
    description:
      "Comprehensive documentation for VersaTailor analytics platform. Learn how to set up your account, track events, and customize your analytics dashboard.",
    keywords: [
      "analytics",
      "documentation",
      "event tracking",
      "web analytics",
      "versatailor",
    ],
  },
  "/docs/set-up-your-account": {
    title: "Set Up Your Account - VersaTailor Documentation",
    description:
      "Learn how to set up and configure your VersaTailor account. Step-by-step guide to getting started with web analytics.",
    keywords: [
      "account setup",
      "configuration",
      "getting started",
      "versatailor setup",
    ],
  },
  "/docs/import-your-data": {
    title: "Import Your Data - VersaTailor Documentation",
    description:
      "Guide to importing your existing analytics data into VersaTailor. Learn about data migration and integration options.",
    keywords: [
      "data import",
      "migration",
      "analytics data",
      "data integration",
    ],
  },
  "/docs/general-settings": {
    title: "General Settings - VersaTailor Documentation",
    description:
      "Configure general settings for your VersaTailor analytics. Learn about customization options and preferences.",
    keywords: ["settings", "configuration", "preferences", "customization"],
  },
  "/docs/filter-settings": {
    title: "Filter Settings - VersaTailor Documentation",
    description:
      "Learn how to set up and manage filters in VersaTailor. Guide to customizing your analytics data views.",
    keywords: [
      "filters",
      "data filtering",
      "analytics filters",
      "customization",
    ],
  },
  "/docs/settings": {
    title: "Website Settings Documentation - VersaTailor",
    description:
      "Learn how to configure and manage your website analytics settings in VersaTailor. Comprehensive guide to customization options.",
    keywords: [
      "website settings",
      "configuration",
      "analytics settings",
      "customization",
    ],
  },
  "/docs/track-events": {
    title: "Track Events - VersaTailor Documentation",
    description:
      "Comprehensive guide to tracking events with VersaTailor. Learn how to implement custom event tracking and analytics.",
    keywords: [
      "event tracking",
      "custom events",
      "analytics events",
      "tracking implementation",
    ],
  },
  "/settings": {
    title: "Settings - VersaTailor Documentation",
    description:
      "Learn how to configure and manage your VersaTailor settings. Comprehensive guide to customization options.",
    keywords: ["settings", "configuration", "preferences", "customization"],
  },
};

export function getMetadata(path: string): DocsMetadata & typeof baseMetadata {
  const metadata = docsMetadata[path] || {
    title: "VersaTailor Documentation",
    description: "Documentation for VersaTailor analytics platform",
    keywords: ["versatailor", "documentation", "analytics"],
  };

  return {
    ...metadata,
    ...baseMetadata,
  };
}
