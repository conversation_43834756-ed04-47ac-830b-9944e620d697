import Stripe from 'stripe';
import crypto from 'crypto';

// Required permissions for restricted Stripe API keys
const REQUIRED_PERMISSIONS = [
  'rak_charge_read',
  'rak_subscription_read', 
  'rak_customer_read',
  'rak_bucket_connect_read',
  'rak_payment_intent_read',
  'rak_checkout_session_read',
  'rak_webhook_write'
];

// Encryption utilities for API keys
const ENCRYPTION_KEY = process.env.STRIPE_API_KEY_ENCRYPTION_KEY;
const ALGORITHM = 'aes-256-gcm';

if (!ENCRYPTION_KEY) {
  throw new Error('STRIPE_API_KEY_ENCRYPTION_KEY environment variable is required');
}

/**
 * Encrypt a Stripe API key for secure storage
 */
export function encryptApiKey(apiKey: string): string {
  const iv = crypto.randomBytes(16);
  const key = Buffer.from(ENCRYPTION_KEY!.slice(0, 32)); // Ensure 32 bytes for AES-256
  const cipher = crypto.createCipheriv('aes-256-cbc', key, iv);
  
  let encrypted = cipher.update(apiKey, 'utf8', 'hex');
  encrypted += cipher.final('hex');
  
  // Return iv:encrypted
  return iv.toString('hex') + ':' + encrypted;
}

/**
 * Decrypt a Stripe API key from storage
 */
export function decryptApiKey(encryptedData: string): string {
  const parts = encryptedData.split(':');
  if (parts.length !== 2) {
    throw new Error('Invalid encrypted API key format');
  }
  
  const iv = Buffer.from(parts[0], 'hex');
  const encrypted = parts[1];
  const key = Buffer.from(ENCRYPTION_KEY!.slice(0, 32)); // Ensure 32 bytes for AES-256
  
  const decipher = crypto.createDecipheriv('aes-256-cbc', key, iv);
  
  let decrypted = decipher.update(encrypted, 'hex', 'utf8');
  decrypted += decipher.final('utf8');
  
  return decrypted;
}

/**
 * Create a Stripe client with user's API key
 */
export function createStripeClient(apiKey: string): Stripe {
  return new Stripe(apiKey, {
    apiVersion: '2025-02-24.acacia',
    typescript: true,
  });
}

/**
 * Validate Stripe API key and check permissions
 */
export async function validateStripeApiKey(apiKey: string): Promise<{
  isValid: boolean;
  hasRequiredPermissions: boolean;
  error?: string;
  accountId?: string;
}> {
  try {
    console.log("=== validateStripeApiKey called ===");
    console.log("API key starts with rk_:", apiKey.startsWith('rk_'));
    console.log("API key length:", apiKey.length);
    
    const stripe = createStripeClient(apiKey);
    
    // For restricted keys, we'll use a simpler validation method
    // that doesn't require account access permissions
    const isRestrictedKey = apiKey.startsWith('rk_');
    
    if (!isRestrictedKey) {
      console.log("Not a restricted key, returning error");
      return {
        isValid: false,
        hasRequiredPermissions: false,
        error: 'Please provide a restricted API key (starts with rk_)'
      };
    }
    
    console.log("Testing webhook endpoints list...");
    // Test the API key by making a simple request that doesn't require special permissions
    // We'll try to list webhook endpoints (which requires rak_webhook_read permission)
    try {
      const result = await stripe.webhookEndpoints.list({ limit: 1 });
      console.log("Webhook endpoints list successful, count:", result.data.length);
    } catch (error: any) {
      console.log("Webhook endpoints list failed:", error.type, error.code, error.message);
      // If we can't list webhooks, the key might not have webhook permissions
      if (error.type === 'invalid_request_error' && error.code === 'api_key_expired') {
        console.log("API key expired");
        return {
          isValid: false,
          hasRequiredPermissions: false,
          error: 'API key has expired'
        };
      }
      if (error.type === 'invalid_request_error' && error.message?.includes('does not have access')) {
        console.log("API key lacks permissions");
        return {
          isValid: false,
          hasRequiredPermissions: false,
          error: 'API key does not have required webhook permissions. Please ensure your restricted key includes webhook access.'
        };
      }
      // For other errors, we'll assume the key is valid but might have limited permissions
      console.log("Other error, assuming valid:", error.message);
    }
    
    console.log("Validation successful");
    return {
      isValid: true,
      hasRequiredPermissions: true,
      accountId: undefined // We don't need account ID for webhook setup
    };
    
  } catch (error: any) {
    console.log("Validation catch block:", error.type, error.message);
    // Handle authentication errors
    if (error.type === 'authentication_error') {
      console.log("Authentication error");
      return {
        isValid: false,
        hasRequiredPermissions: false,
        error: 'Invalid API key'
      };
    }
    
    console.log("Other error in validation");
    return {
      isValid: false,
      hasRequiredPermissions: false,
      error: error.message || 'Invalid API key'
    };
  }
}

/**
 * Create a webhook endpoint on Stripe
 */
export async function createWebhookEndpoint(
  apiKey: string, 
  websiteId: string,
  baseUrl: string = 'https://versatailor.com'
): Promise<{
  success: boolean;
  webhookEndpoint?: Stripe.WebhookEndpoint;
  error?: string;
}> {
  try {
    console.log("=== createWebhookEndpoint called ===");
    console.log("Base URL:", baseUrl);
    console.log("Website ID:", websiteId);
    console.log("Webhook URL will be:", `${baseUrl}/api/webhooks/revenue/${websiteId}`);
    
    const stripe = createStripeClient(apiKey);
    
    console.log("Creating webhook endpoint...");
    const webhookEndpoint = await stripe.webhookEndpoints.create({
      url: `${baseUrl}/api/webhooks/revenue/${websiteId}`,
      enabled_events: [
        'checkout.session.completed',
        'invoice.payment_succeeded',
      ],
      description: `VersaTailor Revenue Attribution - ${websiteId}`,
    });
    
    console.log("Webhook endpoint created successfully:", webhookEndpoint.id);
    return {
      success: true,
      webhookEndpoint
    };
    
  } catch (error: any) {
    console.log("createWebhookEndpoint error:", error.type, error.code, error.message);
    return {
      success: false,
      error: error.message || 'Failed to create webhook endpoint'
    };
  }
}

/**
 * Delete a webhook endpoint from Stripe
 */
export async function deleteWebhookEndpoint(
  apiKey: string,
  webhookEndpointId: string
): Promise<{
  success: boolean;
  error?: string;
}> {
  try {
    const stripe = createStripeClient(apiKey);
    
    await stripe.webhookEndpoints.del(webhookEndpointId);
    
    return {
      success: true
    };
    
  } catch (error: any) {
    return {
      success: false,
      error: error.message || 'Failed to delete webhook endpoint'
    };
  }
}

/**
 * Test webhook connection
 */
export async function testWebhookConnection(
  apiKey: string,
  webhookEndpointId: string
): Promise<{
  isActive: boolean;
  error?: string;
}> {
  try {
    const stripe = createStripeClient(apiKey);
    
    const webhook = await stripe.webhookEndpoints.retrieve(webhookEndpointId);
    
    return {
      isActive: webhook.status === 'enabled'
    };
    
  } catch (error: any) {
    return {
      isActive: false,
      error: error.message || 'Failed to test webhook connection'
    };
  }
} 